#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للنظام
Main Application Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.auth_manager import auth_manager
from config.config import APP_CONFIG, UI_CONFIG, COLORS, FONTS, RTL_CONFIG, STYLE_CONFIG
from src.ui_styles import RTLStyleManager, get_style_manager

class MainWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.style_manager = get_style_manager()
        self.setup_window()
        self.configure_rtl_support()
        self.create_menu()
        self.create_main_layout()
        self.create_status_bar()
        self.update_user_info()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title(APP_CONFIG['window_title'])

        # تعيين النافذة في وضع ملء الشاشة
        self.root.state('zoomed')  # ملء الشاشة في Windows

        # الحصول على أبعاد الشاشة
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # تعيين الحد الأدنى لحجم النافذة
        self.root.minsize(UI_CONFIG['min_width'], UI_CONFIG['min_height'])
        self.root.configure(bg=COLORS['background'])

        # تعيين الأيقونة
        try:
            if os.path.exists(APP_CONFIG['icon_path']):
                self.root.iconbitmap(APP_CONFIG['icon_path'])
        except:
            pass

        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # ربط مفاتيح الاختصار
        self.setup_keyboard_shortcuts()

        # متغير لتتبع حالة ملء الشاشة
        self.is_fullscreen = True

    def configure_rtl_support(self):
        """تكوين دعم RTL للنافذة الرئيسية"""
        # تكوين اتجاه النافذة
        self.root.option_add('*TCombobox*Listbox.selectBackground', COLORS['primary'])
        self.root.option_add('*TCombobox*Listbox.selectForeground', COLORS['text_white'])

        # تكوين الخطوط الافتراضية
        default_font = self.style_manager.get_font('arabic')
        self.root.option_add('*Font', default_font)

        # تكوين اتجاه النص
        self.root.option_add('*Text.justify', 'right')
        self.root.option_add('*Entry.justify', 'right')

        # تكوين ألوان النظام
        self.root.configure(bg=COLORS['background'])

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        # F11 - تبديل ملء الشاشة
        self.root.bind('<F11>', self.toggle_fullscreen)

        # F5 - تحديث لوحة المعلومات
        self.root.bind('<F5>', self.refresh_dashboard)

        # Ctrl+N - شحنة جديدة
        self.root.bind('<Control-n>', lambda e: self.new_shipment())

        # Ctrl+M - إدارة الموردين
        self.root.bind('<Control-m>', lambda e: self.open_suppliers_window())

        # Ctrl+I - إدارة الأصناف
        self.root.bind('<Control-i>', lambda e: self.open_items_window())

        # Ctrl+R - التقارير
        self.root.bind('<Control-r>', lambda e: self.open_reports_window())

        # Ctrl+S - الإعدادات
        self.root.bind('<Control-s>', lambda e: self.open_settings_window())

        # Ctrl+B - نسخة احتياطية
        self.root.bind('<Control-b>', lambda e: self.backup_database())

        # Ctrl+Q - خروج
        self.root.bind('<Control-q>', lambda e: self.on_closing())

        # Alt+F4 - خروج
        self.root.bind('<Alt-F4>', lambda e: self.on_closing())

    def toggle_fullscreen(self, event=None):
        """تبديل وضع ملء الشاشة"""
        self.is_fullscreen = not self.is_fullscreen
        if self.is_fullscreen:
            self.root.state('zoomed')
        else:
            self.root.state('normal')
            self.root.geometry(f"{UI_CONFIG['window_width']}x{UI_CONFIG['window_height']}")
            self.center_window()

    def refresh_dashboard(self, event=None):
        """تحديث لوحة المعلومات"""
        self.create_dashboard()
        self.update_statistics()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_menu(self):
        """إنشاء شريط القوائم مع دعم RTL"""
        menubar = tk.Menu(
            self.root,
            font=self.style_manager.get_font('menu'),
            bg=COLORS['surface'],
            fg=COLORS['text_primary'],
            activebackground=COLORS['primary'],
            activeforeground=COLORS['text_white'],
            relief='flat',
            bd=0
        )
        self.root.config(menu=menubar)

        # قائمة الملف
        file_menu = tk.Menu(
            menubar,
            tearoff=0,
            font=self.style_manager.get_font('menu'),
            bg=COLORS['surface'],
            fg=COLORS['text_primary'],
            activebackground=COLORS['primary'],
            activeforeground=COLORS['text_white'],
            relief='flat',
            bd=1
        )
        menubar.add_cascade(label="📁 ملف", menu=file_menu)
        file_menu.add_command(label="💾 نسخة احتياطية", command=self.backup_database, accelerator="Ctrl+B")
        file_menu.add_command(label="📥 استعادة", command=self.restore_database)
        file_menu.add_separator()
        file_menu.add_command(label="🔄 تحديث لوحة المعلومات", command=self.refresh_dashboard, accelerator="F5")
        file_menu.add_separator()
        file_menu.add_command(label="🚪 خروج", command=self.on_closing, accelerator="Ctrl+Q")
        
        # قائمة الشحنات
        shipments_menu = tk.Menu(
            menubar,
            tearoff=0,
            font=self.style_manager.get_font('menu'),
            bg=COLORS['surface'],
            fg=COLORS['text_primary'],
            activebackground=COLORS['primary'],
            activeforeground=COLORS['text_white']
        )
        menubar.add_cascade(label="🚢 الشحنات", menu=shipments_menu)
        shipments_menu.add_command(label="📋 إدارة الشحنات", command=self.open_shipments_window)
        shipments_menu.add_command(label="➕ شحنة جديدة", command=self.new_shipment, accelerator="Ctrl+N")
        shipments_menu.add_command(label="🔍 تتبع الشحنات", command=self.track_shipments)
        
        # قائمة الموردين
        suppliers_menu = tk.Menu(
            menubar, tearoff=0, font=self.style_manager.get_font('menu'),
            bg=COLORS['surface'], fg=COLORS['text_primary'],
            activebackground=COLORS['primary'], activeforeground=COLORS['text_white']
        )
        menubar.add_cascade(label="🏢 الموردين", menu=suppliers_menu)
        suppliers_menu.add_command(label="📋 إدارة الموردين", command=self.open_suppliers_window, accelerator="Ctrl+M")
        suppliers_menu.add_command(label="➕ مورد جديد", command=self.new_supplier)

        # قائمة المخزون
        inventory_menu = tk.Menu(
            menubar, tearoff=0, font=self.style_manager.get_font('menu'),
            bg=COLORS['surface'], fg=COLORS['text_primary'],
            activebackground=COLORS['primary'], activeforeground=COLORS['text_white']
        )
        menubar.add_cascade(label="📦 المخزون", menu=inventory_menu)
        inventory_menu.add_command(label="📋 إدارة الأصناف", command=self.open_items_window, accelerator="Ctrl+I")
        inventory_menu.add_command(label="➕ صنف جديد", command=self.new_item)
        inventory_menu.add_command(label="🏪 إدارة المخازن", command=self.open_warehouses_window)
        inventory_menu.add_command(label="📊 حركات المخزون", command=self.inventory_movements)
        
        # قائمة التقارير
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="التقارير", menu=reports_menu)
        reports_menu.add_command(label="نافذة التقارير", command=self.open_reports_window, accelerator="Ctrl+R")
        reports_menu.add_separator()
        reports_menu.add_command(label="تقرير الشحنات", command=self.shipments_report)
        reports_menu.add_command(label="تقرير الموردين", command=self.suppliers_report)
        reports_menu.add_command(label="تقرير المخزون", command=self.inventory_report)
        
        # قائمة الإعدادات
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="الإعدادات", menu=settings_menu)
        settings_menu.add_command(label="الإعدادات العامة", command=self.open_settings_window, accelerator="Ctrl+S")
        settings_menu.add_separator()
        settings_menu.add_command(label="إعدادات الشركة", command=self.company_settings)
        settings_menu.add_command(label="إدارة المستخدمين", command=self.manage_users)
        settings_menu.add_command(label="تغيير كلمة المرور", command=self.change_password)

        # قائمة العرض
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="عرض", menu=view_menu)
        view_menu.add_command(label="ملء الشاشة", command=self.toggle_fullscreen, accelerator="F11")
        view_menu.add_command(label="تحديث", command=self.refresh_dashboard, accelerator="F5")
        view_menu.add_separator()
        view_menu.add_command(label="إخفاء/إظهار الشريط الجانبي", command=self.toggle_sidebar)
        view_menu.add_command(label="إخفاء/إظهار شريط الحالة", command=self.toggle_status_bar)

        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="دليل المستخدم", command=self.user_guide)
        help_menu.add_command(label="حول البرنامج", command=self.about)
    
    def create_main_layout(self):
        """إنشاء التخطيط الرئيسي مع دعم RTL"""
        # الإطار الرئيسي
        self.main_frame = self.style_manager.create_styled_frame(
            self.root,
            style='default',
            bg=COLORS['background']
        )
        self.main_frame.pack(fill='both', expand=True, padx=STYLE_CONFIG['spacing']['sm'], pady=STYLE_CONFIG['spacing']['sm'])

        # الشريط العلوي المحسن مع تدرج لوني
        self.header_frame = self.style_manager.create_styled_frame(
            self.main_frame,
            style='header',
            height=120,
            relief='flat',
            bd=0
        )
        self.header_frame.pack(fill='x', pady=(0, STYLE_CONFIG['spacing']['sm']))
        self.header_frame.pack_propagate(False)
        
        # إطار العنوان والشعار مع تصميم RTL
        title_frame = self.style_manager.create_styled_frame(
            self.header_frame,
            style='header'
        )
        title_frame.pack(side='right', padx=STYLE_CONFIG['spacing']['xl'], pady=STYLE_CONFIG['spacing']['lg'])

        # عنوان التطبيق مع خط عربي محسن
        title_label = self.style_manager.create_styled_label(
            title_frame,
            text=f"🚢 {APP_CONFIG['name']}",
            style='title',
            font=self.style_manager.get_font('arabic_title', size=22, weight='bold'),
            bg=COLORS['header'],
            fg=COLORS['text_white']
        )
        title_label.pack(anchor='e')

        # وصف التطبيق
        subtitle_label = self.style_manager.create_styled_label(
            title_frame,
            text="نظام متكامل لإدارة ومتابعة الشحنات مع دعم RTL",
            style='subtitle',
            font=self.style_manager.get_font('arabic', size=11),
            bg=COLORS['header'],
            fg=COLORS['text_light']
        )
        subtitle_label.pack(anchor='e', pady=(STYLE_CONFIG['spacing']['xs'], 0))

        # إطار معلومات المستخدم والوقت مع تصميم RTL
        user_frame = self.style_manager.create_styled_frame(
            self.header_frame,
            style='header'
        )
        user_frame.pack(side='left', padx=STYLE_CONFIG['spacing']['xl'], pady=STYLE_CONFIG['spacing']['lg'])

        # بطاقة معلومات المستخدم
        user_card = self.style_manager.create_styled_frame(
            user_frame,
            bg=COLORS['primary_light'],
            relief='raised',
            bd=1
        )
        user_card.pack(fill='x', pady=STYLE_CONFIG['spacing']['xs'])

        # معلومات المستخدم
        self.user_info_label = self.style_manager.create_styled_label(
            user_card,
            text="",
            font=self.style_manager.get_font('arabic', size=12, weight='bold'),
            bg=COLORS['primary_light'],
            fg=COLORS['text_white'],
            anchor='w',
            justify='left'
        )
        self.user_info_label.pack(anchor='w', padx=STYLE_CONFIG['spacing']['sm'], pady=STYLE_CONFIG['spacing']['xs'])

        # الوقت والتاريخ في الشريط العلوي
        self.header_datetime_label = self.style_manager.create_styled_label(
            user_frame,
            text="",
            font=self.style_manager.get_font('arabic_numbers', size=10),
            bg=COLORS['header'],
            fg=COLORS['text_light'],
            anchor='w',
            justify='left'
        )
        self.header_datetime_label.pack(anchor='w', pady=(STYLE_CONFIG['spacing']['xs'], 0))
        
        # الإطار الأوسط
        self.content_frame = self.style_manager.create_styled_frame(
            self.main_frame,
            style='default'
        )
        self.content_frame.pack(fill='both', expand=True)

        # الشريط الجانبي المحسن مع تصميم RTL
        self.sidebar_frame = self.style_manager.create_styled_frame(
            self.content_frame,
            style='sidebar',
            width=320,
            relief='raised',
            bd=1
        )
        self.sidebar_frame.pack(side='right', fill='y', padx=(0, STYLE_CONFIG['spacing']['sm']))
        self.sidebar_frame.pack_propagate(False)

        self.create_sidebar(self.sidebar_frame)

        # المنطقة الرئيسية المحسنة
        self.main_content_frame = self.style_manager.create_styled_frame(
            self.content_frame,
            style='card',
            relief='raised',
            bd=1
        )
        self.main_content_frame.pack(side='left', fill='both', expand=True, padx=(0, STYLE_CONFIG['spacing']['sm']))
        
        self.create_dashboard()
    
    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي المحسن مع دعم RTL"""
        # عنوان الشريط الجانبي مع تصميم متقدم
        title_frame = self.style_manager.create_styled_frame(
            parent,
            style='sidebar',
            relief='flat'
        )
        title_frame.pack(fill='x', pady=(STYLE_CONFIG['spacing']['xl'], STYLE_CONFIG['spacing']['lg']))

        sidebar_title = self.style_manager.create_styled_label(
            title_frame,
            text="📋 القوائم الرئيسية",
            font=self.style_manager.get_font('arabic_header', size=16, weight='bold'),
            bg=COLORS['sidebar'],
            fg=COLORS['text_white'],
            anchor='center',
            justify='center'
        )
        sidebar_title.pack(pady=STYLE_CONFIG['spacing']['md'])

        # خط فاصل زخرفي
        separator = tk.Frame(
            title_frame,
            height=2,
            bg=COLORS['rtl_accent'],
            relief='flat'
        )
        separator.pack(fill='x', padx=STYLE_CONFIG['spacing']['xl'])

        # أزرار القوائم مع أيقونات نصية
        buttons = [
            ("🚢 إدارة الشحنات", self.open_shipments_window, "إدارة ومتابعة جميع الشحنات"),
            ("🏢 إدارة الموردين", self.open_suppliers_window, "إدارة بيانات الموردين"),
            ("📦 إدارة الأصناف", self.open_items_window, "إدارة الأصناف والمخزون"),
            ("🏪 إدارة المخازن", self.open_warehouses_window, "إدارة المخازن والمواقع"),
            ("📊 التقارير", self.open_reports_window, "عرض وتصدير التقارير"),
            ("⚙️ الإعدادات", self.open_settings_window, "إعدادات النظام والشركة")
        ]

        # تحديث قائمة الأزرار لتشمل الألوان
        buttons_updated = [
            ("🚢 إدارة الشحنات", self.open_shipments_window, "إدارة ومتابعة جميع الشحنات", "primary"),
            ("🏢 إدارة الموردين", self.open_suppliers_window, "إدارة بيانات الموردين", "success"),
            ("📦 إدارة الأصناف", self.open_items_window, "إدارة الأصناف والمخزون", "info"),
            ("🏪 إدارة المخازن", self.open_warehouses_window, "إدارة المخازن والمواقع", "warning"),
            ("📊 التقارير", self.open_reports_window, "عرض وتصدير التقارير", "secondary"),
            ("⚙️ الإعدادات", self.open_settings_window, "إعدادات النظام والشركة", "danger")
        ]

        for text, command, tooltip, style in buttons_updated:
            # إطار الزر مع تصميم متقدم
            btn_frame = self.style_manager.create_styled_frame(
                parent,
                style='sidebar'
            )
            btn_frame.pack(fill='x', padx=STYLE_CONFIG['spacing']['lg'], pady=STYLE_CONFIG['spacing']['sm'])

            # الزر مع تصميم RTL
            btn = self.style_manager.create_styled_button(
                btn_frame,
                text=text,
                command=command,
                style=style,
                font=self.style_manager.get_font('arabic_button', size=12),
                pady=STYLE_CONFIG['spacing']['md'],
                padx=STYLE_CONFIG['spacing']['lg'],
                anchor='e',  # محاذاة لليمين
                justify='right'
            )
            btn.pack(fill='x')

            # إضافة تلميح للزر
            self.create_tooltip(btn, tooltip)

        # إضافة مساحة فارغة
        tk.Frame(parent, bg=COLORS['sidebar'], height=20).pack()

        # معلومات سريعة
        info_frame = tk.LabelFrame(
            parent,
            text="معلومات سريعة",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size'], 'bold'),
            bg=COLORS['sidebar'],
            fg=COLORS['white'],
            bd=1,
            relief='solid'
        )
        info_frame.pack(fill='x', padx=15, pady=10)

        self.quick_info_label = tk.Label(
            info_frame,
            text="جاري التحميل...",
            font=(FONTS['arabic']['family'], 9),
            bg=COLORS['sidebar'],
            fg=COLORS['light'],
            justify='right',
            wraplength=200
        )
        self.quick_info_label.pack(pady=10, padx=10)

        # تحديث المعلومات السريعة
        self.update_quick_info()
    
    def create_dashboard(self):
        """إنشاء لوحة المعلومات الرئيسية المحسنة"""
        # مسح المحتوى السابق
        for widget in self.main_content_frame.winfo_children():
            widget.destroy()

        # إطار قابل للتمرير
        canvas = tk.Canvas(self.main_content_frame, bg=COLORS['white'])
        scrollbar = ttk.Scrollbar(self.main_content_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=COLORS['white'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # عنوان لوحة المعلومات
        header_frame = tk.Frame(scrollable_frame, bg=COLORS['white'])
        header_frame.pack(fill='x', pady=(20, 30))

        dashboard_title = tk.Label(
            header_frame,
            text="📊 لوحة المعلومات الرئيسية",
            font=(FONTS['title']['family'], FONTS['title']['size'], 'bold'),
            bg=COLORS['white'],
            fg=COLORS['primary']
        )
        dashboard_title.pack()

        welcome_subtitle = tk.Label(
            header_frame,
            text=f"مرحباً بك في نظام متابعة الشحنات - {datetime.now().strftime('%Y-%m-%d')}",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['secondary']
        )
        welcome_subtitle.pack(pady=(5, 0))
        
        # إطار الإحصائيات المحسن
        stats_frame = tk.Frame(scrollable_frame, bg=COLORS['white'])
        stats_frame.pack(fill='x', padx=30, pady=20)

        # عنوان الإحصائيات
        stats_title = tk.Label(
            stats_frame,
            text="📈 الإحصائيات السريعة",
            font=(FONTS['header']['family'], FONTS['header']['size'], 'bold'),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        stats_title.pack(pady=(0, 15))

        # إطار البطاقات
        cards_frame = tk.Frame(stats_frame, bg=COLORS['white'])
        cards_frame.pack(fill='x')

        # بطاقات الإحصائيات المحسنة
        stats = [
            ("🚢", "إجمالي الشحنات", "0", COLORS['primary']),
            ("✅", "الشحنات النشطة", "0", COLORS['success']),
            ("🏢", "الموردين", "0", COLORS['info']),
            ("📦", "الأصناف", "0", COLORS['warning'])
        ]

        for i, (icon, title, value, color) in enumerate(stats):
            card_frame = tk.Frame(cards_frame, bg=color, relief='raised', bd=3)
            card_frame.grid(row=0, column=i, padx=15, pady=10, sticky='ew')

            # أيقونة
            icon_label = tk.Label(
                card_frame,
                text=icon,
                font=(FONTS['title']['family'], 20),
                bg=color,
                fg=COLORS['white']
            )
            icon_label.pack(pady=(15, 5))

            # القيمة
            value_label = tk.Label(
                card_frame,
                text=value,
                font=(FONTS['title']['family'], 28, 'bold'),
                bg=color,
                fg=COLORS['white']
            )
            value_label.pack(pady=5)

            # العنوان
            title_label = tk.Label(
                card_frame,
                text=title,
                font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
                bg=color,
                fg=COLORS['white']
            )
            title_label.pack(pady=(0, 15))

        # تكوين الأعمدة
        for i in range(4):
            cards_frame.grid_columnconfigure(i, weight=1)
        
        # قسم الأنشطة الحديثة
        activities_frame = tk.Frame(scrollable_frame, bg=COLORS['white'])
        activities_frame.pack(fill='x', padx=30, pady=20)

        activities_title = tk.Label(
            activities_frame,
            text="🕒 الأنشطة الحديثة",
            font=(FONTS['header']['family'], FONTS['header']['size'], 'bold'),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        activities_title.pack(pady=(0, 15))

        # إطار قائمة الأنشطة
        activities_list_frame = tk.Frame(activities_frame, bg=COLORS['light'], relief='solid', bd=1)
        activities_list_frame.pack(fill='x', pady=10)

        self.activities_text = tk.Text(
            activities_list_frame,
            height=6,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['light'],
            fg=COLORS['dark'],
            relief='flat',
            wrap='word',
            state='disabled'
        )
        self.activities_text.pack(fill='x', padx=15, pady=15)

        # قسم الإجراءات السريعة
        actions_frame = tk.Frame(scrollable_frame, bg=COLORS['white'])
        actions_frame.pack(fill='x', padx=30, pady=20)

        actions_title = tk.Label(
            actions_frame,
            text="⚡ الإجراءات السريعة",
            font=(FONTS['header']['family'], FONTS['header']['size'], 'bold'),
            bg=COLORS['white'],
            fg=COLORS['dark']
        )
        actions_title.pack(pady=(0, 15))

        # أزرار الإجراءات السريعة
        quick_actions_frame = tk.Frame(actions_frame, bg=COLORS['white'])
        quick_actions_frame.pack(fill='x')

        quick_actions = [
            ("🚢 شحنة جديدة", self.new_shipment, COLORS['primary']),
            ("🏢 مورد جديد", self.new_supplier, COLORS['success']),
            ("📦 صنف جديد", self.new_item, COLORS['info']),
            ("📊 عرض التقارير", self.open_reports_window, COLORS['warning'])
        ]

        for i, (text, command, color) in enumerate(quick_actions):
            btn = tk.Button(
                quick_actions_frame,
                text=text,
                font=(FONTS['button']['family'], FONTS['button']['size']),
                bg=color,
                fg=COLORS['white'],
                relief='flat',
                cursor='hand2',
                command=command,
                pady=15,
                width=20
            )
            btn.grid(row=0, column=i, padx=10, pady=10, sticky='ew')

        # تكوين الأعمدة للإجراءات السريعة
        for i in range(4):
            quick_actions_frame.grid_columnconfigure(i, weight=1)

        # رسالة ترحيب
        welcome_frame = tk.Frame(scrollable_frame, bg=COLORS['light'], relief='solid', bd=1)
        welcome_frame.pack(fill='x', padx=30, pady=20)

        welcome_text = tk.Label(
            welcome_frame,
            text="💡 نصائح سريعة:\n\n• استخدم F11 للتبديل بين ملء الشاشة والوضع العادي\n• استخدم F5 لتحديث لوحة المعلومات\n• استخدم Ctrl+N لإنشاء شحنة جديدة\n• استخدم Ctrl+M لفتح إدارة الموردين",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['light'],
            fg=COLORS['dark'],
            justify='right'
        )
        welcome_text.pack(pady=20, padx=20)

        # تخطيط الكانفاس
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # ربط عجلة الماوس بالتمرير
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة المحسن"""
        self.status_bar = tk.Frame(self.root, bg=COLORS['dark'], height=30)
        self.status_bar.pack(side='bottom', fill='x')
        self.status_bar.pack_propagate(False)

        # تاريخ ووقت
        self.datetime_label = tk.Label(
            self.status_bar,
            text="",
            font=(FONTS['arabic']['family'], 9),
            bg=COLORS['dark'],
            fg=COLORS['white']
        )
        self.datetime_label.pack(side='left', padx=15, pady=5)

        # معلومات المستخدم في شريط الحالة
        self.status_user_label = tk.Label(
            self.status_bar,
            text="",
            font=(FONTS['arabic']['family'], 9),
            bg=COLORS['dark'],
            fg=COLORS['light']
        )
        self.status_user_label.pack(side='left', padx=15, pady=5)

        # حالة النظام
        self.status_label = tk.Label(
            self.status_bar,
            text="جاهز",
            font=(FONTS['arabic']['family'], 9),
            bg=COLORS['dark'],
            fg=COLORS['white']
        )
        self.status_label.pack(side='right', padx=15, pady=5)

        # زر تبديل ملء الشاشة
        self.fullscreen_button = tk.Button(
            self.status_bar,
            text="⛶",
            font=(FONTS['arabic']['family'], 8),
            bg=COLORS['dark'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.toggle_fullscreen,
            width=3
        )
        self.fullscreen_button.pack(side='right', padx=5, pady=2)

        # تحديث الوقت
        self.update_datetime()
    
    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        now = datetime.now()
        datetime_str = now.strftime("%Y-%m-%d %H:%M:%S")
        self.datetime_label.config(text=f"📅 {datetime_str}")

        # تحديث الوقت في الشريط العلوي أيضاً
        if hasattr(self, 'header_datetime_label'):
            header_time = now.strftime("%H:%M")
            self.header_datetime_label.config(text=f"🕐 {header_time}")

        self.root.after(1000, self.update_datetime)

    def toggle_sidebar(self):
        """إخفاء/إظهار الشريط الجانبي"""
        if self.sidebar_frame.winfo_viewable():
            self.sidebar_frame.pack_forget()
        else:
            self.sidebar_frame.pack(side='right', fill='y', padx=(0, 5))

    def toggle_status_bar(self):
        """إخفاء/إظهار شريط الحالة"""
        if self.status_bar.winfo_viewable():
            self.status_bar.pack_forget()
        else:
            self.status_bar.pack(side='bottom', fill='x')

    def update_quick_info(self):
        """تحديث المعلومات السريعة في الشريط الجانبي"""
        try:
            # هنا يمكن إضافة استعلامات قاعدة البيانات للحصول على معلومات سريعة
            info_text = "• النظام يعمل بشكل طبيعي\n• آخر تحديث: الآن\n• المستخدمين المتصلين: 1"
            self.quick_info_label.config(text=info_text)
        except:
            self.quick_info_label.config(text="• النظام جاهز")

    def update_statistics(self):
        """تحديث الإحصائيات في لوحة المعلومات"""
        try:
            # هنا يمكن إضافة استعلامات قاعدة البيانات للحصول على الإحصائيات الفعلية
            # مؤقتاً سنستخدم قيم تجريبية
            pass
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def update_recent_activities(self):
        """تحديث الأنشطة الحديثة"""
        try:
            if hasattr(self, 'activities_text'):
                self.activities_text.config(state='normal')
                self.activities_text.delete('1.0', tk.END)

                activities = [
                    f"• {datetime.now().strftime('%H:%M')} - تم تسجيل الدخول بنجاح",
                    f"• {datetime.now().strftime('%H:%M')} - تم تحديث لوحة المعلومات",
                    "• لا توجد أنشطة حديثة أخرى"
                ]

                for activity in activities:
                    self.activities_text.insert(tk.END, activity + "\n")

                self.activities_text.config(state='disabled')
        except Exception as e:
            print(f"خطأ في تحديث الأنشطة: {e}")

    def create_tooltip(self, widget, text):
        """إنشاء تلميح للعنصر"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")

            label = self.style_manager.create_styled_label(
                tooltip,
                text=text,
                style='muted',
                bg=COLORS['dark'],
                fg=COLORS['text_white'],
                font=self.style_manager.get_font('arabic_small'),
                relief='solid',
                bd=1
            )
            label.pack(padx=5, pady=3)

            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)

    def update_user_info(self):
        """تحديث معلومات المستخدم"""
        user = auth_manager.get_current_user()
        if user:
            role_name = auth_manager.get_user_role_name()
            user_text = f"👤 {user['full_name']}"
            role_text = f"🔑 {role_name}"

            self.user_info_label.config(text=user_text)

            # تحديث شريط الحالة
            if hasattr(self, 'status_user_label'):
                self.status_user_label.config(text=f"المستخدم: {user['username']} | الدور: {role_name}")

        # تحديث الأنشطة الحديثة
        self.update_recent_activities()
    
    # وظائف القوائم (ستتم إضافتها لاحقاً)
    def open_shipments_window(self):
        """فتح نافذة إدارة الشحنات المتقدمة والشاملة"""
        try:
            from src.simple_shipments_manager import SimpleShipmentsManager
            shipments_manager = SimpleShipmentsManager(self.root)
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة الشحنات: {str(e)}")
    
    def new_shipment(self):
        """إضافة شحنة جديدة"""
        try:
            from src.ultimate_shipment_form import UltimateShipmentForm
            form = UltimateShipmentForm(self.root, mode='add')
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نموذج الشحنة: {str(e)}")
    
    def track_shipments(self):
        messagebox.showinfo("معلومات", "سيتم فتح نافذة تتبع الشحنات قريباً")
    
    def open_suppliers_window(self):
        try:
            from src.suppliers_window import SuppliersWindow
            suppliers_window = SuppliersWindow(self.root)
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم فتح نافذة إدارة الموردين قريباً")
    
    def new_supplier(self):
        messagebox.showinfo("معلومات", "سيتم فتح نافذة مورد جديد قريباً")
    
    def open_items_window(self):
        try:
            from src.items_window import ItemsWindow
            items_window = ItemsWindow(self.root)
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم فتح نافذة إدارة الأصناف قريباً")
    
    def new_item(self):
        messagebox.showinfo("معلومات", "سيتم فتح نافذة صنف جديد قريباً")
    
    def open_warehouses_window(self):
        try:
            from src.warehouses_window import WarehousesWindow
            warehouses_window = WarehousesWindow(self.root)
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم فتح نافذة إدارة المخازن قريباً")
    
    def inventory_movements(self):
        messagebox.showinfo("معلومات", "سيتم فتح نافذة حركات المخزون قريباً")
    
    def open_reports_window(self):
        try:
            from src.reports_window import ReportsWindow
            reports_window = ReportsWindow(self.root)
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم فتح نافذة التقارير قريباً")
    
    def open_settings_window(self):
        try:
            from src.settings_window import SettingsWindow
            settings_window = SettingsWindow(self.root)
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم فتح نافذة الإعدادات قريباً")
    
    def shipments_report(self):
        messagebox.showinfo("معلومات", "سيتم إنشاء تقرير الشحنات قريباً")
    
    def suppliers_report(self):
        messagebox.showinfo("معلومات", "سيتم إنشاء تقرير الموردين قريباً")
    
    def inventory_report(self):
        messagebox.showinfo("معلومات", "سيتم إنشاء تقرير المخزون قريباً")
    
    def company_settings(self):
        messagebox.showinfo("معلومات", "سيتم فتح إعدادات الشركة قريباً")
    
    def manage_users(self):
        try:
            from src.users_window import UsersWindow
            users_window = UsersWindow(self.root)
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم فتح إدارة المستخدمين قريباً")
    
    def change_password(self):
        messagebox.showinfo("معلومات", "سيتم فتح نافذة تغيير كلمة المرور قريباً")
    
    def backup_database(self):
        try:
            from src.backup_manager import backup_manager
            backup_manager.create_backup()
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم إنشاء نسخة احتياطية قريباً")

    def restore_database(self):
        try:
            from src.backup_manager import backup_manager
            backup_manager.restore_backup()
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم استعادة النسخة الاحتياطية قريباً")
    
    def user_guide(self):
        messagebox.showinfo("معلومات", "سيتم فتح دليل المستخدم قريباً")
    
    def about(self):
        about_text = f"""
{APP_CONFIG['name']}
الإصدار: {APP_CONFIG['version']}

{APP_CONFIG['description']}

تم التطوير بواسطة: {APP_CONFIG['author']}
        """
        messagebox.showinfo("حول البرنامج", about_text)
    
    def on_closing(self):
        """عند إغلاق النافذة"""
        if messagebox.askokcancel("تأكيد الخروج", "هل تريد الخروج من البرنامج؟"):
            auth_manager.logout()
            self.root.destroy()
    
    def run(self):
        """تشغيل النافذة"""
        self.root.mainloop()

if __name__ == "__main__":
    # التحقق من تسجيل الدخول
    if not auth_manager.is_logged_in():
        messagebox.showerror("خطأ", "يجب تسجيل الدخول أولاً")
        sys.exit()
    
    # تشغيل النافذة الرئيسية
    main_window = MainWindow()
    main_window.run()
