#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نافذة البحث في الأصناف
"""

import tkinter as tk
from src.item_search_dialog import ItemSearchDialog

def test_search_dialog():
    """اختبار نافذة البحث"""
    print("🔍 اختبار نافذة البحث في الأصناف")
    
    # إنشاء نافذة رئيسية
    root = tk.Tk()
    root.title("اختبار البحث")
    root.geometry("400x300")
    
    def open_search():
        """فتح نافذة البحث"""
        try:
            # إنشاء بيانات وهمية للاختبار
            items_data = []
            
            # إنشاء نافذة البحث
            search_dialog = ItemSearchDialog(root, items_data, None)
            root.wait_window(search_dialog.root)
            
            print("✅ تم إغلاق نافذة البحث")
            
        except Exception as e:
            print(f"❌ خطأ في فتح نافذة البحث: {e}")
            import traceback
            traceback.print_exc()
    
    # زر لفتح البحث
    search_btn = tk.Button(
        root,
        text="🔍 فتح البحث",
        command=open_search,
        font=('Arial', 14),
        bg='#007bff',
        fg='white',
        padx=20,
        pady=10
    )
    search_btn.pack(expand=True)
    
    # تشغيل النافذة
    root.mainloop()

if __name__ == "__main__":
    test_search_dialog()
