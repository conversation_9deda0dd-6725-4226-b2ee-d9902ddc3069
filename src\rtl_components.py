#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكونات RTL للواجهة
RTL Components for UI
"""

import tkinter as tk
from tkinter import ttk

def create_rtl_frame(parent, **kwargs):
    """إنشاء إطار RTL"""
    return tk.Frame(parent, **kwargs)

def create_rtl_label(parent, text="", **kwargs):
    """إنشاء تسمية RTL"""
    return tk.Label(parent, text=text, **kwargs)

def create_rtl_entry(parent, **kwargs):
    """إنشاء حقل إدخال RTL"""
    return tk.Entry(parent, **kwargs)

def create_rtl_combobox(parent, **kwargs):
    """إنشاء قائمة منسدلة RTL"""
    return ttk.Combobox(parent, **kwargs)

def create_rtl_treeview(parent, **kwargs):
    """إنشاء جدول RTL"""
    return ttk.Treeview(parent, **kwargs)

def create_rtl_button(parent, text="", **kwargs):
    """إنشاء زر RTL"""
    return tk.Button(parent, text=text, **kwargs)

def create_rtl_text(parent, **kwargs):
    """إنشاء منطقة نص RTL"""
    return tk.Text(parent, **kwargs)

def create_rtl_scrollbar(parent, **kwargs):
    """إنشاء شريط تمرير RTL"""
    return ttk.Scrollbar(parent, **kwargs)

def create_rtl_checkbutton(parent, text="", **kwargs):
    """إنشاء مربع اختيار RTL"""
    return tk.Checkbutton(parent, text=text, **kwargs)

def create_rtl_radiobutton(parent, text="", **kwargs):
    """إنشاء زر راديو RTL"""
    return tk.Radiobutton(parent, text=text, **kwargs)

def create_rtl_listbox(parent, **kwargs):
    """إنشاء قائمة RTL"""
    return tk.Listbox(parent, **kwargs)

def create_rtl_scale(parent, **kwargs):
    """إنشاء شريط تمرير RTL"""
    return tk.Scale(parent, **kwargs)

def create_rtl_spinbox(parent, **kwargs):
    """إنشاء صندوق دوار RTL"""
    return tk.Spinbox(parent, **kwargs)

def create_rtl_progressbar(parent, **kwargs):
    """إنشاء شريط تقدم RTL"""
    return ttk.Progressbar(parent, **kwargs)

def create_rtl_separator(parent, **kwargs):
    """إنشاء فاصل RTL"""
    return ttk.Separator(parent, **kwargs)

def create_rtl_notebook(parent, **kwargs):
    """إنشاء دفتر ملاحظات RTL"""
    return ttk.Notebook(parent, **kwargs)

def create_rtl_panedwindow(parent, **kwargs):
    """إنشاء نافذة مقسمة RTL"""
    return ttk.PanedWindow(parent, **kwargs)

def create_rtl_menubutton(parent, text="", **kwargs):
    """إنشاء زر قائمة RTL"""
    return tk.Menubutton(parent, text=text, **kwargs)

def create_rtl_menu(parent, **kwargs):
    """إنشاء قائمة RTL"""
    return tk.Menu(parent, **kwargs)

def create_rtl_canvas(parent, **kwargs):
    """إنشاء لوحة رسم RTL"""
    return tk.Canvas(parent, **kwargs)

def create_rtl_toplevel(parent=None, **kwargs):
    """إنشاء نافذة فرعية RTL"""
    return tk.Toplevel(parent, **kwargs)
