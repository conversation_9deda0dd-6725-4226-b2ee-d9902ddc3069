# -*- mode: python ; coding: utf-8 -*-
"""
ملف تكوين PyInstaller لنظام متابعة الشحنات
PyInstaller spec file for Ship Management System
"""

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# مسار المشروع الحالي
project_path = os.path.dirname(os.path.abspath(SPEC))

# جمع جميع ملفات البيانات
datas = []

# إضافة ملفات قاعدة البيانات والإعدادات
datas += [
    (os.path.join(project_path, 'config'), 'config'),
    (os.path.join(project_path, 'database'), 'database'),
    (os.path.join(project_path, 'templates'), 'templates'),
    (os.path.join(project_path, 'docs'), 'docs'),
    (os.path.join(project_path, 'requirements.txt'), '.'),
    (os.path.join(project_path, 'README.md'), '.'),
    (os.path.join(project_path, 'دليل_المستخدم.md'), '.'),
    (os.path.join(project_path, 'تعليمات_التشغيل.txt'), '.'),
]

# إضافة ملفات الأيقونات إذا كانت موجودة
assets_path = os.path.join(project_path, 'assets')
if os.path.exists(assets_path):
    datas += [(assets_path, 'assets')]

# جمع المكتبات المخفية المطلوبة
hiddenimports = [
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'tkinter.simpledialog',
    'sqlite3',
    'pandas',
    'openpyxl',
    'xlsxwriter',
    'reportlab',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    'arabic_reshaper',
    'bidi',
    'bidi.algorithm',
    'tkcalendar',
    'matplotlib',
    'matplotlib.backends.backend_tkagg',
    'datetime',
    'hashlib',
    'logging',
    'json',
    'csv',
    'os',
    'sys',
    'threading',
    'queue',
    'collections',
    'itertools',
    'functools',
    'pathlib',
    'shutil',
    'tempfile',
    'zipfile',
    'io',
    're',
    'math',
    'decimal',
    'uuid',
    'base64',
    'urllib',
    'urllib.parse',
    'urllib.request',
    'http',
    'http.client',
    'email',
    'email.mime',
    'email.mime.text',
    'email.mime.multipart',
    'email.mime.base',
    'smtplib',
    'calendar',
    'locale',
    'gettext',
    'encodings',
    'encodings.utf_8',
    'encodings.cp1256',
    'encodings.ascii',
    'encodings.latin1',
]

# جمع جميع وحدات المشروع
project_modules = []
src_path = os.path.join(project_path, 'src')
if os.path.exists(src_path):
    for file in os.listdir(src_path):
        if file.endswith('.py') and not file.startswith('__'):
            module_name = f"src.{file[:-3]}"
            project_modules.append(module_name)

hiddenimports.extend(project_modules)

# إعدادات التحليل
a = Analysis(
    [os.path.join(project_path, 'main.py')],
    pathex=[project_path],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'test',
        'tests',
        'testing',
        'unittest',
        'pytest',
        'nose',
        'coverage',
        'setuptools',
        'distutils',
        'pip',
        'wheel',
        'pkg_resources',
        'numpy.tests',
        'pandas.tests',
        'matplotlib.tests',
        'PIL.tests',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# إزالة الملفات غير المرغوب فيها
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# إعدادات الملف التنفيذي
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ShipManagementSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # إخفاء نافذة وحدة التحكم
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # يمكن إضافة مسار الأيقونة هنا
    version_file=None,
    uac_admin=False,
    uac_uiaccess=False,
)

# إنشاء مجلد التوزيع (اختياري)
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='ShipManagementSystem_dist'
)
