#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار مبسط لنافذة البحث
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from database.database_manager import DatabaseManager

class SimpleSearchTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("اختبار البحث المبسط")
        self.root.geometry("800x600")
        
        self.db_manager = DatabaseManager()
        self.items_data = []
        self.filtered_items = []
        
        self.create_interface()
        self.load_items()
        
    def create_interface(self):
        """إنشاء الواجهة"""
        # حقل البحث
        search_frame = tk.Frame(self.root)
        search_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(search_frame, text="البحث:", font=('Arial', 12)).pack(side='right')
        
        self.search_var = tk.StringVar()
        self.search_entry = tk.Entry(search_frame, textvariable=self.search_var, font=('Arial', 12))
        self.search_entry.pack(side='right', fill='x', expand=True, padx=(10, 0))
        
        # ربط البحث الفوري
        self.search_var.trace('w', self.on_search_change)
        
        # جدول النتائج
        table_frame = tk.Frame(self.root)
        table_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        columns = ['code', 'name', 'description', 'price']
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings')
        
        # تكوين الأعمدة
        self.tree.heading('code', text='الكود')
        self.tree.heading('name', text='الاسم')
        self.tree.heading('description', text='الوصف')
        self.tree.heading('price', text='السعر')
        
        self.tree.column('code', width=100)
        self.tree.column('name', width=200)
        self.tree.column('description', width=300)
        self.tree.column('price', width=100)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # عداد النتائج
        self.count_label = tk.Label(self.root, text="", font=('Arial', 10))
        self.count_label.pack(pady=5)
        
    def load_items(self):
        """تحميل الأصناف"""
        try:
            query = '''
                SELECT i.id, i.item_code, i.item_name, i.description, 
                       i.selling_price, i.cost_price
                FROM items i
                WHERE i.is_active = 1
                ORDER BY i.item_name
            '''
            
            items = self.db_manager.fetch_all(query)
            print(f"تم تحميل {len(items)} صنف")
            
            self.items_data = []
            for item in items:
                item_data = {
                    'id': item['id'],
                    'code': item['item_code'] or '',
                    'name': item['item_name'] or '',
                    'description': item['description'] or '',
                    'price': item['selling_price'] or item['cost_price'] or 0
                }
                self.items_data.append(item_data)
            
            self.filtered_items = self.items_data.copy()
            self.update_tree()
            
        except Exception as e:
            print(f"خطأ في تحميل الأصناف: {e}")
            
    def on_search_change(self, *args):
        """عند تغيير البحث"""
        search_term = self.search_var.get().strip().lower()
        
        if not search_term:
            self.filtered_items = self.items_data.copy()
        else:
            self.filtered_items = []
            for item in self.items_data:
                item_text = f"{item['code']} {item['name']} {item['description']}".lower()
                if search_term in item_text:
                    self.filtered_items.append(item)
        
        self.update_tree()
        
    def update_tree(self):
        """تحديث الجدول"""
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # إضافة النتائج
        for item_data in self.filtered_items:
            self.tree.insert('', 'end', values=[
                item_data['code'],
                item_data['name'],
                item_data['description'][:50] + "..." if len(item_data['description']) > 50 else item_data['description'],
                f"{item_data['price']:,.2f}"
            ])
        
        # تحديث العداد
        self.count_label.config(text=f"عدد النتائج: {len(self.filtered_items)} من أصل {len(self.items_data)}")
        
    def run(self):
        """تشغيل النافذة"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleSearchTest()
    app.run()
