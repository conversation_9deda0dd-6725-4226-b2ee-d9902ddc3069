#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار مباشر لنافذة البحث
"""

import sys
import os
import tkinter as tk

# إضافة المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_search_dialog():
    """اختبار نافذة البحث"""
    print("🔍 اختبار نافذة البحث المباشر")
    
    # إنشاء نافذة رئيسية
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    try:
        # استيراد نافذة البحث
        from src.item_search_dialog import ItemSearchDialog
        
        # إنشاء بيانات وهمية للاختبار
        test_items = [
            {
                'id': 1,
                'item_code': 'TEST001',
                'item_name': 'صنف تجريبي 1',
                'description': 'وصف الصنف التجريبي الأول',
                'unit': 'قطعة',
                'unit_price': '100.00',
                'category': 'تجريبي',
                'current_stock': '10',
                'origin_country': 'السعودية'
            },
            {
                'id': 2,
                'item_code': 'TEST002',
                'item_name': 'صنف تجريبي 2',
                'description': 'وصف الصنف التجريبي الثاني',
                'unit': 'كيلو',
                'unit_price': '50.00',
                'category': 'تجريبي',
                'current_stock': '5',
                'origin_country': 'مصر'
            }
        ]
        
        print(f"📦 إنشاء نافذة البحث مع {len(test_items)} صنف تجريبي")
        
        # إنشاء نافذة البحث
        search_dialog = ItemSearchDialog(root, test_items, None)
        
        # تشغيل النافذة
        root.deiconify()  # إظهار النافذة الرئيسية
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة البحث: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_search_dialog()
