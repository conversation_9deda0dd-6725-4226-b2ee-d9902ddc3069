#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة البحث في النموذج المبسط
Test Search Fix for Simple Item Form

هذا الملف يختبر إصلاح مشكلة "يجب اختيار صنف من النتائج" في نافذة البحث
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# إضافة مسار المشروع
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from src.fullscreen_shipment_form import FullscreenShipmentForm, SimpleItemSearchDialog
    from database.database_manager import DatabaseManager
except ImportError as e:
    print(f"خطأ في الاستيراد: {e}")
    print("تأكد من وجود الملفات في المسار الصحيح")
    sys.exit(1)

def test_search_dialog():
    """اختبار نافذة البحث المبسطة"""
    print("🧪 اختبار نافذة البحث المبسطة...")
    
    try:
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # إنشاء نموذج الشحنة
        form = FullscreenShipmentForm(root, mode='add')
        
        # التنقل إلى قسم الأصناف
        form.switch_section(1)  # قسم الأصناف هو القسم رقم 1
        
        print("✅ تم إنشاء النموذج المبسط بنجاح")
        print("🔍 اختبار نافذة البحث...")
        
        # إنشاء نافذة البحث المبسطة مباشرة
        search_dialog = SimpleItemSearchDialog(root, form)
        
        print("✅ تم إنشاء نافذة البحث المبسطة بنجاح")
        print(f"📊 عدد الأصناف المحملة: {len(search_dialog.items_data)}")
        
        # تشغيل التطبيق
        print("\n🚀 تشغيل التطبيق...")
        print("💡 للاختبار:")
        print("   1. انتقل إلى قسم 'الأصناف'")
        print("   2. اضغط F9 لفتح نافذة البحث المبسطة")
        print("   3. ابحث عن صنف واختره")
        print("   4. تحقق من ملء النموذج المبسط تلقائياً")
        print("   5. لن تظهر رسالة 'يجب اختيار صنف من النتائج' بعد الآن")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_simple_form_integration():
    """اختبار تكامل النموذج المبسط مع البحث"""
    print("🔗 اختبار تكامل النموذج المبسط مع البحث...")
    
    try:
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.withdraw()
        
        # إنشاء نموذج الشحنة
        form = FullscreenShipmentForm(root, mode='add')
        
        # التحقق من وجود الدوال المطلوبة
        required_methods = [
            'show_item_search',
            'fill_simple_form_from_search',
            'create_simple_item_input_form',
            'add_simple_item',
            'validate_simple_form'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(form, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ دوال مفقودة: {missing_methods}")
            return False
        
        print("✅ جميع الدوال المطلوبة موجودة")
        
        # التحقق من وجود متغيرات النموذج المبسط
        form.switch_section(1)  # الانتقال لقسم الأصناف لإنشاء النموذج
        
        required_vars = [
            'simple_item_code',
            'simple_item_name',
            'simple_quantity',
            'simple_unit_price',
            'simple_unit',
            'simple_description'
        ]
        
        missing_vars = []
        for var in required_vars:
            if var not in form.form_vars:
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ متغيرات مفقودة: {missing_vars}")
            return False
        
        print("✅ جميع متغيرات النموذج المبسط موجودة")
        
        # اختبار إنشاء نافذة البحث
        try:
            search_dialog = SimpleItemSearchDialog(root, form)
            search_dialog.close_dialog()  # إغلاق فوري للاختبار
            print("✅ نافذة البحث المبسطة تعمل بشكل صحيح")
        except Exception as e:
            print(f"❌ خطأ في نافذة البحث: {e}")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار إصلاح مشكلة البحث في النموذج المبسط")
    print("=" * 60)
    
    # اختبار التكامل أولاً
    if not test_simple_form_integration():
        print("❌ فشل اختبار التكامل")
        return
    
    print("\n" + "-" * 40)
    
    # اختبار نافذة البحث
    if test_search_dialog():
        print("✅ تم اختبار نافذة البحث بنجاح")
        print("\n🎉 الإصلاحات المطبقة:")
        print("   • إنشاء نافذة بحث مخصصة للنموذج المبسط")
        print("   • ملء النموذج المبسط تلقائياً عند اختيار صنف")
        print("   • إزالة رسالة 'يجب اختيار صنف من النتائج'")
        print("   • تحسين تجربة المستخدم في البحث")
    else:
        print("❌ فشل اختبار نافذة البحث")

if __name__ == "__main__":
    main()
