# 🔍 البحث المتقدم في أكواد الأصناف - F9
## Advanced Item Code Search Features

### 📋 نظرة عامة
تم تطوير شاشة البحث في أكواد الأصناف (F9) إلى نظام بحث متقدم وفوري يوفر تجربة بحث محسنة ومتعددة المعايير.

---

## ✨ الميزات الجديدة

### 🚀 البحث الفوري (Real-time Search)
- **البحث أثناء الكتابة**: النتائج تظهر فوراً بمجرد كتابة أي حرف
- **تأخير ذكي**: 300 مللي ثانية لتجنب البحث المفرط
- **بحث شامل**: يبحث في كود الصنف، اسم الصنف، والوصف معاً

### 🎯 البحث المتقدم
#### حقول البحث المتخصصة:
1. **🔍 البحث السريع**: بحث عام في جميع الحقول
2. **🔢 كود الصنف**: بحث محدد في أكواد الأصناف
3. **📦 اسم الصنف**: بحث في أسماء الأصناف
4. **📏 الوحدة**: فلترة حسب وحدة القياس
5. **🌍 بلد المنشأ**: قائمة منسدلة للبلدان الشائعة
6. **💰 نطاق السعر**: فلترة حسب نطاق سعري محدد

### 📊 عرض النتائج المحسن
- **جدول مفصل**: 6 أعمدة تشمل الوصف
- **ألوان تمييزية**: 
  - 🟡 الأسعار العالية (أكثر من 1000)
  - 🔴 الأصناف بدون سعر
- **عداد ذكي**: يعرض النتائج مع النسبة المئوية
- **ترقيم الصفوف**: لسهولة التتبع

### 🎨 تحسينات الواجهة
- **حجم أكبر**: 1000x700 بكسل لعرض أفضل
- **تصميم حديث**: ألوان وخطوط محسنة
- **أزرار إضافية**:
  - 🔄 تحديث البيانات
  - 🗑️ مسح جميع الحقول
  - 🎯 بحث متقدم
- **مؤشرات الحالة**: تظهر حالة البحث والتحميل

---

## 🔧 التحسينات التقنية

### ⚡ الأداء
- **بحث محسن**: استعلامات قاعدة بيانات محسنة
- **ذاكرة التخزين المؤقت**: تحميل البيانات مرة واحدة
- **فلترة محلية**: البحث يتم في الذاكرة للسرعة

### 🎹 اختصارات لوحة المفاتيح
- **F9**: إغلاق النافذة
- **Ctrl+F**: التركيز على حقل البحث
- **F5**: تحديث البيانات
- **Enter**: اختيار الصنف
- **Escape**: إغلاق النافذة

### 🔍 معايير البحث المتقدمة
```python
# البحث العام
general_search = "كمبيوتر"  # يبحث في الكود والاسم والوصف

# البحث المتخصص
item_code = "TEST"          # كود الصنف
item_name = "جهاز"          # اسم الصنف
unit = "قطعة"              # الوحدة
origin_country = "الصين"    # بلد المنشأ
price_min = 100            # السعر الأدنى
price_max = 1000           # السعر الأعلى
```

---

## 📱 كيفية الاستخدام

### 1. فتح شاشة البحث
- اضغط **F9** من شاشة الشحنة الجديدة
- أو اضغط **F9** من حقل كود الصنف في نافذة الصنف

### 2. البحث السريع
1. ابدأ الكتابة في حقل "البحث السريع"
2. النتائج ستظهر فوراً أثناء الكتابة
3. انقر مرتين على النتيجة لاختيارها

### 3. البحث المتقدم
1. استخدم الحقول المتخصصة للبحث الدقيق
2. اجمع بين عدة معايير للحصول على نتائج محددة
3. استخدم نطاق السعر للفلترة حسب التكلفة

### 4. إدارة النتائج
- **عداد النتائج**: يظهر عدد النتائج والنسبة المئوية
- **ترتيب الألوان**: الأسعار العالية باللون الأصفر
- **التنقل**: استخدم أشرطة التمرير للنتائج الكثيرة

---

## 🧪 الاختبارات

### اختبارات آلية ✅
- ✅ اتصال قاعدة البيانات
- ✅ تحميل البيانات
- ✅ البحث العام
- ✅ حقول البحث المتقدم
- ✅ فلتر نطاق السعر
- ✅ فلتر بلد المنشأ
- ✅ اختبار الأداء

### تشغيل الاختبارات
```bash
python test_advanced_item_search.py
```

---

## 🔄 التكامل مع النظام

### الملفات المحدثة
- `src/item_code_search_dialog.py`: الملف الرئيسي المحدث
- `test_advanced_item_search.py`: ملف الاختبارات الشامل

### نقاط التكامل
- `src/fullscreen_shipment_form.py`: ربط F9 في شاشة الشحنة
- `src/item_dialog.py`: ربط F9 في نافذة الصنف
- `src/simple_rtl_components.py`: مكونات الواجهة RTL

---

## 🎯 الفوائد

### للمستخدمين
- **سرعة أكبر**: بحث فوري بدون انتظار
- **دقة أعلى**: معايير بحث متعددة ومتخصصة
- **سهولة الاستخدام**: واجهة بديهية ومألوفة
- **معلومات أكثر**: عرض تفصيلي للأصناف

### للنظام
- **أداء محسن**: استعلامات محسنة وذاكرة تخزين مؤقت
- **قابلية التوسع**: يدعم قواعد بيانات كبيرة
- **استقرار**: اختبارات شاملة وتعامل مع الأخطاء
- **صيانة سهلة**: كود منظم وموثق

---

## 🔮 التطويرات المستقبلية

### ميزات مقترحة
- **حفظ البحثات**: حفظ معايير البحث المفضلة
- **تصدير النتائج**: تصدير نتائج البحث إلى Excel
- **بحث متقدم أكثر**: فلاتر إضافية مثل التاريخ والفئة
- **إحصائيات**: تحليلات لأنماط البحث الشائعة

### تحسينات تقنية
- **فهرسة قاعدة البيانات**: لتحسين أداء البحث
- **ذاكرة تخزين مؤقت ذكية**: تحديث تلقائي للبيانات
- **بحث ضبابي**: البحث بالكلمات المشابهة
- **واجهة برمجة تطبيقات**: API للبحث من تطبيقات أخرى

---

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع هذا الدليل أولاً
2. شغل الاختبارات للتأكد من سلامة النظام
3. تحقق من سجلات الأخطاء في وحدة التحكم

---

*تم التطوير بواسطة Augment Agent - نظام البحث المتقدم في أكواد الأصناف*
