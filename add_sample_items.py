#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إضافة بيانات تجريبية للأصناف لاختبار البحث
"""

import sqlite3
from database.database_manager import DatabaseManager

def add_sample_items():
    """إضافة أصناف تجريبية"""
    db_manager = DatabaseManager()
    
    # بيانات الأصناف التجريبية
    sample_items = [
        {
            'item_code': 'ITM001',
            'item_name': 'لابتوب ديل XPS 13',
            'description': 'لابتوب محمول عالي الأداء مع معالج Intel Core i7',
            'unit_of_measure': 'قطعة',
            'cost_price': 3500.00,
            'selling_price': 4200.00,
            'current_stock': 15,
            'min_stock_level': 5,
            'barcode': '1234567890123',
            'is_active': 1
        },
        {
            'item_code': 'ITM002',
            'item_name': 'ماوس لاسلكي لوجيتك',
            'description': 'ماوس لاسلكي بتقنية البلوتوث مع بطارية طويلة المدى',
            'unit_of_measure': 'قطعة',
            'cost_price': 45.00,
            'selling_price': 65.00,
            'current_stock': 50,
            'min_stock_level': 10,
            'barcode': '1234567890124',
            'is_active': 1
        },
        {
            'item_code': 'ITM003',
            'item_name': 'كيبورد ميكانيكي',
            'description': 'لوحة مفاتيح ميكانيكية للألعاب مع إضاءة RGB',
            'unit_of_measure': 'قطعة',
            'cost_price': 120.00,
            'selling_price': 180.00,
            'current_stock': 25,
            'min_stock_level': 8,
            'barcode': '1234567890125',
            'is_active': 1
        },
        {
            'item_code': 'ITM004',
            'item_name': 'شاشة سامسونج 27 بوصة',
            'description': 'شاشة LED عالية الدقة 4K مع تقنية HDR',
            'unit_of_measure': 'قطعة',
            'cost_price': 800.00,
            'selling_price': 1100.00,
            'current_stock': 12,
            'min_stock_level': 3,
            'barcode': '1234567890126',
            'is_active': 1
        },
        {
            'item_code': 'ITM005',
            'item_name': 'طابعة HP LaserJet',
            'description': 'طابعة ليزر أحادية اللون عالية السرعة',
            'unit_of_measure': 'قطعة',
            'cost_price': 450.00,
            'selling_price': 650.00,
            'current_stock': 8,
            'min_stock_level': 2,
            'barcode': '1234567890127',
            'is_active': 1
        }
    ]
    
    try:
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # إضافة فئة افتراضية إذا لم تكن موجودة
        cursor.execute("""
            INSERT OR IGNORE INTO item_categories (id, category_name, description, is_active)
            VALUES (1, 'إلكترونيات', 'أجهزة إلكترونية وملحقاتها', 1)
        """)
        
        # إضافة الأصناف
        for item in sample_items:
            cursor.execute("""
                INSERT OR REPLACE INTO items (
                    item_code, item_name, description, category_id, unit_of_measure,
                    cost_price, selling_price, current_stock, min_stock_level,
                    barcode, is_active
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                item['item_code'], item['item_name'], item['description'], 1,
                item['unit_of_measure'], item['cost_price'], item['selling_price'],
                item['current_stock'], item['min_stock_level'], item['barcode'],
                item['is_active']
            ))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إضافة الأصناف التجريبية بنجاح!")
        print(f"📦 تم إضافة {len(sample_items)} صنف")
        
        # عرض الأصناف المضافة
        print("\n📋 الأصناف المضافة:")
        for item in sample_items:
            print(f"  • {item['item_code']} - {item['item_name']}")
            
    except Exception as e:
        print(f"❌ خطأ في إضافة الأصناف: {e}")

if __name__ == "__main__":
    add_sample_items()
