#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح مشكلة F9 في حقل المورد
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_f9_functionality():
    """اختبار وظيفة F9 في الأقسام المختلفة"""
    print("=" * 60)
    print("🧪 اختبار إصلاح مشكلة F9 في حقل المورد")
    print("=" * 60)
    
    try:
        # استيراد النموذج
        from src.fullscreen_shipment_form import FullscreenShipmentForm
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # إنشاء النموذج
        form = FullscreenShipmentForm(root)
        
        print("✅ تم إنشاء النموذج بنجاح")
        
        # اختبار F9 في القسم الأساسي (المورد)
        print("\n🔍 اختبار F9 في القسم الأساسي (المورد)...")
        form.current_section = 0  # القسم الأساسي
        
        # محاكاة التركيز على حقل المورد
        if hasattr(form, 'supplier_combo'):
            form.supplier_combo.focus_set()
            print("✅ تم تعيين التركيز على حقل المورد")
        
        # اختبار handle_f9_key
        try:
            result = form.handle_f9_key()
            print("✅ تم استدعاء handle_f9_key بنجاح")
            print(f"📊 النتيجة: {result}")
        except Exception as e:
            print(f"❌ خطأ في handle_f9_key: {e}")
        
        # اختبار F9 في قسم الأصناف
        print("\n🔍 اختبار F9 في قسم الأصناف...")
        form.current_section = 1  # قسم الأصناف
        
        try:
            result = form.handle_f9_key()
            print("✅ تم استدعاء handle_f9_key في قسم الأصناف بنجاح")
            print(f"📊 النتيجة: {result}")
        except Exception as e:
            print(f"❌ خطأ في handle_f9_key في قسم الأصناف: {e}")
        
        # اختبار الدوال المباشرة
        print("\n🔍 اختبار الدوال المباشرة...")
        
        # اختبار show_supplier_search
        try:
            print("📞 اختبار show_supplier_search...")
            # لا نستدعيها فعلياً لتجنب فتح النوافذ
            if hasattr(form, 'show_supplier_search'):
                print("✅ دالة show_supplier_search موجودة")
            else:
                print("❌ دالة show_supplier_search مفقودة")
        except Exception as e:
            print(f"❌ خطأ في show_supplier_search: {e}")
        
        # اختبار show_item_search
        try:
            print("📞 اختبار show_item_search...")
            if hasattr(form, 'show_item_search'):
                print("✅ دالة show_item_search موجودة")
            else:
                print("❌ دالة show_item_search مفقودة")
        except Exception as e:
            print(f"❌ خطأ في show_item_search: {e}")
        
        # إغلاق النموذج
        form.close_form()
        
        print("\n🎉 تم الانتهاء من الاختبار بنجاح!")
        print("💡 الإصلاحات المطبقة:")
        print("   ✅ حذف دالة show_supplier_search المكررة")
        print("   ✅ إزالة التحقق المكرر من الأقسام")
        print("   ✅ تحسين منطق handle_f9_key")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_f9_functionality()
    if success:
        print("\n🚀 يمكنك الآن تشغيل التطبيق واختبار F9:")
        print("   1. شغل التطبيق الرئيسي")
        print("   2. في القسم الأساسي، ضع التركيز على حقل المورد")
        print("   3. اضغط F9 - يجب أن تفتح نافذة البحث في الموردين")
        print("   4. في قسم الأصناف، اضغط F9 - يجب أن تفتح نافذة البحث في الأصناف")
        print("   5. لن تظهر رسائل خطأ مكررة بعد الآن")
    else:
        print("\n❌ فشل الاختبار - يرجى مراجعة الأخطاء أعلاه")
