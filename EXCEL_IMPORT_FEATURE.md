# ميزة استيراد الأصناف من Excel

## 🎯 الهدف
تم إضافة ميزة استيراد الأصناف من ملفات Excel إلى شاشة إدارة الأصناف لتسهيل إدخال البيانات بكميات كبيرة.

## ✨ الميزات الجديدة

### 1️⃣ زر استيراد من Excel (📥)
- **الموقع**: شاشة إدارة الأصناف → شريط الأزرار
- **اللون**: برتقالي (warning)
- **الوظيفة**: استيراد البيانات من ملف Excel

### 2️⃣ زر قالب Excel (📋)
- **الموقع**: شاشة إدارة الأصناف → شريط الأزرار  
- **اللون**: أزرق (info)
- **الوظيفة**: إنشاء قالب Excel للاستيراد

## 📋 تنسيق ملف Excel المطلوب

### الأعمدة المطلوبة (إجبارية):
| العمود | الوصف | مثال |
|--------|-------|------|
| `item_code` | كود الصنف (فريد) | ITM001 |
| `item_name` | اسم الصنف | لابتوب ديل |
| `unit_of_measure` | وحدة القياس | قطعة |

### الأعمدة الاختيارية:
| العمود | الوصف | مثال |
|--------|-------|------|
| `category_name` | اسم الفئة | أجهزة كمبيوتر |
| `cost_price` | سعر التكلفة | 2500.00 |
| `selling_price` | سعر البيع | 3000.00 |
| `current_stock` | المخزون الحالي | 10 |
| `min_stock_level` | الحد الأدنى للمخزون | 5 |
| `description` | وصف الصنف | لابتوب ديل انسبايرون 15 |

## 🔧 كيفية الاستخدام

### الخطوة 1: إنشاء قالب Excel
1. افتح شاشة إدارة الأصناف
2. اضغط على زر "📋 قالب Excel"
3. اختر مكان حفظ القالب
4. سيتم إنشاء ملف Excel يحتوي على:
   - **ورقة "البيانات النموذجية"**: أمثلة على البيانات
   - **ورقة "قالب فارغ"**: للاستخدام الفعلي
   - **ورقة "التعليمات"**: شرح مفصل للأعمدة

### الخطوة 2: تعبئة البيانات
1. افتح القالب في Excel
2. استخدم ورقة "قالب فارغ"
3. أدخل بيانات الأصناف حسب التنسيق المطلوب
4. احفظ الملف

### الخطوة 3: الاستيراد
1. في شاشة إدارة الأصناف، اضغط "📥 استيراد من Excel"
2. اختر ملف Excel المعبأ
3. ستظهر نافذة معاينة البيانات
4. راجع البيانات واضغط "✅ تأكيد الاستيراد"
5. ستظهر نافذة التقدم أثناء الاستيراد
6. ستظهر نتائج الاستيراد في النهاية

## 📊 نافذة المعاينة
- **عرض البيانات**: أول 50 صف من الملف
- **معلومات الملف**: عدد الصفوف والأعمدة
- **أزرار التحكم**: تأكيد أو إلغاء الاستيراد

## 📈 نافذة التقدم
- **شريط التقدم**: يظهر تقدم عملية الاستيراد
- **حالة العملية**: "معالجة الصف X من Y"
- **إلغاء غير متاح**: لضمان سلامة البيانات

## 📋 نافذة النتائج
### الإحصائيات:
- 📝 إجمالي الصفوف
- ✅ أصناف جديدة تم إضافتها
- 🔄 أصناف موجودة تم تحديثها  
- ❌ أخطاء حدثت
- ✔️ إجمالي العمليات الناجحة

### قائمة الأخطاء:
- تفاصيل كل خطأ مع رقم الصف
- إمكانية التمرير لعرض جميع الأخطاء

## 🔒 الصلاحيات المطلوبة
- **استيراد البيانات**: `manage_items`
- **إنشاء القالب**: لا توجد صلاحيات خاصة

## ⚠️ المتطلبات التقنية
### المكتبات المطلوبة:
```python
pandas>=1.5.0      # لقراءة ملفات Excel
openpyxl>=3.0.0    # لدعم تنسيق xlsx
```

### رسائل الخطأ:
- إذا لم تكن المكتبات مثبتة، ستظهر رسالة توضح كيفية التثبيت
- `pip install pandas openpyxl`

## 🛡️ معالجة الأخطاء

### أخطاء الملف:
- ملف غير صالح أو تالف
- تنسيق غير مدعوم
- أعمدة مطلوبة مفقودة

### أخطاء البيانات:
- كود صنف فارغ أو مكرر
- اسم صنف فارغ
- وحدة قياس فارغة
- قيم رقمية غير صحيحة

### أخطاء قاعدة البيانات:
- مشاكل الاتصال
- انتهاك قيود البيانات
- أخطاء SQL

## 🔄 منطق التحديث
- **إذا كان كود الصنف موجود**: يتم تحديث البيانات
- **إذا كان كود الصنف جديد**: يتم إضافة صنف جديد
- **الفئات الجديدة**: يتم إنشاؤها تلقائياً إذا لم تكن موجودة

## 📁 الملفات المعدلة

### `src/items_window.py`
- ✅ إضافة استيرادات pandas و openpyxl
- ✅ إضافة زر "📥 استيراد من Excel"
- ✅ إضافة زر "📋 قالب Excel"
- ✅ دالة `import_from_excel()` - الاستيراد الرئيسي
- ✅ دالة `show_import_preview()` - معاينة البيانات
- ✅ دالة `process_excel_import()` - معالجة الاستيراد
- ✅ دالة `show_import_results()` - عرض النتائج
- ✅ دالة `create_excel_template()` - إنشاء القالب

### `templates/items_import_template.py`
- ✅ سكريبت إنشاء قالب Excel مستقل

## 🎉 الفوائد
1. **توفير الوقت**: استيراد مئات الأصناف في دقائق
2. **تقليل الأخطاء**: التحقق التلقائي من البيانات
3. **سهولة الاستخدام**: واجهة بديهية باللغة العربية
4. **المرونة**: دعم التحديث والإضافة معاً
5. **الشفافية**: تقارير مفصلة عن النتائج

## 🔮 التطوير المستقبلي
- دعم تنسيقات أخرى (CSV, JSON)
- استيراد الصور مع الأصناف
- جدولة الاستيراد التلقائي
- تصدير الأصناف إلى Excel
- قوالب متخصصة حسب نوع النشاط
