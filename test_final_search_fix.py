#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نهائي لإصلاح مشكلة البحث في النموذج المبسط
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_search_dialog():
    """اختبار نافذة البحث المبسطة"""
    print("=" * 60)
    print("🧪 اختبار نهائي لإصلاح مشكلة البحث")
    print("=" * 60)
    
    try:
        # استيراد النموذج
        from src.fullscreen_shipment_form import FullscreenShipmentForm
        
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # إنشاء النموذج
        form = FullscreenShipmentForm(root)
        
        # التأكد من أن النموذج في قسم الأصناف
        form.current_section = 1
        
        print("✅ تم إنشاء النموذج بنجاح")
        
        # اختبار إنشاء نافذة البحث
        from src.fullscreen_shipment_form import SimpleItemSearchDialog
        
        search_dialog = SimpleItemSearchDialog(form.root, form)
        
        print(f"✅ تم إنشاء نافذة البحث بنجاح")
        print(f"📊 عدد الأصناف المحملة: {len(search_dialog.items_data)}")
        print(f"📊 عدد النتائج المعروضة: {len(search_dialog.filtered_items)}")
        
        # اختبار البحث
        search_dialog.search_var.set("صنف")
        search_dialog.on_search_change()
        
        print(f"📊 عدد النتائج بعد البحث: {len(search_dialog.filtered_items)}")
        
        # اختبار عدد العناصر في الجدول
        tree_items = search_dialog.results_tree.get_children()
        print(f"📊 عدد العناصر في الجدول: {len(tree_items)}")
        
        if len(tree_items) > 0:
            print("✅ الجدول يحتوي على نتائج")
            
            # اختبار اختيار عنصر
            first_item = tree_items[0]
            search_dialog.results_tree.selection_set(first_item)
            search_dialog.on_result_select(None)
            
            if search_dialog.selected_item:
                print("✅ تم اختيار صنف بنجاح")
                print(f"📦 الصنف المختار: {search_dialog.selected_item.get('item_name', 'غير محدد')}")
            else:
                print("❌ فشل في اختيار الصنف")
        else:
            print("❌ الجدول فارغ")
        
        # إغلاق النافذة
        search_dialog.close_dialog()
        form.close_form()
        
        print("\n🎉 تم الانتهاء من الاختبار بنجاح!")
        print("💡 المشاكل التي تم إصلاحها:")
        print("   ✅ تحميل بيانات الأصناف من قاعدة البيانات")
        print("   ✅ عرض النتائج في الجدول")
        print("   ✅ البحث والفلترة")
        print("   ✅ اختيار الأصناف")
        print("   ✅ إغلاق النافذة بشكل صحيح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_search_dialog()
    if success:
        print("\n🚀 يمكنك الآن تشغيل التطبيق واختبار البحث:")
        print("   1. شغل التطبيق الرئيسي")
        print("   2. انتقل إلى قسم 'الأصناف'")
        print("   3. اضغط F9 لفتح نافذة البحث")
        print("   4. ابحث عن صنف واختره")
        print("   5. تحقق من ملء النموذج تلقائياً")
    else:
        print("\n❌ فشل الاختبار - يرجى مراجعة الأخطاء أعلاه")
