# دليل المستخدم - نظام متابعة شحنات الموردين

## مقدمة

مرحباً بك في نظام متابعة شحنات الموردين، وهو نظام متكامل مصمم خصيصاً لإدارة ومتابعة شحنات الموردين بكفاءة عالية. يدعم النظام اللغة العربية بالكامل مع اتجاه RTL ويوفر جميع الأدوات اللازمة لإدارة الشحنات والموردين والمخزون.

## البدء السريع

### 1. تشغيل البرنامج
- انقر نقراً مزدوجاً على ملف `run.bat`
- أو افتح موجه الأوامر وقم بتشغيل: `python main.py`

### 2. تسجيل الدخول
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### 3. الواجهة الرئيسية
بعد تسجيل الدخول بنجاح، ستظهر لك الواجهة الرئيسية التي تحتوي على:
- شريط القوائم العلوي
- الشريط الجانبي للوصول السريع
- لوحة المعلومات الرئيسية
- شريط الحالة السفلي

## الوظائف الرئيسية

### إدارة الموردين

#### إضافة مورد جديد
1. من القائمة الجانبية، انقر على "إدارة الموردين"
2. انقر على زر "إضافة مورد جديد"
3. املأ البيانات المطلوبة:
   - كود المورد (مطلوب)
   - اسم المورد (مطلوب)
   - الشخص المسؤول
   - معلومات الاتصال
   - العنوان والمدينة
   - الرقم الضريبي
   - شروط الدفع
4. انقر على "حفظ"

#### تعديل مورد
1. من قائمة الموردين، اختر المورد المطلوب
2. انقر على زر "تعديل"
3. قم بتعديل البيانات المطلوبة
4. انقر على "حفظ"

#### البحث والتصفية
- استخدم حقل البحث للبحث بالاسم أو الكود
- يمكن البحث في جميع حقول المورد

### إدارة الأصناف

#### إضافة صنف جديد
1. من القائمة الجانبية، انقر على "إدارة الأصناف"
2. انقر على زر "إضافة صنف جديد"
3. املأ البيانات:
   - كود الصنف (مطلوب)
   - اسم الصنف (مطلوب)
   - الفئة
   - وحدة القياس
   - أسعار التكلفة والبيع
   - مستويات المخزون
4. انقر على "حفظ"

#### إدارة الفئات
1. من نافذة الأصناف، انقر على "إدارة الفئات"
2. يمكنك إضافة فئات جديدة أو تعديل الموجودة
3. يمكن إنشاء فئات فرعية

### إدارة الشحنات

#### إنشاء شحنة جديدة
1. من القائمة الجانبية، انقر على "إدارة الشحنات"
2. انقر على زر "شحنة جديدة"
3. املأ بيانات الشحنة:
   - رقم الشحنة (مطلوب)
   - المورد (مطلوب)
   - تواريخ الشحن والوصول
   - معلومات الموانئ
   - شركة الشحن
   - رقم الحاوية
   - بيانات الفاتورة
4. أضف الأصناف المشحونة
5. انقر على "حفظ"

#### تتبع الشحنات
1. من قائمة الشحنات، اختر الشحنة المطلوبة
2. انقر على "تتبع الشحنة"
3. يمكنك تحديث حالة الشحنة
4. إضافة ملاحظات وتواريخ مهمة

#### حالات الشحنة
- **في الانتظار**: شحنة جديدة لم تبدأ بعد
- **مؤكدة**: تم تأكيد الشحنة
- **تم الشحن**: غادرت الشحنة ميناء المغادرة
- **في الطريق**: الشحنة في طريقها للوصول
- **وصلت**: وصلت الشحنة لميناء الوصول
- **في الجمارك**: الشحنة في إجراءات الجمارك
- **تم التسليم**: تم تسليم الشحنة بالكامل
- **ملغية**: تم إلغاء الشحنة
- **متأخرة**: الشحنة متأخرة عن الموعد المحدد

### التقارير

#### تقرير الشحنات
1. من القائمة، اختر "التقارير"
2. انتقل لتبويب "تقرير الشحنات"
3. حدد المعايير:
   - فترة التاريخ
   - المورد (اختياري)
   - الحالة (اختياري)
4. اختر تنسيق التصدير (Excel أو PDF)
5. انقر على "إنشاء التقرير"

#### تقرير الموردين
1. انتقل لتبويب "تقرير الموردين"
2. اختر إذا كنت تريد الموردين النشطين فقط
3. اختر تنسيق التصدير
4. انقر على "إنشاء التقرير"

#### تقرير المخزون
1. انتقل لتبويب "تقرير المخزون"
2. اختر إذا كنت تريد الأصناف منخفضة المخزون فقط
3. اختر تنسيق التصدير
4. انقر على "إنشاء التقرير"

### الإعدادات

#### إعدادات الشركة
1. من القائمة، اختر "الإعدادات" > "إعدادات الشركة"
2. في تبويب "معلومات الشركة":
   - أدخل اسم الشركة
   - اختر شعار الشركة
   - أدخل معلومات الاتصال
   - حدد العملة الافتراضية
3. في تبويب "السنة المالية":
   - حدد بداية ونهاية السنة المالية
4. انقر على "حفظ الإعدادات"

#### إدارة المستخدمين
1. من القائمة، اختر "الإعدادات" > "إدارة المستخدمين"
2. لإضافة مستخدم جديد:
   - انقر على "إضافة مستخدم جديد"
   - املأ البيانات المطلوبة
   - حدد الدور والصلاحيات
   - انقر على "حفظ"

#### أدوار المستخدمين
- **مدير النظام**: جميع الصلاحيات
- **مدير**: إدارة الشحنات والموردين والتقارير
- **محاسب**: عرض البيانات والتقارير
- **موظف مبيعات**: عرض الشحنات والموردين
- **موظف**: عرض الشحنات فقط

### النسخ الاحتياطي

#### إنشاء نسخة احتياطية
1. من القائمة، اختر "ملف" > "نسخة احتياطية"
2. سيتم إنشاء النسخة تلقائياً في مجلد `backups`
3. ستحتوي النسخة على قاعدة البيانات والإعدادات

#### استعادة نسخة احتياطية
1. من القائمة، اختر "ملف" > "استعادة"
2. اختر ملف النسخة الاحتياطية
3. تأكد من العملية
4. سيتم استعادة البيانات وإعادة تشغيل البرنامج

## نصائح وحلول للمشاكل

### تحسين الأداء
- قم بتشغيل أداة التحسين بانتظام: `python optimize_system.py`
- احذف البيانات القديمة غير المطلوبة
- أنشئ نسخ احتياطية منتظمة

### حل المشاكل الشائعة

#### البرنامج لا يبدأ
- تأكد من تثبيت Python بشكل صحيح
- تأكد من تثبيت المكتبات المطلوبة: `pip install -r requirements.txt`
- تحقق من ملف السجلات في مجلد `logs`

#### مشاكل قاعدة البيانات
- قم بتشغيل فحص سلامة قاعدة البيانات من أداة التحسين
- استعد نسخة احتياطية حديثة إذا لزم الأمر

#### مشاكل التقارير
- تأكد من تثبيت مكتبات pandas و reportlab
- تحقق من وجود مجلد `reports` وصلاحيات الكتابة

### اختصارات لوحة المفاتيح
- **Enter**: تسجيل الدخول في نافذة تسجيل الدخول
- **F5**: تحديث البيانات في النوافذ
- **Ctrl+N**: إضافة عنصر جديد (حسب النافذة النشطة)
- **Ctrl+S**: حفظ البيانات
- **Escape**: إغلاق النوافذ الفرعية

## الدعم الفني

### معلومات الاتصال
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-11-1234567

### الموارد المفيدة
- ملف `README.md` للمعلومات التقنية
- مجلد `logs` لملفات السجلات
- أداة الاختبار: `python test_system.py`
- أداة التحسين: `python optimize_system.py`

### الإبلاغ عن المشاكل
عند الإبلاغ عن مشكلة، يرجى تضمين:
- وصف مفصل للمشكلة
- خطوات إعادة إنتاج المشكلة
- رسائل الخطأ (إن وجدت)
- ملف السجلات ذي الصلة

---

**ملاحظة**: هذا الدليل يغطي الوظائف الأساسية للنظام. للحصول على معلومات تقنية مفصلة، يرجى مراجعة ملف `README.md`.
