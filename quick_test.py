#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع للتحقق من إصلاح البحث
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """اختبار الاستيراد"""
    try:
        print("🧪 اختبار استيراد النموذج...")
        
        from src.fullscreen_shipment_form import SimpleItemSearchDialog
        print("✅ تم استيراد SimpleItemSearchDialog بنجاح")
        
        # اختبار وجود الدوال المطلوبة
        required_methods = [
            'load_items_data',
            'create_interface', 
            'on_search_change',
            'update_results_tree',
            'add_result_to_tree',
            'on_result_select',
            'select_item',
            'close_dialog'
        ]
        
        for method in required_methods:
            if hasattr(SimpleItemSearchDialog, method):
                print(f"✅ الدالة {method} موجودة")
            else:
                print(f"❌ الدالة {method} مفقودة")
        
        print("\n🎉 جميع الدوال المطلوبة موجودة!")
        print("💡 يمكنك الآن تشغيل التطبيق واختبار البحث:")
        print("   1. شغل التطبيق الرئيسي")
        print("   2. انتقل إلى قسم 'الأصناف'") 
        print("   3. اضغط F9 لفتح نافذة البحث")
        print("   4. ابحث عن صنف واختره")
        print("   5. تحقق من ملء النموذج تلقائياً")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

if __name__ == "__main__":
    test_import()
