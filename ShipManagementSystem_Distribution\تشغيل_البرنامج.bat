@echo off
chcp 65001 >nul
title نظام متابعة الشحنات - Ship Management System

echo ========================================
echo    نظام متابعة شحنات الموردين
echo    Ship Management System
echo ========================================
echo.
echo جاري تشغيل البرنامج...
echo Starting the program...
echo.

:: التحقق من وجود الملف التنفيذي
if not exist "ShipManagementSystem.exe" (
    echo خطأ: لم يتم العثور على الملف التنفيذي
    echo Error: Executable file not found
    echo.
    echo تأكد من وجود الملف ShipManagementSystem.exe في نفس المجلد
    echo Make sure ShipManagementSystem.exe is in the same folder
    pause
    exit /b 1
)

:: التحقق من وجود مجلد قاعدة البيانات
if not exist "database" (
    echo تحذير: مجلد قاعدة البيانات غير موجود
    echo Warning: Database folder not found
    echo.
    echo سيتم إنشاء قاعدة بيانات جديدة...
    echo A new database will be created...
    echo.
)

:: تشغيل البرنامج
echo تشغيل نظام متابعة الشحنات...
echo Running Ship Management System...
echo.

start "" "ShipManagementSystem.exe"

:: انتظار قصير ثم إغلاق نافذة الأوامر
timeout /t 3 /nobreak >nul
exit
