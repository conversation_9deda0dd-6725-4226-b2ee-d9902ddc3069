#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج الشحنة بملء الشاشة مع تصميم متقدم وشامل
Fullscreen Shipment Form with Advanced Design
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from datetime import datetime, date, timedelta
import uuid
import json
from tkcalendar import DateEntry

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS, SHIPMENT_STATUS, PORTS, SHIPPING_COMPANIES, CURRENCIES, CONTAINER_TYPES, PAYMENT_METHODS
from database.database_manager import DatabaseManager
from src.simple_rtl_components import *
from src.auth_manager import auth_manager
from src.item_dialog import ItemDialog
from src.item_search_dialog import ItemSearchDialog

class FullscreenShipmentForm:
    """نموذج الشحنة بملء الشاشة"""
    
    def __init__(self, parent, mode='add', shipment_data=None):
        self.parent = parent
        self.mode = mode  # add, edit, duplicate, view
        self.shipment_data = shipment_data or {}
        self.db_manager = DatabaseManager()
        
        # إنشاء النافذة
        self.root = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_fullscreen_window()
        
        # متغيرات النموذج
        self.form_vars = {}
        self.is_modified = False
        self.current_section = 0
        self.sections = []

        # قاموس المرفقات للمستندات
        self.document_attachments = {}
        print("📎 تم تهيئة قاموس المرفقات")

        # قاموس الروابط للمستندات
        self.document_links = {}
        print("🔗 تم تهيئة قاموس الروابط")
        
        # إنشاء الواجهة
        self.create_fullscreen_interface()
        
        # تحميل البيانات
        self.load_form_data()
        
        # ربط الأحداث
        self.bind_events()
        
    def setup_fullscreen_window(self):
        """إعداد النافذة بملء الشاشة"""
        titles = {
            'add': '➕ إضافة شحنة جديدة - وضع ملء الشاشة',
            'edit': '✏️ تعديل الشحنة - وضع ملء الشاشة',
            'duplicate': '📋 نسخ الشحنة - وضع ملء الشاشة',
            'view': '👁️ عرض تفاصيل الشحنة - وضع ملء الشاشة'
        }
        
        self.root.title(titles.get(self.mode, 'نموذج الشحنة - ملء الشاشة'))
        
        # تعيين ملء الشاشة
        self.root.state('zoomed')  # Windows
        # للأنظمة الأخرى
        try:
            self.root.attributes('-zoomed', True)  # Linux
        except:
            pass
        
        self.root.configure(bg=COLORS['background'])
        
        # منع تغيير الحجم
        self.root.resizable(False, False)
        
        # جعل النافذة modal
        if self.parent:
            self.root.transient(self.parent)
            self.root.grab_set()
        
        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # إخفاء شريط العنوان (اختياري)
        # self.root.overrideredirect(True)

    def on_closing(self):
        """عند إغلاق النافذة"""
        self.close_form()

    def update_time(self):
        """تحديث الوقت"""
        try:
            if self.root and self.root.winfo_exists():
                from datetime import datetime
                current_time = datetime.now().strftime("📅 %Y/%m/%d - ⏰ %H:%M:%S")
                self.time_label.configure(text=current_time)
                self.root.after(1000, lambda: self.update_time())
        except tk.TclError:
            # النافذة تم إغلاقها، توقف عن التحديث
            pass

    def show_supplier_search(self, event=None):
        """إظهار نافذة البحث في الموردين عند الضغط على F9"""
        try:
            # لا نحتاج للتحقق من القسم هنا لأن handle_f9_key يتولى ذلك

            # إنشاء نافذة البحث في الموردين
            from src.supplier_search_dialog import SupplierSearchDialog
            search_dialog = SupplierSearchDialog(self.root, self)
            self.root.wait_window(search_dialog.root)

            # إذا تم اختيار مورد، تحديث حقل المورد
            if hasattr(search_dialog, 'final_selected_supplier') and search_dialog.final_selected_supplier:
                supplier_data = search_dialog.final_selected_supplier
                # تحديث القائمة المنسدلة باستخدام الأسماء الصحيحة للحقول
                supplier_name = str(supplier_data.get('supplier_name', ''))
                supplier_code = str(supplier_data.get('supplier_code', ''))
                phone = str(supplier_data.get('phone', ''))
                email = str(supplier_data.get('email', ''))

                # تكوين النص المعروض
                if supplier_code:
                    supplier_text = f"{supplier_name} ({supplier_code})"
                else:
                    supplier_text = supplier_name

                self.form_vars['supplier_id'].set(supplier_text)

                # إظهار معلومات إضافية في تلميح أو تسمية إضافية
                info_parts = []
                if phone and phone != 'غير محدد':
                    info_parts.append(f"📞 {phone}")
                if email and email != 'غير محدد':
                    info_parts.append(f"📧 {email}")

                if info_parts:
                    info_text = " | ".join(info_parts)
                    # يمكن إضافة تسمية معلومات إضافية هنا إذا كانت متوفرة
                    print(f"معلومات المورد: {info_text}")  # للتشخيص

                # التركيز على الحقل التالي (تاريخ الشحن)
                if hasattr(self, 'shipment_date_entry'):
                    self.shipment_date_entry.focus_set()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث في الموردين: {str(e)}")

    def show_customer_search(self, event=None):
        """إظهار نافذة البحث في العملاء"""
        messagebox.showinfo("قريباً", "نافذة البحث في العملاء قيد التطوير")

    def show_warehouse_search(self, event=None):
        """إظهار نافذة البحث في المخازن"""
        messagebox.showinfo("قريباً", "نافذة البحث في المخازن قيد التطوير")

    def view_attachments(self):
        """عرض المرفقات"""
        messagebox.showinfo("قريباً", "عرض المرفقات قيد التطوير")

    def remove_attachment(self):
        """حذف مرفق"""
        messagebox.showinfo("قريباً", "حذف المرفقات قيد التطوير")

    def add_general_attachment(self):
        """إضافة مرفق عام"""
        try:
            from tkinter import filedialog
            filename = filedialog.askopenfilename(
                title="اختيار ملف للإرفاق",
                filetypes=[
                    ("PDF files", "*.pdf"),
                    ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"),
                    ("Document files", "*.doc *.docx *.txt"),
                    ("Excel files", "*.xls *.xlsx"),
                    ("All files", "*.*")
                ]
            )
            if filename:
                messagebox.showinfo("تم الإرفاق", f"تم اختيار الملف: {os.path.basename(filename)}")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إرفاق الملف: {str(e)}")

    def print_shipment(self):
        """طباعة الشحنة"""
        messagebox.showinfo("قريباً", "طباعة الشحنة قيد التطوير")

    def export_shipment(self):
        """تصدير الشحنة"""
        messagebox.showinfo("قريباً", "تصدير الشحنة قيد التطوير")

    def save_and_close(self):
        """حفظ وإغلاق"""
        if self.save_shipment():
            self.close_form()

    def close_form(self):
        """إغلاق النموذج"""
        try:
            # التحقق من وجود تغييرات غير محفوظة
            if hasattr(self, 'has_unsaved_changes') and self.has_unsaved_changes:
                result = messagebox.askyesnocancel(
                    "تغييرات غير محفوظة",
                    "هناك تغييرات غير محفوظة. هل تريد حفظها قبل الإغلاق؟"
                )
                if result is True:  # نعم - احفظ ثم أغلق
                    if not self.save_shipment():
                        return
                elif result is None:  # إلغاء
                    return
                # False = لا - أغلق بدون حفظ

            self.root.destroy()
        except Exception as e:
            print(f"خطأ في إغلاق النموذج: {e}")
            self.root.destroy()

    def show_status_info(self):
        """عرض معلومات الحالة"""
        messagebox.showinfo("معلومات الحالة", "معلومات الحالة قيد التطوير")

    def bind_events(self):
        """ربط الأحداث"""
        try:
            # ربط أحداث لوحة المفاتيح
            self.root.bind('<F9>', self.show_item_search)
            self.root.bind('<Control-s>', lambda e: self.save_shipment())
            self.root.bind('<Escape>', lambda e: self.close_form())
        except Exception as e:
            print(f"خطأ في ربط الأحداث: {e}")

    def get_required_sections_by_status(self, status=None):
        """الحصول على الأقسام المطلوبة حسب الحالة"""
        # إرجاع جميع الأقسام كمطلوبة افتراضياً
        return list(range(8))  # 8 أقسام

    def run(self):
        """تشغيل النموذج"""
        try:
            self.root.mainloop()
        except Exception as e:
            print(f"خطأ في تشغيل النموذج: {e}")

    def create_fullscreen_interface(self):
        """إنشاء الواجهة بملء الشاشة"""
        # الإطار الرئيسي
        self.main_frame = create_simple_rtl_frame(self.root)
        self.main_frame.pack(fill='both', expand=True)

        # شريط الحالة السفلي (إنشاؤه أولاً وتثبيته)
        self.footer_frame = create_simple_rtl_frame(self.main_frame)
        self.footer_frame.configure(bg=COLORS['light'], height=80)
        self.footer_frame.pack(fill='x', side='bottom')
        self.footer_frame.pack_propagate(False)

        # شريط العنوان العلوي
        self.create_fullscreen_header()

        # المحتوى الرئيسي (سيملأ المساحة المتبقية)
        self.create_main_content()

        # إنشاء محتوى شريط الحالة
        self.create_footer_content()

        # عرض القسم الأول بعد إنشاء جميع العناصر
        self.switch_section(0)
        
    def create_fullscreen_header(self):
        """إنشاء شريط العنوان بملء الشاشة"""
        header_frame = create_simple_rtl_frame(self.main_frame)
        header_frame.configure(bg=COLORS['primary'], height=100)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # الجانب الأيمن - العنوان والوصف
        title_section = create_simple_rtl_frame(header_frame)
        title_section.configure(bg=COLORS['primary'])
        title_section.pack(side='right', padx=40, pady=20)
        
        # العنوان الرئيسي
        title_text = {
            'add': '🚢 إضافة شحنة جديدة',
            'edit': '✏️ تعديل بيانات الشحنة',
            'duplicate': '📋 نسخ شحنة موجودة',
            'view': '👁️ عرض تفاصيل الشحنة'
        }
        
        main_title = create_simple_rtl_label(
            title_section,
            text=title_text.get(self.mode, 'نموذج الشحنة'),
            font=('Segoe UI', 28, 'bold'),
            fg=COLORS['text_white'],
            bg=COLORS['primary']
        )
        main_title.pack(anchor='e')
        
        # الوصف التفصيلي
        if self.mode in ['edit', 'duplicate', 'view'] and self.shipment_data:
            subtitle = create_simple_rtl_label(
                title_section,
                text=f"رقم الشحنة: {self.shipment_data.get('shipment_number', 'غير محدد')}",
                font=('Segoe UI', 16, 'normal'),
                fg=COLORS['text_white'],
                bg=COLORS['primary']
            )
            subtitle.pack(anchor='e', pady=(10, 0))
        else:
            subtitle = create_simple_rtl_label(
                title_section,
                text="نموذج شامل ومتقدم لإدارة بيانات الشحنة",
                font=('Segoe UI', 16, 'normal'),
                fg=COLORS['text_white'],
                bg=COLORS['primary']
            )
            subtitle.pack(anchor='e', pady=(10, 0))
        
        # الجانب الأيسر - معلومات المستخدم والوقت
        info_section = create_simple_rtl_frame(header_frame)
        info_section.configure(bg=COLORS['primary'])
        info_section.pack(side='left', padx=40, pady=20)
        
        # معلومات المستخدم
        current_user = auth_manager.get_current_user()
        username = current_user.get('username', 'غير محدد') if current_user else 'مستخدم'
        user_info = tk.Label(
            info_section,
            text=f"👤 المستخدم: {username}",
            font=('Segoe UI', 14, 'normal'),
            fg='white',
            bg=COLORS['primary']
        )
        user_info.pack(anchor='w')
        
        # الوقت الحالي
        self.time_label = tk.Label(
            info_section,
            text="",
            font=('Segoe UI', 12, 'normal'),
            fg='white',
            bg=COLORS['primary']
        )
        self.time_label.pack(anchor='w', pady=(10, 0))
        self.update_time()

        # الوسط - أزرار التنقل السريع
        nav_section = create_simple_rtl_frame(header_frame)
        nav_section.configure(bg=COLORS['primary'])
        nav_section.pack(expand=True, pady=20)
        
        # أزرار التنقل بين الأقسام
        nav_buttons_frame = create_simple_rtl_frame(nav_section)
        nav_buttons_frame.configure(bg=COLORS['primary'])
        nav_buttons_frame.pack()
        
        self.nav_buttons = []
        nav_items = [
            ("📋 أساسية", 0),
            ("📦 الأصناف", 1),
            ("🚢 الشحن", 2),
            ("📦 الحاوية", 3),
            ("💰 المالية", 4),
            ("📄 المستندات", 5),
            ("📊 التتبع", 6),
            ("📝 الملاحظات", 7)
        ]
        
        for text, section_id in nav_items:
            btn = create_simple_rtl_button(
                nav_buttons_frame,
                text=text,
                button_type="ghost",
                command=lambda s=section_id: self.switch_section(s)
            )
            btn.configure(
                bg=COLORS['primary'],
                fg=COLORS['text_white'],
                activebackground=COLORS['primary_light'],
                activeforeground=COLORS['text_white']
            )
            btn.pack(side='right', padx=5)
            self.nav_buttons.append(btn)
        
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إطار المحتوى الرئيسي
        content_frame = create_simple_rtl_frame(self.main_frame)
        content_frame.pack(fill='both', expand=True, padx=20, pady=(20, 10))
        
        # إنشاء notebook مخصص للأقسام
        self.create_sections_notebook(content_frame)
        
    def create_sections_notebook(self, parent):
        """إنشاء دفتر الأقسام"""
        # إطار الأقسام
        self.sections_frame = create_simple_rtl_frame(parent)
        self.sections_frame.pack(fill='both', expand=True)
        
        # إنشاء الأقسام
        self.sections = []
        
        # قسم المعلومات الأساسية
        basic_section = self.create_basic_info_section()
        self.sections.append(basic_section)

        # قسم الأصناف
        items_section = self.create_items_section()
        self.sections.append(items_section)

        # قسم معلومات الشحن
        shipping_section = self.create_shipping_info_section()
        self.sections.append(shipping_section)
        
        # قسم معلومات الحاوية
        container_section = self.create_container_info_section()
        self.sections.append(container_section)
        
        # قسم المعلومات المالية
        financial_section = self.create_financial_info_section()
        self.sections.append(financial_section)

        # قسم المستندات
        documents_section = self.create_documents_section()
        self.sections.append(documents_section)

        # قسم التتبع والحالة
        tracking_section = self.create_tracking_section()
        self.sections.append(tracking_section)

        # قسم الملاحظات والمرفقات
        notes_section = self.create_notes_section()
        self.sections.append(notes_section)

        # عرض القسم الأول (سيتم تأجيله حتى بعد إنشاء جميع العناصر)
        # self.switch_section(0)
        
    def create_basic_info_section(self):
        """إنشاء قسم المعلومات الأساسية"""
        section = create_simple_rtl_frame(self.sections_frame)
        
        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))
        
        title_label = create_simple_rtl_label(
            title_frame,
            text="📋 المعلومات الأساسية للشحنة",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')
        
        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="البيانات الأساسية المطلوبة لتسجيل الشحنة في النظام",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))
        
        # محتوى القسم في شبكة
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)
        
        # الصف الأول
        row1 = create_simple_rtl_frame(content_frame)
        row1.pack(fill='x', pady=(0, 30))
        
        # رقم الشحنة
        self.create_field_group(
            row1, "🔢 رقم الشحنة *", 'shipment_number',
            placeholder="SH-XXXXXX", required=True, width_ratio=1/3
        )

        # المورد
        self.create_supplier_field(row1, width_ratio=1/3)

        # رقم فاتورة المورد
        self.create_field_group(
            row1, "🧾 رقم فاتورة المورد", 'supplier_invoice_number',
            placeholder="INV-XXXXXX", width_ratio=1/3
        )
        
        # الصف الثاني
        row2 = create_simple_rtl_frame(content_frame)
        row2.pack(fill='x', pady=(30, 30))

        # الحالة
        self.create_status_field(row2, width_ratio=1/3)

        # حالة الإفراج
        self.create_combobox_field(
            row2, "🔓 حالة الإفراج", 'release_status',
            values=['بدون إفراج', 'مع الإفراج'], width_ratio=1/3
        )

        # الصف الثالث
        row3 = create_simple_rtl_frame(content_frame)
        row3.pack(fill='x', pady=(30, 30))

        # الأولوية (محسوبة تلقائياً)
        self.create_priority_display(row3, width_ratio=1/3)
        
        return section

    def create_items_section(self):
        """إنشاء قسم الأصناف"""
        section = create_simple_rtl_frame(self.sections_frame)

        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = create_simple_rtl_label(
            title_frame,
            text="📦 الأصناف والمنتجات",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')

        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="تفاصيل الأصناف والمنتجات المشحونة مع الكميات والأسعار",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))

        # محتوى القسم
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)

        # نموذج إدخال الأصناف المبسط
        self.create_simple_item_input_form(content_frame)

        # جدول الأصناف
        table_frame = create_simple_rtl_frame(content_frame)
        table_frame.pack(fill='both', expand=True, pady=(0, 20))

        # تعريف أعمدة جدول الأصناف
        items_columns = [
            ('item_code', 'كود الصنف', 100),
            ('item_name', 'اسم الصنف', 200),
            ('description', 'الوصف', 150),
            ('quantity', 'الكمية', 80),
            ('unit', 'الوحدة', 80),
            ('unit_price', 'سعر الوحدة', 100),
            ('total_price', 'الإجمالي', 100),
            ('weight', 'الوزن (كجم)', 100),
            ('volume', 'الحجم (م³)', 100),
            ('hs_code', 'كود HS', 100),
            ('origin_country', 'بلد المنشأ', 120),
            ('warehouse', 'المخزن', 150)
        ]

        # إنشاء Treeview للأصناف
        self.items_tree = create_simple_rtl_treeview(
            table_frame,
            columns=[col[0] for col in items_columns],
            show='tree headings',
            height=12
        )

        # تكوين أعمدة الأصناف
        for col_id, col_name, col_width in items_columns:
            self.items_tree.heading(col_id, text=col_name, anchor='e')
            self.items_tree.column(col_id, width=col_width, anchor='e', minwidth=50)

        # تكوين العمود الرئيسي
        self.items_tree.column('#0', width=30, minwidth=30, anchor='e')
        self.items_tree.heading('#0', text='#', anchor='e')

        # أشرطة التمرير للجدول
        items_v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=items_v_scrollbar.set)

        items_h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.items_tree.xview)
        self.items_tree.configure(xscrollcommand=items_h_scrollbar.set)

        # تخطيط جدول الأصناف
        self.items_tree.grid(row=0, column=0, sticky='nsew')
        items_v_scrollbar.grid(row=0, column=1, sticky='ns')
        items_h_scrollbar.grid(row=1, column=0, sticky='ew')

        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # ربط أحداث الجدول
        self.items_tree.bind('<<TreeviewSelect>>', self.on_item_select)
        self.items_tree.bind('<Double-1>', self.on_item_double_click)
        self.items_tree.bind('<Button-3>', self.show_items_context_menu)
        self.items_tree.bind('<F9>', self.show_item_search)

        # ربط F9 على مستوى النافذة حسب السياق
        self.root.bind('<F9>', self.handle_f9_key)

        # إعداد ألوان جدول الأصناف
        self.setup_items_tree_colors()

        # منطقة ملخص الأصناف
        summary_frame = create_simple_rtl_frame(content_frame)
        summary_frame.pack(fill='x', pady=(20, 0))

        # عنوان الملخص
        summary_title = create_simple_rtl_label(
            summary_frame,
            text="📊 ملخص الأصناف:",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['primary']
        )
        summary_title.pack(anchor='e', pady=(0, 10))

        # إطار الملخص
        summary_content = create_simple_rtl_frame(summary_frame)
        summary_content.pack(fill='x')

        # الصف الأول من الملخص
        summary_row1 = create_simple_rtl_frame(summary_content)
        summary_row1.pack(fill='x', pady=(0, 10))

        # إجمالي الأصناف
        self.total_items_label = create_simple_rtl_label(
            summary_row1,
            text="إجمالي الأصناف: 0",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary']
        )
        self.total_items_label.pack(side='right', padx=20)

        # إجمالي الكمية
        self.total_quantity_label = create_simple_rtl_label(
            summary_row1,
            text="إجمالي الكمية: 0",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary']
        )
        self.total_quantity_label.pack(side='right', padx=20)

        # إجمالي الوزن
        self.total_weight_label = create_simple_rtl_label(
            summary_row1,
            text="إجمالي الوزن: 0.00 كجم",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary']
        )
        self.total_weight_label.pack(side='right', padx=20)

        # الصف الثاني من الملخص
        summary_row2 = create_simple_rtl_frame(summary_content)
        summary_row2.pack(fill='x')

        # إجمالي الحجم
        self.total_volume_label = create_simple_rtl_label(
            summary_row2,
            text="إجمالي الحجم: 0.00 م³",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary']
        )
        self.total_volume_label.pack(side='right', padx=20)

        # إجمالي القيمة
        self.total_value_label = create_simple_rtl_label(
            summary_row2,
            text="إجمالي القيمة: 0.00",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['success']
        )
        self.total_value_label.pack(side='right', padx=20)

        # متوسط سعر الوحدة
        self.avg_price_label = create_simple_rtl_label(
            summary_row2,
            text="متوسط السعر: 0.00",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary']
        )
        self.avg_price_label.pack(side='right', padx=20)

        # قائمة الأصناف (لحفظ البيانات)
        self.items_data = []
        self.selected_item = None

        # تحديث الملخص
        self.update_items_summary()

        return section

    def setup_items_tree_colors(self):
        """إعداد ألوان جدول الأصناف"""
        try:
            # ألوان الصفوف المتناوبة
            self.items_tree.tag_configure('odd_row', background='#F9FAFB')
            self.items_tree.tag_configure('even_row', background='#FFFFFF')

            # ألوان خاصة
            self.items_tree.tag_configure('high_value', background='#FEF3C7')  # قيمة عالية
            self.items_tree.tag_configure('low_stock', background='#FECACA')   # مخزون منخفض
            self.items_tree.tag_configure('selected', background='#DBEAFE')    # محدد
        except Exception as e:
            print(f"خطأ في إعداد ألوان جدول الأصناف: {e}")

    def create_shipping_info_section(self):
        """إنشاء قسم معلومات الشحن"""
        section = create_simple_rtl_frame(self.sections_frame)

        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = create_simple_rtl_label(
            title_frame,
            text="🚢 معلومات الشحن والنقل",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')

        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="تفاصيل الموانئ وشركات الشحن ومعلومات الرحلة",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))

        # محتوى القسم
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)

        # الصف الأول - الموانئ
        row1 = create_simple_rtl_frame(content_frame)
        row1.pack(fill='x', pady=(0, 30))

        # ميناء المغادرة
        self.create_combobox_field(
            row1, "🚢 ميناء المغادرة", 'departure_port',
            values=list(PORTS.keys()), width_ratio=1/2
        )

        # ميناء الوصول
        self.create_combobox_field(
            row1, "🏁 ميناء الوصول", 'arrival_port',
            values=list(PORTS.keys()), width_ratio=1/2
        )

        # الصف الثاني - تواريخ الشحن الأساسية
        row2 = create_simple_rtl_frame(content_frame)
        row2.pack(fill='x', pady=(30, 30))

        # تاريخ الشحن
        self.create_date_field(
            row2, "📅 تاريخ الشحن *", 'shipment_date',
            placeholder="YYYY-MM-DD", required=True, width_ratio=1/3
        )

        # تاريخ الوصول المتوقع
        self.create_date_field(
            row2, "🏁 تاريخ الوصول المتوقع", 'expected_arrival_date',
            placeholder="YYYY-MM-DD", width_ratio=1/3
        )

        # شركة الشحن
        self.create_combobox_field(
            row2, "🏢 شركة الشحن", 'shipping_company',
            values=list(SHIPPING_COMPANIES.keys()), width_ratio=1/3
        )

        # الصف الثالث - التواريخ الفعلية
        row3 = create_simple_rtl_frame(content_frame)
        row3.pack(fill='x', pady=(30, 30))

        # تاريخ المغادرة الفعلي
        self.create_date_field(
            row3, "📅 تاريخ المغادرة الفعلي", 'actual_departure_date',
            placeholder="YYYY-MM-DD", width_ratio=1/3
        )

        # تاريخ الوصول الفعلي
        self.create_date_field(
            row3, "🏁 تاريخ الوصول الفعلي", 'actual_arrival_date',
            placeholder="YYYY-MM-DD", width_ratio=1/3
        )

        # الصف الرابع - بوليصة الشحن ومعلومات إضافية
        row4 = create_simple_rtl_frame(content_frame)
        row4.pack(fill='x', pady=(30, 0))

        # رقم بوليصة الشحن
        self.create_field_group(
            row4, "📋 رقم بوليصة الشحن", 'bill_of_lading',
            placeholder="BOL-XXXXXX", width_ratio=1/2
        )

        # رقم التتبع
        self.create_field_group(
            row4, "🔍 رقم التتبع", 'tracking_number',
            placeholder="TRK-XXXXXX", width_ratio=1/2
        )

        return section

    def create_container_info_section(self):
        """إنشاء قسم معلومات الحاوية"""
        section = create_simple_rtl_frame(self.sections_frame)

        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = create_simple_rtl_label(
            title_frame,
            text="📦 معلومات الحاوية والبضائع",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')

        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="تفاصيل الحاوية والأوزان والأحجام والمحتويات",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))

        # محتوى القسم
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)

        # الصف الأول - عدد الحاويات ونوع الحاوية
        row1 = create_simple_rtl_frame(content_frame)
        row1.pack(fill='x', pady=(0, 30))

        # عدد الحاويات
        self.create_container_count_field(row1)

        # نوع الحاوية
        self.create_combobox_field(
            row1, "📏 نوع الحاوية", 'container_type',
            values=list(CONTAINER_TYPES.keys()), width_ratio=1/3
        )

        # رقم الختم
        self.create_field_group(
            row1, "🔒 رقم الختم", 'seal_number',
            placeholder="SEAL-XXXXXX", width_ratio=1/3
        )

        # إطار أرقام الحاويات الديناميكي
        self.containers_frame = create_simple_rtl_frame(content_frame)
        self.containers_frame.pack(fill='x', pady=(0, 30))

        # قائمة لحفظ حقول أرقام الحاويات
        self.container_number_vars = []
        self.container_number_widgets = []

        # إنشاء حقل واحد افتراضياً
        self.update_container_fields(1)

        # الصف الثاني - الأوزان والأحجام
        row2 = create_simple_rtl_frame(content_frame)
        row2.pack(fill='x', pady=(30, 30))

        # الوزن الإجمالي
        self.create_field_group(
            row2, "⚖️ الوزن الإجمالي (كجم)", 'weight',
            placeholder="0.00", width_ratio=1/4
        )

        # الحجم
        self.create_field_group(
            row2, "📐 الحجم (م³)", 'volume',
            placeholder="0.00", width_ratio=1/4
        )

        # عدد القطع
        self.create_field_group(
            row2, "📊 عدد القطع", 'pieces_count',
            placeholder="0", width_ratio=1/4
        )

        # الوزن الصافي
        self.create_field_group(
            row2, "⚖️ الوزن الصافي (كجم)", 'net_weight',
            placeholder="0.00", width_ratio=1/4
        )

        # الصف الثالث - وصف البضائع
        row3 = create_simple_rtl_frame(content_frame)
        row3.pack(fill='x', pady=(30, 0))

        # وصف البضائع
        desc_label = create_simple_rtl_label(
            row3,
            text="📝 وصف البضائع:",
            font=('Segoe UI', 14, 'bold')
        )
        desc_label.pack(anchor='e', pady=(0, 10))

        self.goods_description_text = create_simple_rtl_text(
            row3,
            height=6
        )
        self.goods_description_text.pack(fill='x', pady=(0, 20))

        return section

    def create_container_count_field(self, parent):
        """إنشاء حقل عدد الحاويات"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', padx=(0, 30))

        # تسمية الحقل
        label = create_simple_rtl_label(
            field_frame,
            text="🔢 عدد الحاويات",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary']
        )
        label.pack(anchor='e', pady=(0, 10))

        # إطار الحقل
        entry_frame = create_simple_rtl_frame(field_frame)
        entry_frame.pack(fill='x')

        # متغير عدد الحاويات
        self.form_vars['container_count'] = tk.StringVar()
        self.form_vars['container_count'].set('1')  # القيمة الافتراضية

        # حقل الإدخال
        container_count_entry = create_simple_rtl_entry(
            entry_frame,
            textvariable=self.form_vars['container_count'],
            font=('Segoe UI', 12),
            width=10,
            justify='center'
        )
        container_count_entry.pack(anchor='e')

        # ربط تغيير القيمة بتحديث حقول أرقام الحاويات
        self.form_vars['container_count'].trace('w', self.on_container_count_change)

        # تحديد عرض الحقل
        field_frame.configure(width=int(parent.winfo_reqwidth() * (1/3)))

    def on_container_count_change(self, *args):
        """معالج تغيير عدد الحاويات"""
        try:
            count = int(self.form_vars['container_count'].get() or 1)
            if count < 1:
                count = 1
                self.form_vars['container_count'].set('1')
            elif count > 10:  # حد أقصى 10 حاويات
                count = 10
                self.form_vars['container_count'].set('10')

            self.update_container_fields(count)
        except ValueError:
            # في حالة إدخال قيمة غير صحيحة، استخدم 1
            self.form_vars['container_count'].set('1')
            self.update_container_fields(1)

    def update_container_fields(self, count):
        """تحديث حقول أرقام الحاويات حسب العدد"""
        # مسح الحقول الموجودة
        for widget in self.container_number_widgets:
            widget.destroy()
        self.container_number_widgets.clear()
        self.container_number_vars.clear()

        # إنشاء الحقول الجديدة
        for i in range(count):
            self.create_single_container_field(i + 1)

    def create_single_container_field(self, container_num):
        """إنشاء حقل واحد لرقم الحاوية"""
        # إطار الحقل
        field_frame = create_simple_rtl_frame(self.containers_frame)
        field_frame.pack(fill='x', pady=(10, 0))

        # تسمية الحقل
        label_text = f"📦 رقم الحاوية {container_num}" if container_num > 1 else "📦 رقم الحاوية"
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary']
        )
        label.pack(anchor='e', pady=(0, 10))

        # إطار الإدخال
        entry_frame = create_simple_rtl_frame(field_frame)
        entry_frame.pack(fill='x')

        # متغير الحقل
        var_name = f'container_number_{container_num}' if container_num > 1 else 'container_number'
        container_var = tk.StringVar()
        self.form_vars[var_name] = container_var
        self.container_number_vars.append(container_var)

        # حقل الإدخال
        container_entry = create_simple_rtl_entry(
            entry_frame,
            textvariable=container_var,
            font=('Segoe UI', 12),
            width=30
        )
        container_entry.pack(anchor='e', padx=(0, 50))

        # إضافة placeholder
        container_entry.insert(0, "XXXX-XXXXXXX-X")
        container_entry.bind('<FocusIn>', lambda e: self.clear_placeholder(e, "XXXX-XXXXXXX-X"))
        container_entry.bind('<FocusOut>', lambda e: self.restore_placeholder(e, "XXXX-XXXXXXX-X"))

        # حفظ المرجع للحذف لاحقاً
        self.container_number_widgets.append(field_frame)

    def create_financial_info_section(self):
        """إنشاء قسم المعلومات المالية"""
        section = create_simple_rtl_frame(self.sections_frame)

        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = create_simple_rtl_label(
            title_frame,
            text="💰 المعلومات المالية والتكاليف",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')

        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="القيم المالية وتكاليف الشحن وطرق الدفع",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))

        # محتوى القسم
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)

        # الصف الأول - العملة والقيم الأساسية
        row1 = create_simple_rtl_frame(content_frame)
        row1.pack(fill='x', pady=(0, 30))

        # العملة
        self.create_combobox_field(
            row1, "💱 العملة", 'currency',
            values=list(CURRENCIES.keys()), width_ratio=1/4
        )

        # القيمة الإجمالية
        self.create_field_group(
            row1, "💰 القيمة الإجمالية", 'total_value',
            placeholder="0.00", width_ratio=1/4
        )

        # تكلفة الشحن
        self.create_field_group(
            row1, "🚢 تكلفة الشحن", 'shipping_cost',
            placeholder="0.00", width_ratio=1/4
        )

        # رسوم التأمين
        self.create_field_group(
            row1, "🛡️ رسوم التأمين", 'insurance_cost',
            placeholder="0.00", width_ratio=1/4
        )

        # الصف الثاني - التكاليف الإضافية
        row2 = create_simple_rtl_frame(content_frame)
        row2.pack(fill='x', pady=(30, 30))

        # رسوم إضافية
        self.create_field_group(
            row2, "💸 رسوم إضافية", 'additional_fees',
            placeholder="0.00", width_ratio=1/4
        )

        # رسوم الجمارك
        self.create_field_group(
            row2, "🏛️ رسوم الجمارك", 'customs_fees',
            placeholder="0.00", width_ratio=1/4
        )

        # رسوم المناولة
        self.create_field_group(
            row2, "🔧 رسوم المناولة", 'handling_fees',
            placeholder="0.00", width_ratio=1/4
        )

        # إجمالي التكلفة (محسوب تلقائياً)
        self.create_calculated_field(
            row2, "💯 إجمالي التكلفة", 'total_cost', width_ratio=1/4
        )

        # الصف الثالث - معلومات الدفع
        row3 = create_simple_rtl_frame(content_frame)
        row3.pack(fill='x', pady=(30, 0))

        # طريقة الدفع
        self.create_combobox_field(
            row3, "💳 طريقة الدفع", 'payment_method',
            values=list(PAYMENT_METHODS.keys()), width_ratio=1/3
        )

        # حالة الدفع
        self.create_combobox_field(
            row3, "💰 حالة الدفع", 'payment_status',
            values=['لم يتم الدفع', 'دفع جزئي', 'تم الدفع بالكامل', 'مسترد'], width_ratio=1/3
        )

        # المبلغ المدفوع
        self.create_field_group(
            row3, "💵 المبلغ المدفوع", 'paid_amount',
            placeholder="0.00", width_ratio=1/3
        )

        return section

    def create_documents_section(self):
        """إنشاء قسم المستندات"""
        section = create_simple_rtl_frame(self.sections_frame)

        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = create_simple_rtl_label(
            title_frame,
            text="📄 المستندات والوثائق الرسمية",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')

        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="جميع المستندات والوثائق المطلوبة للشحنة والجمارك",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))

        # محتوى القسم
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)

        # الصف الأول - المستندات الأساسية
        row1 = create_simple_rtl_frame(content_frame)
        row1.pack(fill='x', pady=(0, 30))

        # الوثائق الأولية (كانت الفاتورة التجارية)
        self.create_document_link_field(
            row1, "📋 الوثائق الأولية", 'commercial_invoice',
            "رابط الوثائق الأولية", width_ratio=1/3
        )

        # المستندات (DN) (كانت قائمة التعبئة)
        self.create_document_link_field(
            row1, "📦 المستندات (DN)", 'packing_list',
            "رابط المستندات (DN)", width_ratio=1/3
        )

        # المستندات الخاصة بالجمارك (كانت شهادة المنشأ)
        self.create_document_link_field(
            row1, "🏭 المستندات الخاصة بالجمارك", 'certificate_of_origin',
            "رابط المستندات الخاصة بالجمارك", width_ratio=1/3
        )

        # الصف الثاني - مستندات التأمين وصور الأصناف
        row2 = create_simple_rtl_frame(content_frame)
        row2.pack(fill='x', pady=(30, 30))

        # بوليصة التأمين
        self.create_document_link_field(
            row2, "🛡️ بوليصة التأمين", 'insurance_policy',
            "رابط بوليصة التأمين", width_ratio=1/2
        )

        # صور الأصناف (تم نقلها من الصف الثالث)
        self.create_document_link_field(
            row2, "📸 صور الأصناف", 'customs_declaration',
            "رابط صور الأصناف", width_ratio=1/2
        )



        # الصف الثالث - مستندات إضافية
        row3 = create_simple_rtl_frame(content_frame)
        row3.pack(fill='x', pady=(30, 30))

        # مستندات أخرى
        self.create_document_link_field(
            row3, "📋 مستندات أخرى", 'other_documents',
            "روابط المستندات الأخرى", width_ratio=1/2
        )

        # حالة المستندات
        self.create_documents_status_field(row3, width_ratio=1/2)

        # الصف الرابع - ملاحظات المستندات
        row4 = create_simple_rtl_frame(content_frame)
        row4.pack(fill='x', pady=(30, 0))

        # ملاحظات المستندات
        docs_notes_label = create_simple_rtl_label(
            row4,
            text="📝 ملاحظات حول المستندات:",
            font=('Segoe UI', 14, 'bold')
        )
        docs_notes_label.pack(anchor='e', pady=(0, 10))

        self.documents_notes_text = create_simple_rtl_text(
            row4,
            height=4
        )
        self.documents_notes_text.pack(fill='x', pady=(0, 20))

        # منطقة حالة المستندات
        status_frame = create_simple_rtl_frame(row4)
        status_frame.pack(fill='x', pady=(20, 0))

        # مؤشر اكتمال المستندات
        completion_label = create_simple_rtl_label(
            status_frame,
            text="📊 نسبة اكتمال المستندات:",
            font=('Segoe UI', 14, 'bold')
        )
        completion_label.pack(anchor='e', pady=(0, 10))

        # شريط تقدم المستندات
        docs_progress_frame = create_simple_rtl_frame(status_frame)
        docs_progress_frame.pack(fill='x', pady=(0, 10))

        try:
            self.documents_progress_bar = ttk.Progressbar(
                docs_progress_frame,
                mode='determinate',
                length=400,
                value=0
            )
        except Exception as e:
            self.documents_progress_bar = ttk.Progressbar(
                docs_progress_frame,
                mode='determinate',
                value=0
            )
            print(f"تحذير في Documents Progressbar: {e}")

        self.documents_progress_bar.pack(anchor='e', pady=(0, 10))

        self.documents_progress_label = create_simple_rtl_label(
            docs_progress_frame,
            text="0% مكتمل",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['primary']
        )
        self.documents_progress_label.pack(anchor='e')

        return section

    def create_document_field(self, parent, label_text, var_name, placeholder="", width_ratio=1/3):
        """إنشاء حقل مستند مع تسمية وحالة وزر مرفق"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # إطار التسمية مع زر المرفق
        header_frame = create_simple_rtl_frame(field_frame)
        header_frame.pack(fill='x', pady=(0, 10))

        # زر إضافة مرفق
        print(f"🔧 إنشاء زر المرفق لـ: {label_text} ({var_name})")

        attachment_btn = create_simple_rtl_button(
            header_frame,
            text="📎 إضافة مرفق",
            button_type="secondary",
            command=lambda v=var_name, l=label_text: self.add_attachment(v, l)
        )
        attachment_btn.configure(
            font=('Segoe UI', 9, 'normal'),
            padx=8,
            pady=4
        )
        attachment_btn.pack(side='left', padx=(0, 10))

        print(f"✅ تم إنشاء زر المرفق بنجاح لـ: {label_text}")

        # التسمية
        label = create_simple_rtl_label(
            header_frame,
            text=label_text,
            font=('Segoe UI', 14, 'bold')
        )
        label.pack(side='right', anchor='e')

        # إطار الحقل والحالة
        input_frame = create_simple_rtl_frame(field_frame)
        input_frame.pack(fill='x', pady=(0, 10))

        # الحقل
        self.form_vars[var_name] = tk.StringVar()
        entry = create_simple_rtl_entry(
            input_frame,
            textvariable=self.form_vars[var_name],
            placeholder=placeholder
        )
        entry.configure(font=('Segoe UI', 12, 'normal'))
        entry.pack(fill='x', ipady=8)

        # مؤشر حالة المستند
        status_var_name = f"{var_name}_status"
        self.form_vars[status_var_name] = tk.StringVar()
        status_combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.form_vars[status_var_name]
        )
        status_combo['values'] = ['غير متوفر', 'متوفر', 'مرسل', 'مؤكد', 'مرفوض']
        status_combo.set('غير متوفر')
        status_combo.configure(font=('Segoe UI', 10, 'normal'))
        status_combo.pack(fill='x', ipady=6)

        # إطار عرض المرفقات
        attachments_frame = create_simple_rtl_frame(field_frame)
        attachments_frame.pack(fill='x', pady=(5, 0))

        # تسمية المرفقات (مخفية في البداية)
        attachments_label = create_simple_rtl_label(
            attachments_frame,
            text="",
            font=('Segoe UI', 9, 'italic'),
            fg='#6b7280'
        )
        attachments_label.pack(anchor='e')

        # حفظ مرجع إطار المرفقات
        setattr(self, f'{var_name}_attachments_frame', attachments_frame)
        setattr(self, f'{var_name}_attachments_label', attachments_label)

        # تهيئة قائمة المرفقات
        if not hasattr(self, 'document_attachments'):
            self.document_attachments = {}
        self.document_attachments[var_name] = []

        # ربط تغيير البيانات
        self.form_vars[var_name].trace('w', self.on_document_change)
        self.form_vars[status_var_name].trace('w', self.on_document_change)

        return entry

    def create_document_link_field(self, parent, label_text, var_name, placeholder="", width_ratio=1/3):
        """إنشاء حقل رابط مستند مع تسمية وحالة وزر إدخال الرابط"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # إطار التسمية مع أزرار الإجراءات
        header_frame = create_simple_rtl_frame(field_frame)
        header_frame.pack(fill='x', pady=(0, 10))

        # زر إضافة مرفق
        add_attachment_btn = create_simple_rtl_button(
            header_frame,
            text="📎 إضافة مرفق",
            button_type="primary",
            command=lambda v=var_name, l=label_text: self.add_attachment(v, l)
        )
        add_attachment_btn.configure(
            font=('Segoe UI', 9, 'normal'),
            padx=8,
            pady=4
        )
        add_attachment_btn.pack(side='left', padx=(0, 5))

        # حفظ مرجع الزر لتحديث النص لاحقاً
        setattr(self, f'{var_name}_add_attachment_btn', add_attachment_btn)
        setattr(self, f'{var_name}_document_name', label_text)

        # زر إدخال الرابط
        add_link_btn = create_simple_rtl_button(
            header_frame,
            text="📝 إضافة رابط",
            button_type="secondary",
            command=lambda v=var_name, l=label_text: self.add_document_link(v, l)
        )
        add_link_btn.configure(
            font=('Segoe UI', 9, 'normal'),
            padx=8,
            pady=4
        )
        add_link_btn.pack(side='left', padx=(0, 10))

        print(f"✅ تم إنشاء أزرار الرابط بنجاح لـ: {label_text}")

        # التسمية
        label = create_simple_rtl_label(
            header_frame,
            text=label_text,
            font=('Segoe UI', 14, 'bold')
        )
        label.pack(side='right', anchor='e')

        # إطار الحقل والحالة
        input_frame = create_simple_rtl_frame(field_frame)
        input_frame.pack(fill='x', pady=(0, 10))

        # حقل الرابط (للقراءة فقط)
        self.form_vars[var_name] = tk.StringVar()
        link_entry = create_simple_rtl_entry(
            input_frame,
            textvariable=self.form_vars[var_name],
            placeholder=placeholder or f"رابط {label_text}"
        )
        link_entry.configure(
            font=('Segoe UI', 12, 'normal'),
            fg='#2563eb',  # لون أزرق للروابط
            cursor='hand2',  # مؤشر اليد
            state='readonly'  # للقراءة فقط - لا يسمح بالإدخال اليدوي
        )
        link_entry.pack(fill='x', ipady=8)

        # ربط النقر على الحقل لفتح الرابط
        link_entry.bind('<Button-1>', lambda e, v=var_name, l=label_text: self.open_document_link(v, l))
        link_entry.bind('<Return>', lambda e, v=var_name, l=label_text: self.open_document_link(v, l))

        # مؤشر حالة المستند
        status_var_name = f"{var_name}_status"
        self.form_vars[status_var_name] = tk.StringVar()
        status_combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.form_vars[status_var_name]
        )
        status_combo['values'] = ['غير متوفر', 'متوفر', 'مرسل', 'مؤكد', 'مرفوض']
        status_combo.set('غير متوفر')
        status_combo.configure(font=('Segoe UI', 10, 'normal'))
        status_combo.pack(fill='x', ipady=6)

        # إطار معلومات الرابط
        link_info_frame = create_simple_rtl_frame(field_frame)
        link_info_frame.pack(fill='x', pady=(5, 0))

        # تسمية معلومات الرابط (مخفية في البداية)
        link_info_label = create_simple_rtl_label(
            link_info_frame,
            text="",
            font=('Segoe UI', 9, 'italic'),
            fg='#6b7280'
        )
        link_info_label.pack(anchor='e')

        # حفظ مرجع إطار معلومات الرابط
        setattr(self, f'{var_name}_link_info_frame', link_info_frame)
        setattr(self, f'{var_name}_link_info_label', link_info_label)

        # تهيئة قاموس الروابط
        if not hasattr(self, 'document_links'):
            self.document_links = {}
        self.document_links[var_name] = ""

        # ربط تغيير البيانات
        self.form_vars[var_name].trace('w', self.on_document_change)
        self.form_vars[status_var_name].trace('w', self.on_document_change)

        return link_entry

    def add_attachment(self, var_name, document_name):
        """إضافة مرفق أو استعراض المرفقات الموجودة"""
        try:
            print(f"🔍 محاولة إضافة/استعراض مرفق لـ: {document_name} ({var_name})")

            # التأكد من وجود قاموس المرفقات
            if not hasattr(self, 'document_attachments'):
                self.document_attachments = {}
            if var_name not in self.document_attachments:
                self.document_attachments[var_name] = []

            # التحقق من وجود مرفقات
            existing_attachments = self.document_attachments.get(var_name, [])

            if existing_attachments:
                # إذا كانت هناك مرفقات موجودة، عرض نافذة الاستعراض
                print(f"📎 توجد {len(existing_attachments)} مرفقات، عرض نافذة الاستعراض...")
                self.show_attachments_browser(var_name, document_name)
            else:
                # إذا لم توجد مرفقات، إضافة مرفق جديد
                print("📂 لا توجد مرفقات، فتح نافذة إضافة مرفق...")
                self.add_new_attachment(var_name, document_name)

        except Exception as e:
            print(f"❌ خطأ في معالجة المرفق: {str(e)}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"خطأ في معالجة المرفق: {str(e)}")

    def add_new_attachment(self, var_name, document_name):
        """إضافة مرفق جديد"""
        try:
            # فتح نافذة اختيار الملف
            file_types = [
                ("جميع الملفات المدعومة", "*.pdf *.jpg *.jpeg *.png *.doc *.docx *.xls *.xlsx"),
                ("ملفات PDF", "*.pdf"),
                ("ملفات الصور", "*.jpg *.jpeg *.png *.gif *.bmp"),
                ("ملفات Word", "*.doc *.docx"),
                ("ملفات Excel", "*.xls *.xlsx"),
                ("جميع الملفات", "*.*")
            ]

            print("📂 فتح نافذة اختيار الملف...")
            file_path = filedialog.askopenfilename(
                title=f"اختيار مرفق لـ {document_name}",
                filetypes=file_types,
                initialdir=os.getcwd()
            )

            print(f"📁 الملف المختار: {file_path}")

            if file_path and file_path.strip():
                print("✅ تم اختيار ملف، بدء المعالجة...")

                # إضافة الملف لقائمة المرفقات
                file_name = os.path.basename(file_path)
                file_size = os.path.getsize(file_path)

                print(f"📄 اسم الملف: {file_name}")
                print(f"📏 حجم الملف: {file_size} بايت")

                # تحويل حجم الملف لوحدة مناسبة
                if file_size < 1024:
                    size_str = f"{file_size} بايت"
                elif file_size < 1024 * 1024:
                    size_str = f"{file_size / 1024:.1f} كيلوبايت"
                else:
                    size_str = f"{file_size / (1024 * 1024):.1f} ميجابايت"

                from datetime import datetime
                attachment_info = {
                    'path': file_path,
                    'name': file_name,
                    'size': file_size,
                    'size_str': size_str,
                    'added_date': datetime.now().strftime("%Y-%m-%d %H:%M")
                }

                # إضافة للقائمة
                self.document_attachments[var_name].append(attachment_info)
                print(f"📎 تم إضافة المرفق للقائمة. العدد الحالي: {len(self.document_attachments[var_name])}")

                # تحديث عرض المرفقات
                self.update_attachments_display(var_name)
                self.update_attachment_button_text(var_name)

                # إظهار رسالة نجاح
                messagebox.showinfo(
                    "تم إضافة المرفق",
                    f"تم إضافة الملف '{file_name}' بنجاح\nالحجم: {size_str}"
                )
                print("✅ تم إضافة المرفق بنجاح!")

            else:
                print("❌ لم يتم اختيار ملف أو تم إلغاء العملية")

        except Exception as e:
            print(f"❌ خطأ في إضافة المرفق: {str(e)}")
            import traceback
            traceback.print_exc()

            messagebox.showerror(
                "خطأ في إضافة المرفق",
                f"حدث خطأ أثناء إضافة المرفق:\n{str(e)}\n\nتأكد من:\n• وجود الملف\n• صلاحيات القراءة\n• مساحة كافية"
            )

    def show_attachments_browser(self, var_name, document_name):
        """عرض نافذة استعراض المرفقات مع إمكانية الإضافة والحذف والفتح"""
        try:
            attachments = self.document_attachments.get(var_name, [])
            if not attachments:
                messagebox.showinfo("لا توجد مرفقات", f"لا توجد مرفقات لـ {document_name}")
                return

            # إنشاء نافذة الاستعراض
            browser_window = tk.Toplevel(self.root)
            browser_window.title(f"استعراض مرفقات - {document_name}")
            browser_window.geometry("800x600")
            browser_window.configure(bg='white')
            browser_window.transient(self.root)
            browser_window.grab_set()

            # إطار العنوان
            title_frame = tk.Frame(browser_window, bg='#2563eb', height=60)
            title_frame.pack(fill='x')
            title_frame.pack_propagate(False)

            title_label = tk.Label(
                title_frame,
                text=f"📎 مرفقات {document_name}",
                font=('Segoe UI', 16, 'bold'),
                bg='#2563eb',
                fg='white'
            )
            title_label.pack(expand=True)

            # إطار المحتوى
            content_frame = tk.Frame(browser_window, bg='white')
            content_frame.pack(fill='both', expand=True, padx=20, pady=20)

            # إطار الأزرار العلوية
            buttons_frame = tk.Frame(content_frame, bg='white')
            buttons_frame.pack(fill='x', pady=(0, 15))

            # زر إضافة مرفق جديد
            add_btn = tk.Button(
                buttons_frame,
                text="➕ إضافة مرفق جديد",
                font=('Segoe UI', 11, 'bold'),
                bg='#10b981',
                fg='white',
                relief='flat',
                padx=15,
                pady=8,
                cursor='hand2',
                command=lambda: self.add_attachment_from_browser(var_name, document_name, browser_window)
            )
            add_btn.pack(side='right', padx=(10, 0))

            # معلومات المرفقات
            info_label = tk.Label(
                buttons_frame,
                text=f"العدد الإجمالي: {len(attachments)} مرفق",
                font=('Segoe UI', 12, 'bold'),
                bg='white',
                fg='#374151'
            )
            info_label.pack(side='left')

            # إطار قائمة المرفقات مع شريط التمرير
            list_frame = tk.Frame(content_frame, bg='white')
            list_frame.pack(fill='both', expand=True)

            # شريط التمرير
            scrollbar = tk.Scrollbar(list_frame)
            scrollbar.pack(side='right', fill='y')

            # قائمة المرفقات
            attachments_listbox = tk.Listbox(
                list_frame,
                font=('Segoe UI', 11),
                bg='#f9fafb',
                fg='#374151',
                selectbackground='#dbeafe',
                selectforeground='#1e40af',
                relief='flat',
                bd=1,
                yscrollcommand=scrollbar.set
            )
            attachments_listbox.pack(fill='both', expand=True)
            scrollbar.config(command=attachments_listbox.yview)

            # إضافة المرفقات للقائمة
            for i, attachment in enumerate(attachments):
                display_text = f"📄 {attachment['name']} ({attachment['size_str']})"
                if 'added_date' in attachment:
                    display_text += f" - {attachment['added_date']}"
                attachments_listbox.insert(tk.END, display_text)

            # إطار أزرار الإجراءات
            actions_frame = tk.Frame(content_frame, bg='white')
            actions_frame.pack(fill='x', pady=(15, 0))

            # زر فتح المرفق
            open_btn = tk.Button(
                actions_frame,
                text="📂 فتح المرفق",
                font=('Segoe UI', 11, 'bold'),
                bg='#3b82f6',
                fg='white',
                relief='flat',
                padx=15,
                pady=8,
                cursor='hand2',
                command=lambda: self.open_selected_attachment(attachments_listbox, attachments)
            )
            open_btn.pack(side='left', padx=(0, 10))

            # زر حذف المرفق
            delete_btn = tk.Button(
                actions_frame,
                text="🗑️ حذف المرفق",
                font=('Segoe UI', 11, 'bold'),
                bg='#ef4444',
                fg='white',
                relief='flat',
                padx=15,
                pady=8,
                cursor='hand2',
                command=lambda: self.delete_selected_attachment(attachments_listbox, var_name, browser_window)
            )
            delete_btn.pack(side='left', padx=(0, 10))

            # زر إغلاق
            close_btn = tk.Button(
                actions_frame,
                text="❌ إغلاق",
                font=('Segoe UI', 11, 'bold'),
                bg='#6b7280',
                fg='white',
                relief='flat',
                padx=15,
                pady=8,
                cursor='hand2',
                command=browser_window.destroy
            )
            close_btn.pack(side='right')

            # ربط النقر المزدوج لفتح المرفق
            attachments_listbox.bind('<Double-1>',
                lambda e: self.open_selected_attachment(attachments_listbox, attachments))

            # تركيز النافذة
            browser_window.focus_set()

        except Exception as e:
            print(f"❌ خطأ في عرض نافذة الاستعراض: {e}")
            messagebox.showerror("خطأ", f"خطأ في عرض المرفقات: {str(e)}")

    def update_attachments_display(self, var_name):
        """تحديث عرض المرفقات لمستند معين"""
        try:
            print(f"🔄 تحديث عرض المرفقات لـ: {var_name}")

            attachments = self.document_attachments.get(var_name, [])
            attachments_label = getattr(self, f'{var_name}_attachments_label', None)

            print(f"📊 عدد المرفقات: {len(attachments)}")
            print(f"🏷️ تسمية العرض موجودة: {attachments_label is not None}")

            if attachments_label:
                if attachments:
                    # عرض عدد المرفقات
                    count = len(attachments)
                    if count == 1:
                        text = f"📎 مرفق واحد: {attachments[0]['name']}"
                    else:
                        text = f"📎 {count} مرفقات"

                    print(f"📝 النص المعروض: {text}")
                    attachments_label.configure(text=text)

                    # إضافة tooltip مع تفاصيل المرفقات
                    tooltip_text = "\n".join([
                        f"• {att['name']} ({att['size_str']})"
                        for att in attachments
                    ])

                    # ربط حدث النقر لعرض التفاصيل
                    attachments_label.bind(
                        "<Button-1>",
                        lambda e: self.show_attachments_details(var_name)
                    )
                    attachments_label.configure(cursor="hand2")

                else:
                    attachments_label.configure(text="")
                    attachments_label.unbind("<Button-1>")
                    attachments_label.configure(cursor="")

        except Exception as e:
            print(f"خطأ في تحديث عرض المرفقات: {e}")

    def add_attachment_from_browser(self, var_name, document_name, browser_window):
        """إضافة مرفق جديد من نافذة الاستعراض"""
        try:
            # إضافة مرفق جديد
            self.add_new_attachment(var_name, document_name)

            # إغلاق النافذة الحالية وإعادة فتحها لتحديث القائمة
            browser_window.destroy()

            # إعادة فتح نافذة الاستعراض إذا تم إضافة مرفق
            if self.document_attachments.get(var_name, []):
                self.show_attachments_browser(var_name, document_name)

        except Exception as e:
            print(f"❌ خطأ في إضافة مرفق من المتصفح: {e}")
            messagebox.showerror("خطأ", f"خطأ في إضافة المرفق: {str(e)}")

    def open_selected_attachment(self, listbox, attachments):
        """فتح المرفق المحدد"""
        try:
            selection = listbox.curselection()
            if not selection:
                messagebox.showwarning("تحديد مطلوب", "يرجى تحديد مرفق لفتحه")
                return

            index = selection[0]
            if index < len(attachments):
                attachment = attachments[index]
                file_path = attachment['path']

                # التحقق من وجود الملف
                if not os.path.exists(file_path):
                    messagebox.showerror("ملف غير موجود", f"الملف غير موجود:\n{file_path}")
                    return

                # فتح الملف
                import subprocess
                import platform

                if platform.system() == 'Windows':
                    os.startfile(file_path)
                elif platform.system() == 'Darwin':  # macOS
                    subprocess.call(['open', file_path])
                else:  # Linux
                    subprocess.call(['xdg-open', file_path])

                print(f"✅ تم فتح الملف: {attachment['name']}")

        except Exception as e:
            print(f"❌ خطأ في فتح المرفق: {e}")
            messagebox.showerror("خطأ في فتح الملف", f"خطأ في فتح الملف:\n{str(e)}")

    def delete_selected_attachment(self, listbox, var_name, browser_window):
        """حذف المرفق المحدد"""
        try:
            selection = listbox.curselection()
            if not selection:
                messagebox.showwarning("تحديد مطلوب", "يرجى تحديد مرفق لحذفه")
                return

            index = selection[0]
            attachments = self.document_attachments.get(var_name, [])

            if index < len(attachments):
                attachment = attachments[index]

                # تأكيد الحذف
                result = messagebox.askyesno(
                    "تأكيد الحذف",
                    f"هل أنت متأكد من حذف المرفق:\n{attachment['name']}؟"
                )

                if result:
                    # حذف المرفق من القائمة
                    del attachments[index]

                    # تحديث العرض
                    self.update_attachments_display(var_name)
                    self.update_attachment_button_text(var_name)

                    # إغلاق النافذة وإعادة فتحها إذا كانت هناك مرفقات متبقية
                    browser_window.destroy()
                    if attachments:  # إذا كانت هناك مرفقات متبقية
                        document_name = getattr(self, f'{var_name}_document_name', 'المستند')
                        self.show_attachments_browser(var_name, document_name)

                    messagebox.showinfo("تم الحذف", f"تم حذف المرفق '{attachment['name']}' بنجاح")
                    print(f"✅ تم حذف المرفق: {attachment['name']}")

        except Exception as e:
            print(f"❌ خطأ في حذف المرفق: {e}")
            messagebox.showerror("خطأ في الحذف", f"خطأ في حذف المرفق:\n{str(e)}")

    def update_attachment_button_text(self, var_name):
        """تحديث نص زر المرفق حسب حالة المرفقات"""
        try:
            attachments = self.document_attachments.get(var_name, [])

            # البحث عن الزر المرتبط بهذا المتغير
            for widget_name in dir(self):
                if widget_name.endswith('_add_attachment_btn') and var_name in widget_name:
                    button = getattr(self, widget_name, None)
                    if button and hasattr(button, 'configure'):
                        if attachments:
                            # إذا كانت هناك مرفقات، غيّر النص لاستعراض
                            button.configure(text=f"📎 استعراض المرفقات ({len(attachments)})")
                        else:
                            # إذا لم توجد مرفقات، أعد النص الأصلي
                            button.configure(text="📎 إضافة مرفق")
                        break

        except Exception as e:
            print(f"❌ خطأ في تحديث نص الزر: {e}")

    def show_attachments_details(self, var_name):
        """عرض تفاصيل المرفقات لمستند معين"""
        try:
            attachments = self.document_attachments.get(var_name, [])
            if not attachments:
                return

            from tkinter import messagebox

            # إنشاء نص التفاصيل
            details = f"المرفقات ({len(attachments)}):\n\n"
            for i, att in enumerate(attachments, 1):
                details += f"{i}. {att['name']}\n"
                details += f"   الحجم: {att['size_str']}\n"
                details += f"   المسار: {att['path']}\n\n"

            details += "\n💡 يمكنك فتح الملفات من مجلد المشروع"

            messagebox.showinfo("تفاصيل المرفقات", details)

        except Exception as e:
            from tkinter import messagebox
            messagebox.showerror("خطأ", f"خطأ في عرض تفاصيل المرفقات: {str(e)}")

    def create_documents_status_field(self, parent, width_ratio=1/2):
        """إنشاء حقل حالة المستندات العامة"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text="📊 حالة المستندات العامة",
            font=('Segoe UI', 14, 'bold')
        )
        label.pack(anchor='e', pady=(0, 10))

        # القائمة المنسدلة
        self.form_vars['documents_status'] = tk.StringVar()
        status_combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.form_vars['documents_status']
        )
        status_combo['values'] = [
            'غير مكتملة',
            'قيد المراجعة',
            'مكتملة جزئياً',
            'مكتملة',
            'مؤكدة',
            'مرفوضة'
        ]
        status_combo.set('غير مكتملة')
        status_combo.configure(font=('Segoe UI', 12, 'normal'))
        status_combo.pack(fill='x', ipady=12)

        # ربط تغيير البيانات
        self.form_vars['documents_status'].trace('w', self.on_document_change)

        return status_combo

    def create_tracking_section(self):
        """إنشاء قسم التتبع والحالة"""
        section = create_simple_rtl_frame(self.sections_frame)

        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = create_simple_rtl_label(
            title_frame,
            text="📊 التتبع والحالة المتقدمة",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')

        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="معلومات التتبع والموقع الحالي ونسبة التقدم",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))

        # محتوى القسم
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)

        # الصف الأول - معلومات التتبع
        row1 = create_simple_rtl_frame(content_frame)
        row1.pack(fill='x', pady=(0, 30))

        # الموقع الحالي
        self.create_field_group(
            row1, "📍 الموقع الحالي", 'current_location',
            placeholder="الموقع الحالي للشحنة", width_ratio=1/2
        )

        # آخر تحديث
        self.create_datetime_display(row1, width_ratio=1/2)

        # الصف الثاني - شريط التقدم
        row2 = create_simple_rtl_frame(content_frame)
        row2.pack(fill='x', pady=(30, 30))

        # شريط التقدم
        progress_label = create_simple_rtl_label(
            row2,
            text="📊 نسبة التقدم:",
            font=('Segoe UI', 14, 'bold')
        )
        progress_label.pack(anchor='e', pady=(0, 10))

        progress_frame = create_simple_rtl_frame(row2)
        progress_frame.pack(fill='x', pady=(0, 20))

        try:
            self.progress_bar = ttk.Progressbar(
                progress_frame,
                mode='determinate',
                length=600,
                value=0
            )
        except Exception as e:
            # في حالة الخطأ، إنشاء شريط تقدم بسيط
            self.progress_bar = ttk.Progressbar(
                progress_frame,
                mode='determinate',
                value=0
            )
            print(f"تحذير في Progressbar: {e}")
        self.progress_bar.pack(anchor='e', pady=(0, 10))

        self.progress_label = create_simple_rtl_label(
            progress_frame,
            text="0%",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['primary']
        )
        self.progress_label.pack(anchor='e')

        # الصف الثالث - معلومات إضافية
        row3 = create_simple_rtl_frame(content_frame)
        row3.pack(fill='x', pady=(30, 0))

        # الأيام المتبقية
        self.create_calculated_field(
            row3, "⏰ الأيام المتبقية", 'days_remaining', width_ratio=1/3
        )

        # نسبة التأخير
        self.create_calculated_field(
            row3, "⚠️ نسبة التأخير", 'delay_percentage', width_ratio=1/3
        )

        # تقييم الأداء
        self.create_calculated_field(
            row3, "⭐ تقييم الأداء", 'performance_rating', width_ratio=1/3
        )

        return section

    def create_notes_section(self):
        """إنشاء قسم الملاحظات والمرفقات"""
        section = create_simple_rtl_frame(self.sections_frame)

        # عنوان القسم
        title_frame = create_simple_rtl_frame(section)
        title_frame.pack(fill='x', pady=(0, 30))

        title_label = create_simple_rtl_label(
            title_frame,
            text="📝 الملاحظات والمرفقات",
            font=('Segoe UI', 24, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e')

        subtitle_label = create_simple_rtl_label(
            title_frame,
            text="ملاحظات إضافية ومرفقات ووثائق الشحنة",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['text_secondary']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))

        # محتوى القسم
        content_frame = create_simple_rtl_frame(section)
        content_frame.pack(fill='both', expand=True, padx=50)

        # منطقة الملاحظات
        notes_frame = create_simple_rtl_frame(content_frame)
        notes_frame.pack(fill='x', pady=(0, 30))

        notes_label = create_simple_rtl_label(
            notes_frame,
            text="📝 ملاحظات إضافية:",
            font=('Segoe UI', 14, 'bold')
        )
        notes_label.pack(anchor='e', pady=(0, 10))

        self.notes_text = create_simple_rtl_text(
            notes_frame,
            height=8
        )
        self.notes_text.pack(fill='x', pady=(0, 20))

        # منطقة المرفقات
        attachments_frame = create_simple_rtl_frame(content_frame)
        attachments_frame.pack(fill='both', expand=True)

        attachments_label = create_simple_rtl_label(
            attachments_frame,
            text="📎 المرفقات والوثائق:",
            font=('Segoe UI', 14, 'bold')
        )
        attachments_label.pack(anchor='e', pady=(0, 10))

        # أزرار المرفقات
        attachments_buttons = create_simple_rtl_frame(attachments_frame)
        attachments_buttons.pack(fill='x', pady=(0, 10))

        add_file_btn = create_simple_rtl_button(
            attachments_buttons,
            text="📎 إضافة ملف",
            button_type="secondary",
            command=self.add_general_attachment
        )
        add_file_btn.pack(side='right', padx=5)

        view_files_btn = create_simple_rtl_button(
            attachments_buttons,
            text="👁️ عرض الملفات",
            button_type="ghost",
            command=self.view_attachments
        )
        view_files_btn.pack(side='right', padx=5)

        remove_file_btn = create_simple_rtl_button(
            attachments_buttons,
            text="🗑️ حذف ملف",
            button_type="danger",
            command=self.remove_attachment
        )
        remove_file_btn.pack(side='right', padx=5)

        # قائمة المرفقات
        try:
            self.attachments_list = tk.Listbox(
                attachments_frame,
                height=6,
                font=('Segoe UI', 12),
                bg=COLORS['surface'],
                fg=COLORS['text_primary'],
                selectbackground=COLORS['primary_light'],
                relief='solid',
                bd=2
            )
        except Exception as e:
            # في حالة الخطأ، إنشاء قائمة بسيطة
            self.attachments_list = tk.Listbox(
                attachments_frame,
                height=6
            )
            print(f"تحذير في Listbox: {e}")
        self.attachments_list.pack(fill='both', expand=True, pady=(10, 0))

        return section

    def create_footer_content(self):
        """إنشاء محتوى شريط الحالة"""
        # استخدام الإطار المُنشأ مسبقاً
        footer_frame = self.footer_frame

        # الجانب الأيمن - أزرار الإجراءات الرئيسية
        actions_frame = create_simple_rtl_frame(footer_frame)
        actions_frame.configure(bg=COLORS['light'])
        actions_frame.pack(side='right', padx=40, pady=20)

        if self.mode != 'view':
            # زر الحفظ الرئيسي
            save_btn = create_simple_rtl_button(
                actions_frame,
                text="💾 حفظ الشحنة",
                button_type="success",
                command=self.save_shipment
            )
            save_btn.configure(font=('Segoe UI', 14, 'bold'), padx=30, pady=15)
            save_btn.pack(side='right', padx=10)

            # زر الحفظ والإغلاق
            save_close_btn = create_simple_rtl_button(
                actions_frame,
                text="💾 حفظ وإغلاق",
                button_type="primary",
                command=self.save_and_close
            )
            save_close_btn.configure(font=('Segoe UI', 14, 'bold'), padx=30, pady=15)
            save_close_btn.pack(side='right', padx=10)

            # زر معلومات الحالة
            status_info_btn = create_simple_rtl_button(
                actions_frame,
                text="ℹ️ معلومات الحالة",
                button_type="info",
                command=self.show_status_info
            )
            status_info_btn.configure(font=('Segoe UI', 12, 'bold'), padx=20, pady=15)
            status_info_btn.pack(side='right', padx=10)

        # زر الإغلاق
        close_btn = create_simple_rtl_button(
            actions_frame,
            text="❌ إغلاق",
            button_type="danger",
            command=self.close_form
        )
        close_btn.configure(font=('Segoe UI', 14, 'bold'), padx=30, pady=15)
        close_btn.pack(side='right', padx=10)

        # الوسط - أزرار التنقل بين الأقسام
        nav_frame = create_simple_rtl_frame(footer_frame)
        nav_frame.configure(bg=COLORS['light'])
        nav_frame.pack(expand=True, pady=20)

        # زر القسم السابق
        self.prev_section_btn = create_simple_rtl_button(
            nav_frame,
            text="◀ القسم السابق",
            button_type="outline",
            command=self.prev_section
        )
        self.prev_section_btn.configure(font=('Segoe UI', 12, 'bold'), padx=20, pady=10)
        self.prev_section_btn.pack(side='right', padx=10)

        # مؤشر القسم الحالي
        self.section_indicator = create_simple_rtl_label(
            nav_frame,
            text="القسم 1 من 8",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['primary'],
            bg=COLORS['light']
        )
        self.section_indicator.pack(side='right', padx=20)

        # زر القسم التالي
        self.next_section_btn = create_simple_rtl_button(
            nav_frame,
            text="القسم التالي ▶",
            button_type="outline",
            command=self.next_section
        )
        self.next_section_btn.configure(font=('Segoe UI', 12, 'bold'), padx=20, pady=10)
        self.next_section_btn.pack(side='right', padx=10)

        # الجانب الأيسر - معلومات الحالة
        status_frame = create_simple_rtl_frame(footer_frame)
        status_frame.configure(bg=COLORS['light'])
        status_frame.pack(side='left', padx=40, pady=20)

        self.status_label = create_simple_rtl_label(
            status_frame,
            text="جاهز",
            font=('Segoe UI', 12, 'normal'),
            bg=COLORS['light']
        )
        self.status_label.pack(side='left')

        # مؤشر الأقسام المطلوبة
        self.sections_indicator = create_simple_rtl_label(
            status_frame,
            text="",
            font=('Segoe UI', 10, 'normal'),
            bg=COLORS['light'],
            fg=COLORS['info']
        )
        self.sections_indicator.pack(side='left', padx=(20, 0))

        # مؤشر التعديل
        self.modified_indicator = create_simple_rtl_label(
            status_frame,
            text="",
            font=('Segoe UI', 12, 'bold'),
            bg=COLORS['light']
        )
        self.modified_indicator.pack(side='left', padx=(20, 0))

    # وظائف إنشاء الحقول المساعدة
    def create_field_group(self, parent, label_text, var_name, placeholder="", required=False, width_ratio=1/3):
        """إنشاء مجموعة حقل مع تسمية"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 14, 'bold')
        )
        if required:
            label.configure(fg=COLORS['danger'])
        label.pack(anchor='e', pady=(0, 10))

        # الحقل
        self.form_vars[var_name] = tk.StringVar()
        entry = create_simple_rtl_entry(
            field_frame,
            textvariable=self.form_vars[var_name],
            placeholder=placeholder
        )
        entry.configure(font=('Segoe UI', 12, 'normal'))
        entry.pack(fill='x', ipady=12)

        # ربط تغيير البيانات
        self.form_vars[var_name].trace('w', self.on_data_change)

        return entry

    def create_combobox_field(self, parent, label_text, var_name, values=[], width_ratio=1/3):
        """إنشاء حقل قائمة منسدلة"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 14, 'bold')
        )
        label.pack(anchor='e', pady=(0, 10))

        # القائمة المنسدلة
        self.form_vars[var_name] = tk.StringVar()
        combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.form_vars[var_name]
        )
        combo['values'] = values
        combo.configure(font=('Segoe UI', 12, 'normal'))
        combo.pack(fill='x', ipady=12)

        # ربط تغيير البيانات
        self.form_vars[var_name].trace('w', self.on_data_change)

        return combo

    def create_date_field(self, parent, label_text, var_name, placeholder="", required=False, width_ratio=1/3):
        """إنشاء حقل تاريخ مع زر اختيار التاريخ"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 14, 'bold')
        )
        if required:
            label.configure(fg=COLORS['danger'])
        label.pack(anchor='e', pady=(0, 10))

        # إطار الحقل مع الزر
        entry_frame = create_simple_rtl_frame(field_frame)
        entry_frame.pack(fill='x')

        # زر اختيار التاريخ
        date_btn = create_simple_rtl_button(
            entry_frame,
            text="📅",
            button_type="secondary",
            command=lambda: self.open_date_picker(var_name)
        )
        date_btn.configure(
            font=('Segoe UI', 12, 'bold'),
            width=3,
            padx=5,
            pady=8
        )
        date_btn.pack(side='left', padx=(0, 10))

        # حقل إدخال التاريخ
        self.form_vars[var_name] = tk.StringVar()
        entry = create_simple_rtl_entry(
            entry_frame,
            textvariable=self.form_vars[var_name],
            placeholder=placeholder
        )
        entry.configure(font=('Segoe UI', 12, 'normal'))
        entry.pack(fill='x', ipady=12)

        # ربط تغيير البيانات
        self.form_vars[var_name].trace('w', self.on_data_change)

        return entry

    def create_simple_item_input_form(self, parent):
        """إنشاء نموذج إدخال الأصناف المبسط والمباشر"""
        # إطار النموذج المبسط
        form_frame = create_simple_rtl_frame(parent)
        form_frame.pack(fill='x', pady=(0, 20))

        # عنوان النموذج
        form_title = create_simple_rtl_label(
            form_frame,
            text="➕ إضافة صنف جديد - إدخال مباشر",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['primary']
        )
        form_title.pack(anchor='e', pady=(0, 15))

        # إطار الحقول الأساسية
        fields_frame = create_simple_rtl_frame(form_frame)
        fields_frame.pack(fill='x', pady=(0, 15))

        # الصف الأول - كود الصنف واسم الصنف
        row1 = create_simple_rtl_frame(fields_frame)
        row1.pack(fill='x', pady=(0, 10))

        # كود الصنف
        self.simple_item_code_entry = self.create_simple_input_field(
            row1, "🔢 كود الصنف *", 'simple_item_code',
            placeholder="مثال: ITM001", width_ratio=1/3
        )

        # اسم الصنف
        self.simple_item_name_entry = self.create_simple_input_field(
            row1, "📦 اسم الصنف *", 'simple_item_name',
            placeholder="مثال: لابتوب ديل", width_ratio=1/3
        )

        # الكمية
        self.simple_quantity_entry = self.create_simple_input_field(
            row1, "📊 الكمية *", 'simple_quantity',
            placeholder="مثال: 10", width_ratio=1/3
        )

        # الصف الثاني - الوحدة وسعر الوحدة والمخزن
        row2 = create_simple_rtl_frame(fields_frame)
        row2.pack(fill='x', pady=(0, 10))

        # الوحدة
        self.simple_unit_combo = self.create_simple_combobox_field(
            row2, "📏 الوحدة *", 'simple_unit',
            values=['قطعة', 'كيلو', 'متر', 'لتر', 'طن', 'صندوق', 'كرتون'],
            width_ratio=1/3
        )

        # سعر الوحدة
        self.simple_unit_price_entry = self.create_simple_input_field(
            row2, "💰 سعر الوحدة *", 'simple_unit_price',
            placeholder="مثال: 1500.00", width_ratio=1/3
        )

        # المخزن المستهدف
        self.simple_warehouse_combo = self.create_simple_warehouse_field(
            row2, "🏪 المخزن المستهدف *", 'simple_warehouse', width_ratio=1/3
        )

        # الصف الثالث - الوصف والإجمالي
        row3 = create_simple_rtl_frame(fields_frame)
        row3.pack(fill='x', pady=(0, 15))

        # الوصف
        self.simple_description_entry = self.create_simple_input_field(
            row3, "📝 الوصف", 'simple_description',
            placeholder="وصف اختياري للصنف", width_ratio=2/3
        )

        # الإجمالي (محسوب تلقائياً)
        self.simple_total_entry = self.create_simple_calculated_field(
            row3, "💯 الإجمالي", 'simple_total', width_ratio=1/3
        )

        # شريط الإجراءات
        actions_frame = create_simple_rtl_frame(form_frame)
        actions_frame.pack(fill='x')

        # تلميح الاستخدام
        hint_label = create_simple_rtl_label(
            actions_frame,
            text="💡 اضغط Enter لإضافة الصنف | Tab للانتقال بين الحقول | F9 للبحث في الأصناف",
            font=('Segoe UI', 11, 'normal'),
            fg=COLORS['text_secondary']
        )
        hint_label.pack(anchor='e', pady=(5, 0))

        # ربط الأحداث
        self.setup_simple_form_events()

    def create_simple_input_field(self, parent, label_text, var_name, placeholder="", width_ratio=1/3):
        """إنشاء حقل إدخال بسيط"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=10)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 12, 'bold')
        )
        label.pack(anchor='e', pady=(0, 5))

        # متغير الحقل
        if var_name not in self.form_vars:
            self.form_vars[var_name] = tk.StringVar()

        # حقل الإدخال
        entry = create_simple_rtl_entry(
            field_frame,
            textvariable=self.form_vars[var_name],
            font=('Segoe UI', 12)
        )
        entry.pack(fill='x', ipady=8)

        # إضافة placeholder
        if placeholder:
            entry.insert(0, placeholder)
            entry.configure(fg=COLORS['text_muted'])

            def on_focus_in(event):
                if entry.get() == placeholder:
                    entry.delete(0, tk.END)
                    entry.configure(fg=COLORS['text_primary'])

            def on_focus_out(event):
                if not entry.get():
                    entry.insert(0, placeholder)
                    entry.configure(fg=COLORS['text_muted'])

            entry.bind('<FocusIn>', on_focus_in)
            entry.bind('<FocusOut>', on_focus_out)

        return entry

    def create_simple_combobox_field(self, parent, label_text, var_name, values=[], width_ratio=1/3):
        """إنشاء حقل قائمة منسدلة بسيط"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=10)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 12, 'bold')
        )
        label.pack(anchor='e', pady=(0, 5))

        # متغير الحقل
        if var_name not in self.form_vars:
            self.form_vars[var_name] = tk.StringVar()

        # القائمة المنسدلة
        combo = ttk.Combobox(
            field_frame,
            textvariable=self.form_vars[var_name],
            values=values,
            font=('Segoe UI', 12),
            state='readonly'
        )
        combo.pack(fill='x', ipady=8)

        # تعيين القيمة الافتراضية
        if values:
            combo.set(values[0])

        return combo

    def create_simple_warehouse_field(self, parent, label_text, var_name, width_ratio=1/3):
        """إنشاء حقل المخازن البسيط"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=10)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 12, 'bold')
        )
        label.pack(anchor='e', pady=(0, 5))

        # متغير الحقل
        if var_name not in self.form_vars:
            self.form_vars[var_name] = tk.StringVar()

        # تحميل المخازن
        warehouses = self.load_warehouses_for_combo()

        # القائمة المنسدلة
        combo = ttk.Combobox(
            field_frame,
            textvariable=self.form_vars[var_name],
            values=warehouses,
            font=('Segoe UI', 12),
            state='readonly'
        )
        combo.pack(fill='x', ipady=8)

        return combo

    def create_simple_calculated_field(self, parent, label_text, var_name, width_ratio=1/3):
        """إنشاء حقل محسوب تلقائياً بسيط"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=10)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 12, 'bold')
        )
        label.pack(anchor='e', pady=(0, 5))

        # متغير الحقل
        if var_name not in self.form_vars:
            self.form_vars[var_name] = tk.StringVar()
            self.form_vars[var_name].set("0.00")

        # حقل الإدخال (للقراءة فقط)
        entry = create_simple_rtl_entry(
            field_frame,
            textvariable=self.form_vars[var_name],
            font=('Segoe UI', 12, 'bold'),
            state='readonly'
        )
        entry.pack(fill='x', ipady=8)
        entry.configure(bg=COLORS['background'], fg=COLORS['primary'])

        return entry

    def load_warehouses_for_combo(self):
        """تحميل قائمة المخازن للقائمة المنسدلة"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT id, warehouse_name FROM warehouses WHERE is_active = 1 ORDER BY warehouse_name")
            warehouses = cursor.fetchall()
            conn.close()

            # تنسيق القائمة: "ID - اسم المخزن"
            warehouse_list = []
            for warehouse in warehouses:
                warehouse_list.append(f"{warehouse['id']} - {warehouse['warehouse_name']}")

            return warehouse_list
        except Exception as e:
            print(f"خطأ في تحميل المخازن: {e}")
            return ["1 - المخزن الرئيسي"]

    def setup_simple_form_events(self):
        """إعداد أحداث النموذج المبسط"""
        # ربط Enter لإضافة الصنف (فقط في الحقول الأساسية)
        for entry in [self.simple_item_code_entry, self.simple_item_name_entry,
                     self.simple_quantity_entry, self.simple_unit_price_entry]:
            entry.bind('<Return>', self.add_simple_item)

        # ربط Enter في حقل الوصف للانتقال لحقل كود الصنف (للإدخال التالي)
        self.simple_description_entry.bind('<Return>', lambda e: self.simple_item_code_entry.focus())

        # ربط F9 للبحث في الأصناف
        for entry in [self.simple_item_code_entry, self.simple_item_name_entry]:
            entry.bind('<F9>', self.show_item_search)

        # ربط حساب الإجمالي التلقائي
        self.form_vars['simple_quantity'].trace('w', self.calculate_simple_total)
        self.form_vars['simple_unit_price'].trace('w', self.calculate_simple_total)

        # ربط التنقل التلقائي بين الحقول عند الانتهاء من الإدخال
        self.simple_item_code_entry.bind('<FocusOut>', self.auto_fill_item_data)

        # ربط Tab للانتقال المنطقي بين الحقول
        self.simple_item_code_entry.bind('<Tab>', lambda e: self.focus_next_field(self.simple_item_name_entry))
        self.simple_item_name_entry.bind('<Tab>', lambda e: self.focus_next_field(self.simple_quantity_entry))
        self.simple_quantity_entry.bind('<Tab>', lambda e: self.focus_next_field(self.simple_unit_combo))
        self.simple_unit_combo.bind('<Tab>', lambda e: self.focus_next_field(self.simple_unit_price_entry))
        self.simple_unit_price_entry.bind('<Tab>', lambda e: self.focus_next_field(self.simple_warehouse_combo))
        self.simple_warehouse_combo.bind('<Tab>', lambda e: self.focus_next_field(self.simple_description_entry))
        self.simple_description_entry.bind('<Tab>', lambda e: self.focus_next_field(self.simple_item_code_entry))

        # ربط Shift+Tab للانتقال العكسي
        self.simple_item_name_entry.bind('<Shift-Tab>', lambda e: self.focus_previous_field(self.simple_item_code_entry))
        self.simple_quantity_entry.bind('<Shift-Tab>', lambda e: self.focus_previous_field(self.simple_item_name_entry))
        self.simple_unit_combo.bind('<Shift-Tab>', lambda e: self.focus_previous_field(self.simple_quantity_entry))
        self.simple_unit_price_entry.bind('<Shift-Tab>', lambda e: self.focus_previous_field(self.simple_unit_combo))
        self.simple_warehouse_combo.bind('<Shift-Tab>', lambda e: self.focus_previous_field(self.simple_unit_price_entry))
        self.simple_description_entry.bind('<Shift-Tab>', lambda e: self.focus_previous_field(self.simple_warehouse_combo))

    def focus_next_field(self, next_field):
        """التركيز على الحقل التالي"""
        try:
            next_field.focus()
            return "break"  # منع التنقل الافتراضي
        except:
            pass

    def focus_previous_field(self, prev_field):
        """التركيز على الحقل السابق"""
        try:
            prev_field.focus()
            return "break"  # منع التنقل الافتراضي
        except:
            pass

    def auto_fill_item_data(self, event=None):
        """ملء بيانات الصنف تلقائياً عند إدخال كود الصنف"""
        try:
            item_code = self.form_vars['simple_item_code'].get().strip()

            # تجاهل placeholder text
            if not item_code or item_code.startswith('مثال:'):
                return

            # البحث عن الصنف في قاعدة البيانات
            item_data = self.get_item_data_by_code(item_code)

            if item_data:
                # ملء اسم الصنف إذا كان فارغاً
                current_name = self.form_vars['simple_item_name'].get().strip()
                if not current_name or current_name.startswith('مثال:'):
                    self.form_vars['simple_item_name'].set(item_data.get('item_name', ''))
                    self.simple_item_name_entry.configure(fg=COLORS['text_primary'])

                # ملء سعر الوحدة إذا كان متوفراً
                if item_data.get('unit_price'):
                    current_price = self.form_vars['simple_unit_price'].get().strip()
                    if not current_price or current_price.startswith('مثال:'):
                        self.form_vars['simple_unit_price'].set(str(item_data['unit_price']))
                        self.simple_unit_price_entry.configure(fg=COLORS['text_primary'])

                # ملء الوحدة إذا كانت متوفرة
                if item_data.get('unit'):
                    self.form_vars['simple_unit'].set(item_data['unit'])

                # ملء الوصف إذا كان متوفراً
                if item_data.get('description'):
                    current_desc = self.form_vars['simple_description'].get().strip()
                    if not current_desc or current_desc.startswith('وصف اختياري'):
                        self.form_vars['simple_description'].set(item_data['description'])
                        self.simple_description_entry.configure(fg=COLORS['text_primary'])

        except Exception as e:
            print(f"خطأ في الملء التلقائي: {e}")

    def get_item_data_by_code(self, item_code):
        """جلب بيانات الصنف من كود الصنف"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, item_name, description, unit, unit_price
                FROM items
                WHERE item_code = ? AND is_active = 1
            """, (item_code,))
            result = cursor.fetchone()
            conn.close()

            if result:
                return {
                    'id': result['id'],
                    'item_name': result['item_name'],
                    'description': result['description'],
                    'unit': result['unit'],
                    'unit_price': result['unit_price']
                }
            return None
        except Exception as e:
            print(f"خطأ في جلب بيانات الصنف: {e}")
            return None

    def calculate_simple_total(self, *args):
        """حساب الإجمالي في النموذج المبسط"""
        try:
            quantity_text = self.form_vars['simple_quantity'].get().strip()
            price_text = self.form_vars['simple_unit_price'].get().strip()

            # تجاهل placeholder text
            if quantity_text.startswith('مثال:') or not quantity_text:
                quantity = 0
            else:
                quantity = float(quantity_text)

            if price_text.startswith('مثال:') or not price_text:
                price = 0
            else:
                price = float(price_text)

            total = quantity * price
            self.form_vars['simple_total'].set(f"{total:.2f}")
        except ValueError:
            self.form_vars['simple_total'].set("0.00")

    def create_supplier_field(self, parent, width_ratio=1/3):
        """إنشاء حقل المورد مع تحميل من قاعدة البيانات"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية مع تلميح F9
        label = create_simple_rtl_label(
            field_frame,
            text="🏢 المورد * (F9 للبحث)",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['danger']
        )
        label.pack(anchor='e', pady=(0, 10))

        # القائمة المنسدلة
        self.form_vars['supplier_id'] = tk.StringVar()
        self.supplier_combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.form_vars['supplier_id']
        )
        self.supplier_combo.configure(font=('Segoe UI', 12, 'normal'))
        self.supplier_combo.pack(fill='x', ipady=12)

        # ربط F9 للبحث في الموردين
        self.supplier_combo.bind('<F9>', self.show_supplier_search)
        self.supplier_combo.bind('<KeyPress-F9>', self.show_supplier_search)

        # ربط تغيير البيانات
        self.form_vars['supplier_id'].trace('w', self.on_data_change)

        return self.supplier_combo

    def open_date_picker(self, var_name):
        """فتح نافذة اختيار التاريخ"""
        try:
            # إنشاء نافذة منبثقة
            date_window = tk.Toplevel(self.root)
            date_window.title("اختيار التاريخ")
            date_window.geometry("300x250")
            date_window.resizable(False, False)
            date_window.transient(self.root)
            date_window.grab_set()

            # توسيط النافذة
            date_window.geometry("+%d+%d" % (
                self.root.winfo_rootx() + 50,
                self.root.winfo_rooty() + 50
            ))

            # إطار التقويم
            cal_frame = tk.Frame(date_window, bg='white')
            cal_frame.pack(fill='both', expand=True, padx=20, pady=20)

            # التقويم
            current_date = date.today()
            try:
                # محاولة الحصول على التاريخ الحالي من الحقل
                current_value = self.form_vars[var_name].get().strip()
                if current_value:
                    current_date = datetime.strptime(current_value, '%Y-%m-%d').date()
            except:
                pass

            cal = DateEntry(
                cal_frame,
                width=12,
                background='darkblue',
                foreground='white',
                borderwidth=2,
                year=current_date.year,
                month=current_date.month,
                day=current_date.day,
                date_pattern='yyyy-mm-dd',
                font=('Segoe UI', 12)
            )
            cal.pack(pady=20)

            # إطار الأزرار
            btn_frame = tk.Frame(date_window, bg='white')
            btn_frame.pack(fill='x', padx=20, pady=(0, 20))

            # زر الموافقة
            ok_btn = tk.Button(
                btn_frame,
                text="✅ موافق",
                font=('Segoe UI', 12, 'bold'),
                bg=COLORS['primary'],
                fg='white',
                padx=20,
                pady=8,
                command=lambda: self.select_date(var_name, cal.get_date(), date_window)
            )
            ok_btn.pack(side='right', padx=(10, 0))

            # زر الإلغاء
            cancel_btn = tk.Button(
                btn_frame,
                text="❌ إلغاء",
                font=('Segoe UI', 12, 'bold'),
                bg=COLORS['secondary'],
                fg='white',
                padx=20,
                pady=8,
                command=date_window.destroy
            )
            cancel_btn.pack(side='right')

            # زر اليوم
            today_btn = tk.Button(
                btn_frame,
                text="📅 اليوم",
                font=('Segoe UI', 12, 'bold'),
                bg=COLORS['info'],
                fg='white',
                padx=20,
                pady=8,
                command=lambda: self.select_date(var_name, date.today(), date_window)
            )
            today_btn.pack(side='left')

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح اختيار التاريخ: {str(e)}")

    def select_date(self, var_name, selected_date, window):
        """تحديد التاريخ المختار"""
        try:
            # تحويل التاريخ إلى النسق المطلوب
            formatted_date = selected_date.strftime('%Y-%m-%d')
            self.form_vars[var_name].set(formatted_date)
            window.destroy()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديد التاريخ: {str(e)}")

    def create_status_field(self, parent, width_ratio=1/3):
        """إنشاء حقل الحالة مع ألوان"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text="📊 حالة الشحنة *",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['danger']
        )
        label.pack(anchor='e', pady=(0, 10))

        # القائمة المنسدلة
        self.form_vars['status'] = tk.StringVar()
        self.status_combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.form_vars['status']
        )
        self.status_combo['values'] = list(SHIPMENT_STATUS.values())
        self.status_combo.configure(font=('Segoe UI', 12, 'normal'))
        self.status_combo.pack(fill='x', ipady=12)

        # ربط تغيير البيانات والتقدم
        self.form_vars['status'].trace('w', self.on_status_change)

        return self.status_combo

    def create_priority_display(self, parent, width_ratio=1/3):
        """إنشاء عرض الأولوية المحسوبة"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text="⚡ الأولوية (تلقائية)",
            font=('Segoe UI', 14, 'bold')
        )
        label.pack(anchor='e', pady=(0, 10))

        # عرض الأولوية
        self.priority_display = create_simple_rtl_label(
            field_frame,
            text="عادي ⚪",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['text_secondary']
        )
        self.priority_display.pack(anchor='e', pady=(12, 0))

        return self.priority_display

    def create_calculated_field(self, parent, label_text, var_name, width_ratio=1/3):
        """إنشاء حقل محسوب تلقائياً"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 14, 'bold')
        )
        label.pack(anchor='e', pady=(0, 10))

        # العرض
        display = create_simple_rtl_label(
            field_frame,
            text="--",
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['primary']
        )
        display.pack(anchor='e', pady=(12, 0))

        # حفظ المرجع
        setattr(self, f'{var_name}_display', display)

        return display

    def create_datetime_display(self, parent, width_ratio=1/2):
        """إنشاء عرض التاريخ والوقت"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=15)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text="🕐 آخر تحديث:",
            font=('Segoe UI', 14, 'bold')
        )
        label.pack(anchor='e', pady=(0, 10))

        # العرض
        self.last_update_display = create_simple_rtl_label(
            field_frame,
            text=datetime.now().strftime('%Y/%m/%d %H:%M:%S'),
            font=('Segoe UI', 14, 'normal'),
            fg=COLORS['primary']
        )
        self.last_update_display.pack(anchor='e', pady=(12, 0))

        return self.last_update_display

    # وظائف التنقل بين الأقسام
    def switch_section(self, section_id):
        """التبديل إلى قسم معين"""
        if 0 <= section_id < len(self.sections):
            # إخفاء جميع الأقسام
            for section in self.sections:
                section.pack_forget()

            # عرض القسم المحدد
            self.sections[section_id].pack(fill='both', expand=True)
            self.current_section = section_id

            # تحديث أزرار التنقل
            self.update_navigation_buttons()

            # تحديث أزرار التنقل
            self.update_navigation_buttons()

            # تحديث مؤشر القسم
            if hasattr(self, 'section_indicator') and self.section_indicator:
                self.section_indicator.configure(text=f"القسم {section_id + 1} من {len(self.sections)}")

            # تحديث ألوان أزرار التنقل العلوية
            if hasattr(self, 'nav_buttons') and self.nav_buttons:
                for i, btn in enumerate(self.nav_buttons):
                    try:
                        if i == section_id:
                            btn.configure(
                                bg=COLORS['primary_light'],
                                fg=COLORS['text_white']
                            )
                        else:
                            btn.configure(
                                bg=COLORS['primary'],
                                fg=COLORS['text_white']
                            )
                    except Exception as e:
                        print(f"خطأ في تحديث زر التنقل {i}: {e}")

    def next_section(self):
        """الانتقال للقسم التالي"""
        if self.current_section < len(self.sections) - 1:
            self.switch_section(self.current_section + 1)

    def prev_section(self):
        """الانتقال للقسم السابق"""
        if self.current_section > 0:
            self.switch_section(self.current_section - 1)

    def update_navigation_buttons(self):
        """تحديث حالة أزرار التنقل"""
        try:
            # التحقق من وجود الأزرار قبل تحديثها
            if hasattr(self, 'prev_section_btn') and self.prev_section_btn:
                # زر السابق
                if self.current_section == 0:
                    self.prev_section_btn.configure(state='disabled')
                else:
                    self.prev_section_btn.configure(state='normal')

            if hasattr(self, 'next_section_btn') and self.next_section_btn:
                # زر التالي
                if self.current_section == len(self.sections) - 1:
                    self.next_section_btn.configure(state='disabled')
                else:
                    self.next_section_btn.configure(state='normal')
        except Exception as e:
            print(f"خطأ في تحديث أزرار التنقل: {e}")

    # وظائف البيانات
    def load_form_data(self):
        """تحميل بيانات النموذج"""
        try:
            # تحميل قائمة الموردين
            self.load_suppliers()

            # تعيين القيم الافتراضية للشحنات الجديدة
            if self.mode == 'add':
                self.set_default_values()

            # تحميل بيانات الشحنة إذا كانت موجودة
            elif self.shipment_data and self.mode in ['edit', 'duplicate', 'view']:
                self.populate_form_data()

            # تطبيق قيود العرض فقط
            if self.mode == 'view':
                self.make_form_readonly()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            suppliers = self.db_manager.fetch_all(
                "SELECT id, supplier_name FROM suppliers WHERE is_active = 1 ORDER BY supplier_name"
            )

            supplier_values = [f"{s['supplier_name']} (ID: {s['id']})" for s in suppliers]
            self.supplier_combo['values'] = supplier_values

            # حفظ بيانات الموردين للمرجع
            self.suppliers_data = suppliers

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الموردين: {str(e)}")

    def set_default_values(self):
        """تعيين القيم الافتراضية"""
        # إنشاء رقم شحنة تلقائي
        self.form_vars['shipment_number'].set(self.generate_shipment_number())

        # تعيين التاريخ الحالي
        self.form_vars['shipment_date'].set(date.today().strftime('%Y-%m-%d'))

        # تعيين الحالة الافتراضية
        self.form_vars['status'].set('في الانتظار')

        # تعيين العملة الافتراضية
        self.form_vars['currency'].set('USD')

        # تعيين حالة الدفع الافتراضية
        self.form_vars['payment_status'].set('لم يتم الدفع')

        # تعيين حالة الإفراج الافتراضية
        self.form_vars['release_status'].set('بدون إفراج')

        # تعيين عدد الحاويات الافتراضي
        self.form_vars['container_count'].set('1')

        # تحديث شريط التقدم
        self.update_progress_display()

    def generate_shipment_number(self):
        """إنشاء رقم شحنة تلقائي"""
        try:
            # الحصول على آخر رقم شحنة
            last_shipment = self.db_manager.fetch_one(
                "SELECT shipment_number FROM advanced_shipments ORDER BY created_at DESC LIMIT 1"
            )

            if last_shipment and last_shipment['shipment_number']:
                last_number = last_shipment['shipment_number']
                if last_number.startswith('SH-'):
                    try:
                        number_part = int(last_number.split('-')[1])
                        new_number = number_part + 1
                        return f"SH-{new_number:06d}"
                    except:
                        pass

            return f"SH-{1:06d}"

        except Exception:
            return f"SH-{datetime.now().strftime('%Y%m%d%H%M%S')}"

    def populate_form_data(self):
        """ملء النموذج بالبيانات"""
        try:
            # ملء جميع الحقول
            for field, var in self.form_vars.items():
                value = self.shipment_data.get(field, '')
                if value:
                    var.set(str(value))

            # معالجة خاصة للمورد
            supplier_id = self.shipment_data.get('supplier_id')
            if supplier_id and hasattr(self, 'suppliers_data'):
                for supplier in self.suppliers_data:
                    if supplier['id'] == supplier_id:
                        supplier_text = f"{supplier['supplier_name']} (ID: {supplier['id']})"
                        self.form_vars['supplier_id'].set(supplier_text)
                        break

            # ملء منطقة الملاحظات
            notes = self.shipment_data.get('notes', '')
            if notes:
                self.notes_text.delete('1.0', tk.END)
                self.notes_text.insert('1.0', notes)

            # ملء وصف البضائع
            goods_desc = self.shipment_data.get('goods_description', '')
            if goods_desc:
                self.goods_description_text.delete('1.0', tk.END)
                self.goods_description_text.insert('1.0', goods_desc)

            # ملء ملاحظات المستندات
            if hasattr(self, 'documents_notes_text'):
                docs_notes = self.shipment_data.get('documents_notes', '')
                if docs_notes:
                    self.documents_notes_text.delete('1.0', tk.END)
                    self.documents_notes_text.insert('1.0', docs_notes)

            # تحديث شريط التقدم
            self.update_progress_display()

            # تحديث تقدم المستندات
            self.update_documents_progress()

            # تحديث رؤية الأقسام والتنقل
            self.update_sections_visibility()
            self.update_navigation_buttons()

            # معالجة خاصة لأرقام الحاويات المتعددة
            self.populate_container_numbers()

            # تحميل أصناف الشحنة
            if self.shipment_data.get('id'):
                self.load_shipment_items(self.shipment_data.get('id'))

            # إذا كان الوضع نسخ، مسح بعض الحقول
            if self.mode == 'duplicate':
                self.form_vars['shipment_number'].set(self.generate_shipment_number())
                self.form_vars['status'].set('في الانتظار')
                self.form_vars['tracking_number'].set('')
                self.form_vars['current_location'].set('')

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في ملء البيانات: {str(e)}")

    def populate_container_numbers(self):
        """ملء أرقام الحاويات المتعددة"""
        try:
            # الحصول على عدد الحاويات وأرقامها
            container_count = self.shipment_data.get('container_count', 1)
            container_numbers_json = self.shipment_data.get('container_numbers_json', '')

            # تحليل JSON لأرقام الحاويات
            container_numbers = []
            if container_numbers_json:
                try:
                    container_numbers = json.loads(container_numbers_json)
                except:
                    # في حالة فشل تحليل JSON، استخدم رقم الحاوية الواحد
                    single_container = self.shipment_data.get('container_number', '')
                    if single_container:
                        container_numbers = [single_container]
            else:
                # إذا لم يكن هناك JSON، استخدم رقم الحاوية الواحد
                single_container = self.shipment_data.get('container_number', '')
                if single_container:
                    container_numbers = [single_container]

            # تحديث عدد الحاويات
            if container_count and container_count > 0:
                self.form_vars['container_count'].set(str(container_count))
                # تحديث حقول أرقام الحاويات
                self.update_container_fields(container_count)

                # ملء أرقام الحاويات
                for i, container_num in enumerate(container_numbers):
                    if i < len(self.container_number_vars):
                        self.container_number_vars[i].set(container_num)

        except Exception as e:
            print(f"خطأ في ملء أرقام الحاويات: {e}")

    def make_form_readonly(self):
        """جعل النموذج للقراءة فقط"""
        # تعطيل جميع حقول الإدخال
        for widget in self.sections_frame.winfo_children():
            self.disable_widget_recursively(widget)

    def disable_widget_recursively(self, widget):
        """تعطيل عنصر وجميع عناصره الفرعية"""
        try:
            if hasattr(widget, 'configure'):
                if isinstance(widget, (tk.Entry, ttk.Combobox)):
                    widget.configure(state='readonly')
                elif isinstance(widget, tk.Text):
                    widget.configure(state='disabled')
                elif isinstance(widget, tk.Button):
                    widget.configure(state='disabled')

            # تطبيق على العناصر الفرعية
            for child in widget.winfo_children():
                self.disable_widget_recursively(child)

        except Exception:
            pass

    # وظائف التفاعل
    def on_data_change(self, *args):
        """عند تغيير البيانات"""
        if not self.is_modified:
            self.is_modified = True
            self.modified_indicator.configure(
                text="● تم التعديل",
                fg=COLORS['warning']
            )

        # تحديث الحسابات التلقائية
        self.update_calculations()

    def on_status_change(self, *args):
        """عند تغيير الحالة"""
        self.on_data_change()
        self.update_progress_display()
        self.update_priority_display()
        self.update_sections_visibility()
        self.update_navigation_buttons()

    def on_document_change(self, *args):
        """عند تغيير المستندات"""
        self.on_data_change()
        self.update_documents_progress()

    def update_progress_display(self):
        """تحديث عرض التقدم"""
        try:
            status = self.form_vars.get('status', tk.StringVar()).get()

            # خريطة التقدم
            progress_map = {
                'في الانتظار': 10,
                'تحت الطلب': 5,
                'مؤكدة': 25,
                'تم الشحن': 40,
                'في الطريق': 60,
                'وصلت': 80,
                'في الجمارك': 85,
                'تم التسليم': 100,
                'ملغية': 0,
                'متأخرة': 50
            }

            progress = progress_map.get(status, 0)
            self.progress_bar['value'] = progress
            self.progress_label.configure(text=f"{progress}%")

            # تغيير لون شريط التقدم حسب الحالة
            if progress == 100:
                self.progress_label.configure(fg=COLORS['success'])
            elif progress >= 80:
                self.progress_label.configure(fg=COLORS['primary'])
            elif progress >= 50:
                self.progress_label.configure(fg=COLORS['warning'])
            else:
                self.progress_label.configure(fg=COLORS['danger'])

        except Exception:
            pass

    def update_priority_display(self):
        """تحديث عرض الأولوية"""
        try:
            priority = self.calculate_priority()

            priority_icons = {
                'urgent': '🔴 عاجل',
                'high': '🟠 عالي',
                'medium': '🟡 متوسط',
                'low': '🟢 منخفض',
                'normal': '⚪ عادي'
            }

            priority_colors = {
                'urgent': COLORS['danger'],
                'high': COLORS['warning'],
                'medium': COLORS['info'],
                'low': COLORS['success'],
                'normal': COLORS['text_secondary']
            }

            icon_text = priority_icons.get(priority, '⚪ عادي')
            color = priority_colors.get(priority, COLORS['text_secondary'])

            self.priority_display.configure(text=icon_text, fg=color)

        except Exception:
            pass

    def update_sections_visibility(self):
        """تحديث رؤية الأقسام بناءً على حالة الشحنة"""
        try:
            current_status = self.form_vars.get('status', tk.StringVar()).get().strip()
            required_sections = self.get_required_sections_by_status(current_status)

            # إخفاء جميع الأقسام أولاً
            for i, section in enumerate(self.sections):
                if i in required_sections:
                    section.pack(fill='both', expand=True)
                else:
                    section.pack_forget()

            # التأكد من عرض القسم الحالي إذا كان مطلوباً
            if hasattr(self, 'current_section') and self.current_section not in required_sections:
                # الانتقال إلى أول قسم مطلوب
                if required_sections:
                    self.switch_section(required_sections[0])

            # تحديث مؤشر الأقسام
            self.update_sections_indicator(current_status, required_sections)

        except Exception as e:
            print(f"خطأ في تحديث رؤية الأقسام: {e}")

    def update_sections_indicator(self, status, required_sections):
        """تحديث مؤشر الأقسام المطلوبة"""
        try:
            if hasattr(self, 'sections_indicator'):
                section_count = len(required_sections)
                total_sections = 8

                indicator_text = f"📋 {section_count}/{total_sections} أقسام مطلوبة لحالة '{status}'"
                self.sections_indicator.configure(text=indicator_text)

        except Exception as e:
            print(f"خطأ في تحديث مؤشر الأقسام: {e}")

    def update_navigation_buttons(self):
        """تحديث أزرار التنقل بناءً على الأقسام المطلوبة"""
        try:
            current_status = self.form_vars.get('status', tk.StringVar()).get().strip()
            required_sections = self.get_required_sections_by_status(current_status)

            # تحديث حالة أزرار التنقل
            for i, button in enumerate(self.nav_buttons):
                if i in required_sections:
                    button.configure(state='normal', bg=COLORS['secondary'])
                    # إضافة مؤشر للأقسام المطلوبة
                    if '✓' not in button.cget('text'):
                        current_text = button.cget('text')
                        if not current_text.endswith(' ✓'):
                            button.configure(text=current_text + ' ✓')
                else:
                    button.configure(state='disabled', bg=COLORS['disabled'])
                    # إزالة المؤشر من الأقسام غير المطلوبة
                    current_text = button.cget('text')
                    if current_text.endswith(' ✓'):
                        button.configure(text=current_text.replace(' ✓', ''))

        except Exception as e:
            print(f"خطأ في تحديث أزرار التنقل: {e}")

    def calculate_priority(self):
        """حساب أولوية الشحنة"""
        try:
            priority_score = 0

            # عامل القيمة
            total_value = float(self.form_vars.get('total_value', tk.StringVar()).get() or 0)
            if total_value > 100000:
                priority_score += 3
            elif total_value > 50000:
                priority_score += 2
            elif total_value > 10000:
                priority_score += 1

            # عامل التأخير
            expected_date = self.form_vars.get('expected_arrival_date', tk.StringVar()).get()
            if expected_date:
                try:
                    expected = datetime.strptime(expected_date, '%Y-%m-%d').date()
                    today = date.today()
                    if expected < today:
                        priority_score += 4  # متأخرة
                    elif (expected - today).days <= 3:
                        priority_score += 2  # قريبة
                except:
                    pass

            # عامل الحالة
            status = self.form_vars.get('status', tk.StringVar()).get()
            if status in ['متأخرة', 'في الجمارك']:
                priority_score += 2
            elif status in ['في الطريق', 'وصلت']:
                priority_score += 1

            # تحديد الأولوية النهائية
            if priority_score >= 6:
                return 'urgent'
            elif priority_score >= 4:
                return 'high'
            elif priority_score >= 2:
                return 'medium'
            elif priority_score >= 1:
                return 'low'
            else:
                return 'normal'

        except Exception:
            return 'normal'

    def update_calculations(self):
        """تحديث الحسابات التلقائية"""
        try:
            # حساب إجمالي التكلفة
            total_value = float(self.form_vars.get('total_value', tk.StringVar()).get() or 0)
            shipping_cost = float(self.form_vars.get('shipping_cost', tk.StringVar()).get() or 0)
            insurance_cost = float(self.form_vars.get('insurance_cost', tk.StringVar()).get() or 0)
            additional_fees = float(self.form_vars.get('additional_fees', tk.StringVar()).get() or 0)
            customs_fees = float(self.form_vars.get('customs_fees', tk.StringVar()).get() or 0)
            handling_fees = float(self.form_vars.get('handling_fees', tk.StringVar()).get() or 0)

            total_cost = total_value + shipping_cost + insurance_cost + additional_fees + customs_fees + handling_fees

            if hasattr(self, 'total_cost_display'):
                currency = self.form_vars.get('currency', tk.StringVar()).get() or 'USD'
                self.total_cost_display.configure(text=f"{total_cost:,.2f} {currency}")

            # حساب الأيام المتبقية
            expected_date = self.form_vars.get('expected_arrival_date', tk.StringVar()).get()
            if expected_date and hasattr(self, 'days_remaining_display'):
                try:
                    expected = datetime.strptime(expected_date, '%Y-%m-%d').date()
                    today = date.today()
                    diff = (expected - today).days

                    if diff < 0:
                        self.days_remaining_display.configure(
                            text=f"متأخرة {abs(diff)} يوم",
                            fg=COLORS['danger']
                        )
                    elif diff == 0:
                        self.days_remaining_display.configure(
                            text="اليوم",
                            fg=COLORS['warning']
                        )
                    elif diff == 1:
                        self.days_remaining_display.configure(
                            text="غداً",
                            fg=COLORS['warning']
                        )
                    else:
                        self.days_remaining_display.configure(
                            text=f"{diff} يوم",
                            fg=COLORS['success']
                        )
                except:
                    self.days_remaining_display.configure(text="غير صحيح", fg=COLORS['danger'])

        except Exception:
            pass

    def update_documents_progress(self):
        """تحديث تقدم المستندات"""
        try:
            # قائمة المستندات الأساسية (بعد التحديث)
            document_fields = [
                'commercial_invoice_status',  # الوثائق الأولية
                'packing_list_status',        # المستندات (DN)
                'certificate_of_origin_status',  # المستندات الخاصة بالجمارك
                'insurance_policy_status',    # بوليصة التأمين
                'customs_declaration_status'  # صور الأصناف
            ]

            total_docs = len(document_fields)
            completed_docs = 0

            # حساب المستندات المكتملة
            for field in document_fields:
                if field in self.form_vars:
                    status = self.form_vars[field].get()
                    if status in ['متوفر', 'مرسل', 'مؤكد']:
                        completed_docs += 1

            # حساب النسبة المئوية
            if total_docs > 0:
                progress_percentage = (completed_docs / total_docs) * 100
            else:
                progress_percentage = 0

            # تحديث شريط التقدم
            if hasattr(self, 'documents_progress_bar'):
                self.documents_progress_bar['value'] = progress_percentage

            # تحديث النص
            if hasattr(self, 'documents_progress_label'):
                self.documents_progress_label.configure(
                    text=f"{progress_percentage:.0f}% مكتمل ({completed_docs}/{total_docs})"
                )

                # تغيير لون النص حسب النسبة
                if progress_percentage == 100:
                    self.documents_progress_label.configure(fg=COLORS['success'])
                elif progress_percentage >= 75:
                    self.documents_progress_label.configure(fg=COLORS['primary'])
                elif progress_percentage >= 50:
                    self.documents_progress_label.configure(fg=COLORS['warning'])
                else:
                    self.documents_progress_label.configure(fg=COLORS['danger'])

            # تحديث حالة المستندات العامة تلقائياً
            if hasattr(self, 'form_vars') and 'documents_status' in self.form_vars:
                if progress_percentage == 100:
                    self.form_vars['documents_status'].set('مكتملة')
                elif progress_percentage >= 75:
                    self.form_vars['documents_status'].set('مكتملة جزئياً')
                elif progress_percentage >= 25:
                    self.form_vars['documents_status'].set('قيد المراجعة')
                else:
                    self.form_vars['documents_status'].set('غير مكتملة')

        except Exception as e:
            print(f"خطأ في تحديث تقدم المستندات: {e}")

    # وظائف إدارة الأصناف
    def add_simple_item(self, event=None):
        """إضافة صنف من النموذج المبسط"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_simple_form():
                return

            # جمع البيانات من النموذج المبسط
            item_data = self.collect_simple_form_data()

            # التحقق من توفر الكمية في المخزن
            if item_data.get('item_id') and item_data.get('warehouse_id'):
                if not self.check_inventory_availability(item_data):
                    return

            # إضافة الصنف للقائمة
            item_data['id'] = str(uuid.uuid4())
            self.items_data.append(item_data)

            # تحديث الجدول
            self.update_items_tree()
            self.update_items_summary()

            # تحديث مؤشر التعديل
            self.on_data_change()

            # مسح النموذج للإدخال التالي
            self.clear_simple_form()

            # التركيز على حقل كود الصنف للإدخال التالي
            self.simple_item_code_entry.focus()

            # رسالة نجاح
            messagebox.showinfo("نجح", f"تم إضافة الصنف '{item_data.get('item_name', '')}' بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة الصنف: {str(e)}")

    def validate_simple_form(self):
        """التحقق من صحة بيانات النموذج المبسط"""
        errors = []

        # الحقول المطلوبة
        required_fields = {
            'simple_item_code': 'كود الصنف',
            'simple_item_name': 'اسم الصنف',
            'simple_quantity': 'الكمية',
            'simple_unit': 'الوحدة',
            'simple_unit_price': 'سعر الوحدة',
            'simple_warehouse': 'المخزن المستهدف'
        }

        for field, label in required_fields.items():
            value = self.form_vars.get(field, tk.StringVar()).get().strip()
            if not value or value.startswith('مثال:'):
                errors.append(f"حقل {label} مطلوب")

        # التحقق من صحة الأرقام
        numeric_fields = ['simple_quantity', 'simple_unit_price']
        for field in numeric_fields:
            value = self.form_vars.get(field, tk.StringVar()).get().strip()
            if value and not value.startswith('مثال:'):
                try:
                    float(value)
                except ValueError:
                    field_name = 'الكمية' if field == 'simple_quantity' else 'سعر الوحدة'
                    errors.append(f"قيمة {field_name} يجب أن تكون رقماً")

        if errors:
            error_message = "يرجى تصحيح الأخطاء التالية:\n\n" + "\n".join(f"• {error}" for error in errors)
            messagebox.showerror("أخطاء في البيانات", error_message)
            return False

        return True

    def collect_simple_form_data(self):
        """جمع البيانات من النموذج المبسط"""
        data = {}

        # جمع البيانات الأساسية
        for field, var in self.form_vars.items():
            if field.startswith('simple_'):
                value = var.get().strip()
                if value and not value.startswith('مثال:'):
                    # إزالة البادئة 'simple_'
                    clean_field = field.replace('simple_', '')

                    # معالجة الحقول الرقمية
                    if clean_field in ['quantity', 'unit_price']:
                        try:
                            data[clean_field] = float(value)
                        except ValueError:
                            data[clean_field] = 0.0
                    # معالجة المخزن
                    elif clean_field == 'warehouse':
                        try:
                            data['warehouse_id'] = int(value.split(' - ')[0])
                            data['warehouse'] = value.split(' - ')[1]
                        except (ValueError, IndexError):
                            data['warehouse_id'] = None
                            data['warehouse'] = value
                    else:
                        data[clean_field] = value

        # حساب الإجمالي
        if 'quantity' in data and 'unit_price' in data:
            data['total_price'] = data['quantity'] * data['unit_price']

        # البحث عن معرف الصنف من قاعدة البيانات
        item_code = data.get('item_code', '')
        if item_code:
            data['item_id'] = self.get_item_id_by_code(item_code)

        return data

    def clear_simple_form(self):
        """مسح النموذج المبسط"""
        # إعادة تعيين القيم الافتراضية
        if 'simple_unit' in self.form_vars:
            self.form_vars['simple_unit'].set('قطعة')

        if 'simple_total' in self.form_vars:
            self.form_vars['simple_total'].set('0.00')

        # إعادة إضافة placeholder text مع الألوان المناسبة
        placeholders = {
            'simple_item_code': ('مثال: ITM001', self.simple_item_code_entry),
            'simple_item_name': ('مثال: لابتوب ديل', self.simple_item_name_entry),
            'simple_quantity': ('مثال: 10', self.simple_quantity_entry),
            'simple_unit_price': ('مثال: 1500.00', self.simple_unit_price_entry),
            'simple_description': ('وصف اختياري للصنف', self.simple_description_entry)
        }

        for field, (placeholder, entry_widget) in placeholders.items():
            if field in self.form_vars:
                self.form_vars[field].set(placeholder)
                entry_widget.configure(fg=COLORS['text_muted'])

    def get_item_id_by_code(self, item_code):
        """جلب معرف الصنف من كود الصنف"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM items WHERE item_code = ?", (item_code,))
            result = cursor.fetchone()
            conn.close()
            return result['id'] if result else None
        except Exception as e:
            print(f"خطأ في جلب معرف الصنف: {e}")
            return None

    def add_item(self):
        """إضافة صنف جديد"""
        try:
            item_dialog = ItemDialog(self.root, mode='add')
            self.root.wait_window(item_dialog.root)

            if hasattr(item_dialog, 'result') and item_dialog.result:
                # إضافة الصنف للقائمة
                item_data = item_dialog.result
                item_data['id'] = str(uuid.uuid4())

                # التحقق من توفر الكمية في المخزن
                if item_data.get('item_id') and item_data.get('warehouse_id'):
                    if not self.check_inventory_availability(item_data):
                        return

                self.items_data.append(item_data)

                # تحديث الجدول
                self.update_items_tree()
                self.update_items_summary()

                # تحديث مؤشر التعديل
                self.on_data_change()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة الصنف: {str(e)}")

    def check_inventory_availability(self, item_data):
        """التحقق من توفر الكمية في المخزن"""
        try:
            item_id = item_data.get('item_id')
            warehouse_id = item_data.get('warehouse_id')
            required_quantity = float(item_data.get('quantity', 0))

            if not item_id or not warehouse_id:
                return True  # السماح بالإضافة إذا لم يتم ربط الصنف بالمخزن

            # جلب الكمية المتاحة
            inventory_info = self.db_manager.get_item_inventory(item_id, warehouse_id)

            if inventory_info:
                available_quantity = inventory_info['quantity'] - inventory_info.get('reserved_quantity', 0)
                warehouse_name = inventory_info['warehouse_name']

                if available_quantity < required_quantity:
                    result = messagebox.askyesno(
                        "تحذير - نقص في المخزون",
                        f"الكمية المطلوبة: {required_quantity}\n"
                        f"الكمية المتاحة في {warehouse_name}: {available_quantity}\n\n"
                        f"هل تريد المتابعة رغم نقص المخزون؟"
                    )
                    return result
            else:
                result = messagebox.askyesno(
                    "تحذير - لا يوجد مخزون",
                    f"لا يوجد مخزون لهذا الصنف في المخزن المحدد.\n"
                    f"هل تريد المتابعة؟"
                )
                return result

            return True

        except Exception as e:
            print(f"خطأ في التحقق من المخزون: {e}")
            return True

    def get_warehouse_name(self, warehouse_id):
        """جلب اسم المخزن"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT warehouse_name FROM warehouses WHERE id = ?", (warehouse_id,))
            result = cursor.fetchone()
            conn.close()
            return result['warehouse_name'] if result else ''
        except Exception as e:
            print(f"خطأ في جلب اسم المخزن: {e}")
            return ''

    def edit_item(self):
        """تعديل صنف موجود"""
        if not self.selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للتعديل")
            return

        try:
            item_dialog = ItemDialog(self.root, mode='edit', item_data=self.selected_item)
            self.root.wait_window(item_dialog.root)

            if hasattr(item_dialog, 'result') and item_dialog.result:
                # تحديث الصنف في القائمة
                for i, item in enumerate(self.items_data):
                    if item.get('id') == self.selected_item.get('id'):
                        self.items_data[i] = item_dialog.result
                        break

                # تحديث الجدول
                self.update_items_tree()
                self.update_items_summary()

                # تحديث مؤشر التعديل
                self.on_data_change()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تعديل الصنف: {str(e)}")

    def delete_item(self):
        """حذف صنف"""
        if not self.selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للحذف")
            return

        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الصنف '{self.selected_item.get('item_name', '')}'؟"
        )

        if result:
            try:
                # حذف الصنف من القائمة
                self.items_data = [item for item in self.items_data
                                 if item.get('id') != self.selected_item.get('id')]

                # تحديث الجدول
                self.update_items_tree()
                self.update_items_summary()

                # مسح التحديد
                self.selected_item = None

                # تحديث مؤشر التعديل
                self.on_data_change()

                messagebox.showinfo("نجح", "تم حذف الصنف بنجاح")

            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الصنف: {str(e)}")

    def duplicate_item(self):
        """نسخ صنف"""
        if not self.selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للنسخ")
            return

        try:
            item_dialog = ItemDialog(self.root, mode='duplicate', item_data=self.selected_item)
            self.root.wait_window(item_dialog.root)

            if hasattr(item_dialog, 'result') and item_dialog.result:
                # إضافة الصنف المنسوخ للقائمة
                item_data = item_dialog.result
                item_data['id'] = str(uuid.uuid4())
                self.items_data.append(item_data)

                # تحديث الجدول
                self.update_items_tree()
                self.update_items_summary()

                # تحديث مؤشر التعديل
                self.on_data_change()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في نسخ الصنف: {str(e)}")

    def on_item_select(self, event):
        """عند اختيار صنف"""
        selection = self.items_tree.selection()
        if selection:
            item = selection[0]
            item_text = self.items_tree.item(item, 'text')

            # البحث عن الصنف
            if '(ID:' in item_text:
                try:
                    item_id = item_text.split('(ID:')[1].split(')')[0].strip()

                    for item_data in self.items_data:
                        if str(item_data.get('id', '')) == str(item_id):
                            self.selected_item = item_data
                            break
                except:
                    self.selected_item = None

    def on_item_double_click(self, event):
        """عند النقر المزدوج على صنف"""
        if self.selected_item:
            self.edit_item()

    def show_items_context_menu(self, event):
        """إظهار قائمة السياق للأصناف"""
        context_menu = tk.Menu(self.root, tearoff=0)

        context_menu.add_command(label="➕ إضافة صنف", command=self.add_item)
        context_menu.add_separator()
        context_menu.add_command(label="✏️ تعديل", command=self.edit_item)
        context_menu.add_command(label="📋 نسخ", command=self.duplicate_item)
        context_menu.add_separator()
        context_menu.add_command(label="🗑️ حذف", command=self.delete_item)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def update_items_tree(self):
        """تحديث جدول الأصناف"""
        # مسح البيانات الحالية
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # إضافة الأصناف للجدول
        for i, item_data in enumerate(self.items_data):
            self.add_item_to_tree(item_data, i + 1)

    def add_item_to_tree(self, item_data, index):
        """إضافة صنف للجدول"""
        try:
            # جلب اسم المخزن إذا كان متوفراً
            warehouse_name = ''
            if item_data.get('warehouse_id'):
                warehouse_name = self.get_warehouse_name(item_data['warehouse_id'])

            # تنسيق البيانات
            values = [
                item_data.get('item_code', ''),
                item_data.get('item_name', ''),
                item_data.get('description', ''),
                str(item_data.get('quantity', 0)),
                item_data.get('unit', ''),
                f"{float(item_data.get('unit_price', 0)):,.2f}",
                f"{float(item_data.get('total_price', 0)):,.2f}",
                f"{float(item_data.get('weight', 0)):,.2f}",
                f"{float(item_data.get('volume', 0)):,.2f}",
                item_data.get('hs_code', ''),
                item_data.get('origin_country', ''),
                warehouse_name
            ]

            # تحديد لون الصف
            tags = []

            # لون متناوب
            if index % 2 == 0:
                tags.append('even_row')
            else:
                tags.append('odd_row')

            # لون خاص للقيم العالية
            total_price = float(item_data.get('total_price', 0))
            if total_price > 10000:
                tags.append('high_value')

            # إدراج العنصر
            item = self.items_tree.insert(
                '', 'end',
                text=f"{index} (ID: {item_data.get('id', '')})",
                values=values,
                tags=tags
            )

        except Exception as e:
            print(f"خطأ في إضافة صنف للجدول: {e}")

    def update_items_summary(self):
        """تحديث ملخص الأصناف"""
        try:
            total_items = len(self.items_data)
            total_quantity = sum(float(item.get('quantity', 0)) for item in self.items_data)
            total_weight = sum(float(item.get('weight', 0)) for item in self.items_data)
            total_volume = sum(float(item.get('volume', 0)) for item in self.items_data)
            total_value = sum(float(item.get('total_price', 0)) for item in self.items_data)
            avg_price = total_value / total_items if total_items > 0 else 0

            # تحديث التسميات
            self.total_items_label.configure(text=f"إجمالي الأصناف: {total_items}")
            self.total_quantity_label.configure(text=f"إجمالي الكمية: {total_quantity:,.0f}")
            self.total_weight_label.configure(text=f"إجمالي الوزن: {total_weight:,.2f} كجم")
            self.total_volume_label.configure(text=f"إجمالي الحجم: {total_volume:,.2f} م³")
            self.total_value_label.configure(text=f"إجمالي القيمة: {total_value:,.2f}")
            self.avg_price_label.configure(text=f"متوسط السعر: {avg_price:,.2f}")

            # تحديث القيمة الإجمالية في القسم المالي إذا كان موجوداً
            if hasattr(self, 'form_vars') and 'total_value' in self.form_vars:
                self.form_vars['total_value'].set(f"{total_value:.2f}")

        except Exception as e:
            print(f"خطأ في تحديث ملخص الأصناف: {e}")

    def handle_f9_key(self, event=None):
        """معالج F9 الذكي حسب السياق"""
        try:
            # التحقق من العنصر الذي له التركيز
            focused_widget = self.root.focus_get()

            # إذا كان التركيز على حقل المورد أو في القسم الأساسي
            if (focused_widget == self.supplier_combo or
                (hasattr(focused_widget, 'master') and focused_widget.master == self.supplier_combo) or
                self.current_section == 0):
                self.show_supplier_search(event)
                return 'break'

            # إذا كان في قسم الأصناف أو التركيز على جدول الأصناف
            elif (self.current_section == 1 or
                  focused_widget == self.items_tree or
                  (hasattr(focused_widget, 'master') and focused_widget.master == self.items_tree)):
                self.show_item_search(event)
                return 'break'

            # في الحالات الأخرى، عرض رسالة توضيحية
            else:
                messagebox.showinfo("تنبيه",
                    "F9 للبحث:\n" +
                    "• في القسم الأساسي أو حقل المورد: البحث في الموردين\n" +
                    "• في قسم الأصناف: البحث في الأصناف")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في معالجة F9: {str(e)}")

    def show_item_search(self, event=None):
        """إظهار نافذة البحث في الأصناف عند الضغط على F9"""
        try:
            # لا نحتاج للتحقق من القسم هنا لأن handle_f9_key يتولى ذلك

            # إنشاء نافذة البحث مخصصة للنموذج المبسط
            search_dialog = SimpleItemSearchDialog(self.root, self)
            # لا نحتاج wait_window لأن النافذة modal بالفعل

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")

    def fill_simple_form_from_search(self, item_data):
        """ملء النموذج المبسط من نتائج البحث"""
        try:
            # ملء كود الصنف
            if item_data.get('item_code'):
                self.form_vars['simple_item_code'].set(item_data['item_code'])
                self.simple_item_code_entry.configure(fg=COLORS['text_primary'])

            # ملء اسم الصنف
            if item_data.get('item_name'):
                self.form_vars['simple_item_name'].set(item_data['item_name'])
                self.simple_item_name_entry.configure(fg=COLORS['text_primary'])

            # ملء سعر الوحدة
            if item_data.get('unit_price'):
                self.form_vars['simple_unit_price'].set(str(item_data['unit_price']))
                self.simple_unit_price_entry.configure(fg=COLORS['text_primary'])

            # ملء الوحدة
            if item_data.get('unit'):
                self.form_vars['simple_unit'].set(item_data['unit'])

            # ملء الوصف
            if item_data.get('description'):
                self.form_vars['simple_description'].set(item_data['description'])
                self.simple_description_entry.configure(fg=COLORS['text_primary'])

            # التركيز على حقل الكمية
            self.simple_quantity_entry.focus()

            print(f"✅ تم ملء النموذج المبسط من البحث: {item_data.get('item_name', '')}")

        except Exception as e:
            print(f"❌ خطأ في ملء النموذج من البحث: {e}")

class SimpleItemSearchDialog:
    """نافذة بحث الأصناف المخصصة للنموذج المبسط"""

    def __init__(self, parent, main_form):
        self.parent = parent
        self.main_form = main_form
        self.selected_item = None

        # إنشاء النافذة
        self.root = tk.Toplevel(parent)
        self.root.title("🔍 البحث في الأصناف - النموذج المبسط")
        self.root.geometry("1000x600")
        self.root.configure(bg=COLORS['background'])
        self.root.transient(parent)
        self.root.grab_set()

        # ربط أحداث لوحة المفاتيح
        self.root.bind('<Escape>', lambda e: self.close_dialog())
        self.root.bind('<Return>', lambda e: self.select_item())
        self.root.protocol("WM_DELETE_WINDOW", self.close_dialog)

        # تحميل البيانات
        self.load_items_data()

        # إنشاء الواجهة
        self.create_interface()

        # توسيط النافذة
        self.center_window()

        # التركيز على حقل البحث
        self.root.after(100, lambda: self.focus_search_field())

    def load_items_data(self):
        """تحميل بيانات الأصناف"""
        try:
            from database.database_manager import DatabaseManager
            db_manager = DatabaseManager()
            conn = db_manager.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT i.id, i.item_code, i.item_name, i.description,
                       i.unit_of_measure as unit, i.selling_price, i.cost_price,
                       COALESCE(c.category_name, 'غير محدد') as category_name,
                       i.current_stock, i.barcode
                FROM items i
                LEFT JOIN item_categories c ON i.category_id = c.id
                WHERE i.is_active = 1
                ORDER BY i.item_name
            """)

            items = cursor.fetchall()
            conn.close()

            # تحويل البيانات للتنسيق المطلوب
            self.items_data = []
            for item in items:
                item_data = {
                    'id': item['id'],
                    'item_code': item['item_code'] or '',
                    'item_name': item['item_name'] or '',
                    'description': item['description'] or '',
                    'unit': item['unit'] or 'قطعة',
                    'unit_price': str(item['selling_price'] or item['cost_price'] or 0),
                    'category': item['category_name'] or 'غير محدد',
                    'current_stock': str(item['current_stock'] or 0),
                    'barcode': item['barcode'] or ''
                }
                self.items_data.append(item_data)

            self.filtered_items = self.items_data.copy()
            print(f"✅ تم تحميل {len(self.items_data)} صنف للبحث المبسط")

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الأصناف: {e}")
            self.items_data = []
            self.filtered_items = []

    def create_interface(self):
        """إنشاء واجهة البحث"""
        # الإطار الرئيسي
        main_frame = create_simple_rtl_frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = create_simple_rtl_label(
            main_frame,
            text="🔍 البحث في الأصناف - للنموذج المبسط",
            font=('Arial', 16, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 20))

        # حقل البحث
        search_frame = create_simple_rtl_frame(main_frame)
        search_frame.pack(fill='x', pady=(0, 20))

        search_label = create_simple_rtl_label(
            search_frame,
            text="🔍 البحث:",
            font=('Arial', 12, 'bold')
        )
        search_label.pack(side='right', padx=(0, 10))

        self.search_var = tk.StringVar()
        self.search_entry = create_simple_rtl_entry(
            search_frame,
            textvariable=self.search_var,
            font=('Arial', 12),
            width=50
        )
        self.search_entry.pack(side='right', fill='x', expand=True)
        self.search_entry.bind('<KeyRelease>', self.on_search_change)

        # جدول النتائج
        self.create_results_tree(main_frame)

        # أزرار التحكم
        self.create_control_buttons(main_frame)

    def create_results_tree(self, parent):
        """إنشاء جدول النتائج"""
        # إطار الجدول
        tree_frame = create_simple_rtl_frame(parent)
        tree_frame.pack(fill='both', expand=True, pady=(0, 20))

        # الأعمدة
        columns = ('item_code', 'item_name', 'description', 'unit', 'unit_price', 'category', 'stock')

        self.results_tree = ttk.Treeview(
            tree_frame,
            columns=columns,
            show='tree headings',
            height=15
        )

        # تعيين عناوين الأعمدة
        self.results_tree.heading('#0', text='#', anchor='center')
        self.results_tree.heading('item_code', text='كود الصنف', anchor='center')
        self.results_tree.heading('item_name', text='اسم الصنف', anchor='center')
        self.results_tree.heading('description', text='الوصف', anchor='center')
        self.results_tree.heading('unit', text='الوحدة', anchor='center')
        self.results_tree.heading('unit_price', text='السعر', anchor='center')
        self.results_tree.heading('category', text='الفئة', anchor='center')
        self.results_tree.heading('stock', text='المخزون', anchor='center')

        # تعيين عرض الأعمدة
        self.results_tree.column('#0', width=50, minwidth=50)
        self.results_tree.column('item_code', width=100, minwidth=80)
        self.results_tree.column('item_name', width=200, minwidth=150)
        self.results_tree.column('description', width=200, minwidth=150)
        self.results_tree.column('unit', width=80, minwidth=60)
        self.results_tree.column('unit_price', width=100, minwidth=80)
        self.results_tree.column('category', width=120, minwidth=100)
        self.results_tree.column('stock', width=80, minwidth=60)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(tree_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.results_tree.pack(side='right', fill='both', expand=True)
        scrollbar.pack(side='left', fill='y')

        # ربط الأحداث
        self.results_tree.bind('<<TreeviewSelect>>', self.on_result_select)
        self.results_tree.bind('<Double-1>', self.on_result_double_click)

        # تحديث النتائج
        self.update_results_tree()

    def on_search_change(self, event=None):
        """عند تغيير نص البحث"""
        try:
            search_text = self.search_var.get().strip().lower()

            if not search_text:
                # إذا كان البحث فارغ، عرض جميع الأصناف
                self.filtered_items = self.items_data.copy()
            else:
                # فلترة الأصناف حسب النص
                self.filtered_items = []
                for item in self.items_data:
                    # البحث في كود الصنف واسم الصنف والوصف
                    if (search_text in item.get('item_code', '').lower() or
                        search_text in item.get('item_name', '').lower() or
                        search_text in item.get('description', '').lower() or
                        search_text in item.get('category', '').lower()):
                        self.filtered_items.append(item)

            # تحديث الجدول
            self.update_results_tree()
            self.update_results_count()

        except Exception as e:
            print(f"❌ خطأ في البحث: {e}")

    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        control_frame = create_simple_rtl_frame(parent)
        control_frame.pack(fill='x')

        # زر الاختيار
        select_btn = create_simple_rtl_button(
            control_frame,
            text="✅ اختيار الصنف",
            button_type="success",
            command=self.select_item
        )
        select_btn.pack(side='right', padx=5)

        # زر الإغلاق
        close_btn = create_simple_rtl_button(
            control_frame,
            text="❌ إغلاق",
            button_type="danger",
            command=self.close_dialog
        )
        close_btn.pack(side='right', padx=5)

        # عداد النتائج
        self.results_label = create_simple_rtl_label(
            control_frame,
            text=f"📊 إجمالي الأصناف: {len(self.items_data)}",
            font=('Arial', 10)
        )
        self.results_label.pack(side='left')

    def on_search_change(self, event=None):
        """عند تغيير نص البحث"""
        search_text = self.search_var.get().strip().lower()

        if not search_text:
            self.filtered_items = self.items_data.copy()
        else:
            self.filtered_items = []
            for item in self.items_data:
                # البحث في كود الصنف واسم الصنف والوصف
                if (search_text in item.get('item_code', '').lower() or
                    search_text in item.get('item_name', '').lower() or
                    search_text in item.get('description', '').lower() or
                    search_text in item.get('category', '').lower()):
                    self.filtered_items.append(item)

        self.update_results_tree()
        self.update_results_count()

    def update_results_tree(self):
        """تحديث جدول النتائج"""
        try:
            # مسح البيانات الحالية
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

            # إضافة النتائج
            for i, item_data in enumerate(self.filtered_items):
                self.add_result_to_tree(item_data, i + 1)

        except Exception as e:
            print(f"❌ خطأ في تحديث جدول النتائج: {e}")

    def update_results_count(self):
        """تحديث عداد النتائج"""
        try:
            total_items = len(self.items_data)
            filtered_items = len(self.filtered_items)

            if hasattr(self, 'results_label'):
                if filtered_items == total_items:
                    self.results_label.configure(text=f"📊 إجمالي الأصناف: {total_items}")
                else:
                    self.results_label.configure(text=f"📊 النتائج: {filtered_items} من {total_items}")
        except Exception as e:
            print(f"❌ خطأ في تحديث عداد النتائج: {e}")

    def add_result_to_tree(self, item_data, index):
        """إضافة نتيجة للجدول"""
        try:
            # تحضير الوصف مع قطع النص الطويل
            description = item_data.get('description', '')
            if len(description) > 40:
                description = description[:37] + "..."

            values = [
                item_data.get('item_code', ''),
                item_data.get('item_name', ''),
                description,
                item_data.get('unit', ''),
                f"{float(item_data.get('unit_price', 0)):,.2f}",
                item_data.get('category', ''),
                item_data.get('current_stock', '0')
            ]

            # إدراج العنصر
            item = self.results_tree.insert(
                '', 'end',
                text=str(index),
                values=values
            )

        except Exception as e:
            print(f"❌ خطأ في إضافة نتيجة للجدول: {e}")

    def update_results_count(self):
        """تحديث عداد النتائج"""
        total_items = len(self.items_data)
        filtered_items = len(self.filtered_items)

        if filtered_items == total_items:
            self.results_label.configure(text=f"📊 إجمالي الأصناف: {total_items}")
        else:
            self.results_label.configure(text=f"📊 النتائج: {filtered_items} من {total_items}")

    def on_result_select(self, event):
        """عند اختيار نتيجة"""
        selection = self.results_tree.selection()
        if selection:
            item = selection[0]
            values = self.results_tree.item(item, 'values')

            if values:
                # البحث عن بيانات الصنف الكاملة
                item_code = values[0]
                for item_data in self.filtered_items:
                    if item_data.get('item_code') == item_code:
                        self.selected_item = item_data
                        break

    def on_result_double_click(self, event):
        """عند النقر المزدوج على نتيجة"""
        self.select_item()

    def select_item(self):
        """اختيار الصنف وإرجاع البيانات"""
        if not self.selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف من النتائج")
            return

        # ملء النموذج المبسط قبل الإغلاق
        self.main_form.fill_simple_form_from_search(self.selected_item)

        # إغلاق النافذة
        self.close_dialog()

    def close_dialog(self):
        """إغلاق النافذة"""
        self.root.destroy()

    def focus_search_field(self):
        """تركيز على حقل البحث"""
        self.search_entry.focus_set()

    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def highlight_item_in_tree(self, item_data):
        """تمييز صنف في الجدول"""
        try:
            # البحث عن الصنف في الجدول
            for item in self.items_tree.get_children():
                item_text = self.items_tree.item(item, 'text')
                if f"ID: {item_data.get('id')}" in item_text:
                    # تحديد الصنف
                    self.items_tree.selection_set(item)
                    self.items_tree.focus(item)
                    self.items_tree.see(item)

                    # تحديث الصنف المحدد
                    self.selected_item = item_data
                    break

        except Exception as e:
            print(f"خطأ في تمييز الصنف: {e}")



    # وظائف الأحداث والتفاعل
    def bind_events(self):
        """ربط الأحداث"""
        # اختصارات لوحة المفاتيح
        self.root.bind('<Control-s>', lambda e: self.save_shipment())
        self.root.bind('<Control-w>', lambda e: self.close_form())
        self.root.bind('<Escape>', lambda e: self.close_form())
        self.root.bind('<F1>', lambda e: self.show_help())
        self.root.bind('<F2>', lambda e: self.show_status_info())
        self.root.bind('<F11>', lambda e: self.toggle_fullscreen())

        # أحداث التنقل
        self.root.bind('<Control-Right>', lambda e: self.next_section())
        self.root.bind('<Control-Left>', lambda e: self.prev_section())
        self.root.bind('<Page_Down>', lambda e: self.next_section())
        self.root.bind('<Page_Up>', lambda e: self.prev_section())

        # أحداث الأرقام للانتقال المباشر للأقسام
        for i in range(1, 9):
            self.root.bind(f'<Control-Key-{i}>', lambda e, s=i-1: self.switch_section(s))

    def add_document_link(self, var_name, document_name):
        """إضافة رابط لمستند معين"""
        try:
            print(f"🔗 محاولة إضافة رابط لـ: {document_name} ({var_name})")

            # نافذة إدخال الرابط
            link_dialog = tk.Toplevel(self.root)
            link_dialog.title(f"إدخال رابط - {document_name}")
            link_dialog.geometry("600x400")
            link_dialog.resizable(True, True)
            link_dialog.transient(self.root)
            link_dialog.grab_set()

            # تطبيق نمط RTL
            link_dialog.configure(bg=COLORS['background'])

            # إطار المحتوى
            content_frame = create_simple_rtl_frame(link_dialog)
            content_frame.pack(fill='both', expand=True, padx=30, pady=30)

            # العنوان
            title_label = create_simple_rtl_label(
                content_frame,
                text=f"🔗 إدخال رابط {document_name}",
                font=('Segoe UI', 18, 'bold'),
                fg=COLORS['primary']
            )
            title_label.pack(anchor='e', pady=(0, 20))

            # تسمية الرابط
            link_label = create_simple_rtl_label(
                content_frame,
                text="🌐 رابط المستند:",
                font=('Segoe UI', 14, 'bold')
            )
            link_label.pack(anchor='e', pady=(0, 10))

            # حقل الرابط
            link_var = tk.StringVar()
            current_link = self.form_vars[var_name].get()
            if current_link:
                link_var.set(current_link)

            link_entry = create_simple_rtl_entry(
                content_frame,
                textvariable=link_var,
                placeholder="https://example.com/document.pdf"
            )
            link_entry.configure(font=('Segoe UI', 12, 'normal'))
            link_entry.pack(fill='x', ipady=10, pady=(0, 20))
            link_entry.focus()

            # تسمية الوصف
            desc_label = create_simple_rtl_label(
                content_frame,
                text="📝 وصف المستند (اختياري):",
                font=('Segoe UI', 14, 'bold')
            )
            desc_label.pack(anchor='e', pady=(0, 10))

            # حقل الوصف
            desc_var = tk.StringVar()
            desc_entry = create_simple_rtl_entry(
                content_frame,
                textvariable=desc_var,
                placeholder="وصف مختصر للمستند"
            )
            desc_entry.configure(font=('Segoe UI', 12, 'normal'))
            desc_entry.pack(fill='x', ipady=10, pady=(0, 20))

            # إطار الأزرار
            buttons_frame = create_simple_rtl_frame(content_frame)
            buttons_frame.pack(fill='x', pady=(20, 0))

            def save_link():
                """حفظ الرابط"""
                link_url = link_var.get().strip()
                description = desc_var.get().strip()

                if not link_url:
                    messagebox.showwarning("تحذير", "يرجى إدخال رابط المستند")
                    return

                # التحقق من صحة الرابط
                if not (link_url.startswith('http://') or link_url.startswith('https://') or link_url.startswith('ftp://')):
                    if not link_url.startswith('www.'):
                        link_url = 'https://' + link_url
                    else:
                        link_url = 'https://' + link_url

                # حفظ الرابط
                self.form_vars[var_name].set(link_url)
                self.document_links[var_name] = {
                    'url': link_url,
                    'description': description,
                    'added_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }

                # تحديث معلومات الرابط
                self.update_link_info_display(var_name, document_name)

                print(f"✅ تم حفظ الرابط لـ: {document_name}")
                messagebox.showinfo("تم الحفظ", f"تم حفظ رابط {document_name} بنجاح")
                link_dialog.destroy()

            def test_link():
                """اختبار الرابط"""
                link_url = link_var.get().strip()
                if link_url:
                    self.open_link_in_browser(link_url)
                else:
                    messagebox.showwarning("تحذير", "يرجى إدخال رابط أولاً")

            # زر الحفظ
            save_btn = create_simple_rtl_button(
                buttons_frame,
                text="💾 حفظ الرابط",
                button_type="primary",
                command=save_link
            )
            save_btn.pack(side='left', padx=(0, 10))

            # زر الاختبار
            test_btn = create_simple_rtl_button(
                buttons_frame,
                text="🧪 اختبار الرابط",
                button_type="secondary",
                command=test_link
            )
            test_btn.pack(side='left', padx=(0, 10))

            # زر الإلغاء
            cancel_btn = create_simple_rtl_button(
                buttons_frame,
                text="❌ إلغاء",
                button_type="ghost",
                command=link_dialog.destroy
            )
            cancel_btn.pack(side='right')

            # ربط مفتاح Enter بالحفظ
            link_entry.bind('<Return>', lambda e: save_link())
            desc_entry.bind('<Return>', lambda e: save_link())

        except Exception as e:
            print(f"❌ خطأ في إضافة الرابط: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"خطأ في إضافة الرابط: {str(e)}")

    def open_document_link(self, var_name, document_name):
        """فتح رابط المستند"""
        try:
            link_url = self.form_vars[var_name].get().strip()

            if not link_url:
                messagebox.showinfo("لا يوجد رابط", f"لا يوجد رابط محفوظ لـ {document_name}")
                return

            print(f"🔗 فتح رابط {document_name}: {link_url}")
            self.open_link_in_browser(link_url)

        except Exception as e:
            print(f"❌ خطأ في فتح الرابط: {e}")
            messagebox.showerror("خطأ", f"خطأ في فتح الرابط: {str(e)}")

    def open_link_in_browser(self, url):
        """فتح الرابط في المتصفح"""
        try:
            import webbrowser

            # التأكد من وجود بروتوكول
            if not (url.startswith('http://') or url.startswith('https://') or url.startswith('ftp://')):
                url = 'https://' + url

            webbrowser.open(url)
            print(f"✅ تم فتح الرابط: {url}")

        except Exception as e:
            print(f"❌ خطأ في فتح المتصفح: {e}")
            messagebox.showerror("خطأ", f"خطأ في فتح الرابط في المتصفح: {str(e)}")

    def update_link_info_display(self, var_name, document_name):
        """تحديث عرض معلومات الرابط"""
        try:
            link_info_label = getattr(self, f'{var_name}_link_info_label', None)
            if not link_info_label:
                return

            if var_name in self.document_links and self.document_links[var_name]:
                link_data = self.document_links[var_name]
                url = link_data.get('url', '')
                description = link_data.get('description', '')
                added_date = link_data.get('added_date', '')

                # عرض معلومات مختصرة
                display_url = url[:50] + "..." if len(url) > 50 else url
                info_text = f"🔗 {display_url}"

                if description:
                    info_text += f" | 📝 {description}"

                if added_date:
                    info_text += f" | 📅 {added_date}"

                link_info_label.configure(text=info_text, fg='#2563eb')
            else:
                link_info_label.configure(text="", fg='#6b7280')

        except Exception as e:
            print(f"❌ خطأ في تحديث معلومات الرابط: {e}")

    def view_attachments(self):
        """عرض المرفقات"""
        attachments = list(self.attachments_list.get(0, tk.END))

        if not attachments:
            messagebox.showinfo("لا توجد مرفقات", "لا توجد ملفات مرفقة")
            return

        attachments_text = "\n".join(f"• {att}" for att in attachments)
        messagebox.showinfo("المرفقات", f"الملفات المرفقة:\n\n{attachments_text}")

    def remove_attachment(self):
        """حذف مرفق"""
        selection = self.attachments_list.curselection()
        if selection:
            self.attachments_list.delete(selection[0])
        else:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف للحذف")

    def show_status_info(self):
        """عرض معلومات الحالة الحالية"""
        try:
            current_status = self.form_vars.get('status', tk.StringVar()).get().strip()
            if not current_status:
                return

            status_info = self.get_status_info_message(current_status)
            required_sections = self.get_required_sections_by_status(current_status)

            # أسماء الأقسام
            section_names = [
                "📋 المعلومات الأساسية",
                "📦 الأصناف",
                "🚢 معلومات الشحن",
                "📦 معلومات الحاوية",
                "💰 المعلومات المالية",
                "📄 المستندات",
                "📊 التتبع والحالة",
                "📝 الملاحظات والمرفقات"
            ]

            required_section_names = [section_names[i] for i in required_sections]

            info_message = f"""
🔄 حالة الشحنة الحالية: {current_status}

📋 الأقسام المطلوبة للحفظ:
{chr(10).join(f"✓ {name}" for name in required_section_names)}

💡 ملاحظة: يمكنك حفظ الشحنة بملء الأقسام المطلوبة فقط.
الأقسام الأخرى اختيارية ويمكن ملؤها لاحقاً.

🎯 نصيحة: استخدم مفتاح Tab للتنقل السريع بين الحقول.
            """

            messagebox.showinfo("معلومات الحالة", info_message)

        except Exception as e:
            print(f"خطأ في عرض معلومات الحالة: {e}")

    def get_status_info_message(self, status):
        """الحصول على رسالة معلومات الحالة"""
        status_info = {
            'تحت الطلب': {
                'message': 'حالة "تحت الطلب" - يكفي ملء البيانات الأساسية فقط',
                'sections': 'الأقسام المطلوبة: المعلومات الأساسية',
                'color': '#FFA500'
            },
            'في الانتظار': {
                'message': 'حالة "في الانتظار" - يكفي ملء البيانات الأساسية',
                'sections': 'الأقسام المطلوبة: المعلومات الأساسية',
                'color': '#4CAF50'
            },
            'مؤكدة': {
                'message': 'حالة "مؤكدة" - مطلوب البيانات الأساسية والأصناف',
                'sections': 'الأقسام المطلوبة: المعلومات الأساسية، الأصناف',
                'color': '#2196F3'
            },
            'تم الشحن': {
                'message': 'حالة "تم الشحن" - مطلوب البيانات الأساسية والأصناف ومعلومات الشحن والحاوية والمالية والمستندات',
                'sections': 'الأقسام المطلوبة: الأساسية، الأصناف، الشحن، الحاوية، المالية، المستندات',
                'color': '#FF9800'
            },
            'في الطريق': {
                'message': 'حالة "في الطريق" - مطلوب معظم البيانات للتتبع',
                'sections': 'الأقسام المطلوبة: الأساسية، الأصناف، الشحن، الحاوية، التتبع',
                'color': '#9C27B0'
            },
            'وصلت': {
                'message': 'حالة "وصلت" - مطلوب البيانات المالية والتتبع',
                'sections': 'الأقسام المطلوبة: الأساسية، الأصناف، الشحن، الحاوية، المالية، التتبع',
                'color': '#607D8B'
            },
            'في الجمارك': {
                'message': 'حالة "في الجمارك" - مطلوب جميع البيانات والمستندات',
                'sections': 'الأقسام المطلوبة: جميع الأقسام عدا الملاحظات',
                'color': '#795548'
            },
            'تم التسليم': {
                'message': 'حالة "تم التسليم" - مطلوب جميع البيانات مكتملة',
                'sections': 'الأقسام المطلوبة: جميع الأقسام',
                'color': '#4CAF50'
            }
        }

        return status_info.get(status, {
            'message': 'حالة غير محددة - يرجى ملء البيانات الأساسية',
            'sections': 'الأقسام المطلوبة: المعلومات الأساسية',
            'color': '#757575'
        })

    # وظائف الحفظ والإدارة
    def save_shipment(self):
        """حفظ الشحنة"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_form():
                return False

            # جمع البيانات
            shipment_data = self.collect_form_data()

            # حفظ في قاعدة البيانات
            success = False
            if self.mode == 'add' or self.mode == 'duplicate':
                success = self.create_new_shipment(shipment_data)
            elif self.mode == 'edit':
                success = self.update_existing_shipment(shipment_data)

            # تحديث المؤشرات إذا نجح الحفظ
            if success:
                self.is_modified = False
                self.modified_indicator.configure(text="✓ تم الحفظ", fg=COLORS['success'])
                self.status_label.configure(text="تم الحفظ بنجاح")
                return True
            elif success:
                # تحديث آخر تحديث
                self.last_update_display.configure(text=datetime.now().strftime('%Y/%m/%d %H:%M:%S'))
                return True
            else:
                return False

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الشحنة: {str(e)}")
            return False

    def get_required_fields_by_status(self, status):
        """تحديد الحقول المطلوبة حسب حالة الشحنة"""
        # الحقول الأساسية المطلوبة دائماً
        basic_required = {
            'shipment_number': 'رقم الشحنة',
            'supplier_id': 'المورد',
            'status': 'حالة الشحنة'
        }

        # الحقول المطلوبة حسب الحالة
        status_requirements = {
            'تحت الطلب': {
                # فقط الحقول الأساسية
                **basic_required
            },
            'في الانتظار': {
                **basic_required,
                'shipment_date': 'تاريخ الشحن'
            },
            'مؤكدة': {
                **basic_required,
                'shipment_date': 'تاريخ الشحن',
                'expected_arrival_date': 'تاريخ الوصول المتوقع'
            },
            'تم الشحن': {
                **basic_required,
                'shipment_date': 'تاريخ الشحن',
                'expected_arrival_date': 'تاريخ الوصول المتوقع',
                'departure_port': 'ميناء المغادرة',
                'arrival_port': 'ميناء الوصول',
                'shipping_company': 'شركة الشحن',
                'container_number': 'رقم الحاوية',
                'container_type': 'نوع الحاوية',
                'total_value': 'القيمة الإجمالية',
                'payment_method': 'طريقة الدفع',
                'invoice_document': 'فاتورة المورد',
                'packing_list': 'قائمة التعبئة'
            },
            'في الطريق': {
                **basic_required,
                'shipment_date': 'تاريخ الشحن',
                'expected_arrival_date': 'تاريخ الوصول المتوقع',
                'departure_port': 'ميناء المغادرة',
                'arrival_port': 'ميناء الوصول',
                'shipping_company': 'شركة الشحن',
                'container_number': 'رقم الحاوية',
                'tracking_number': 'رقم التتبع'
            },
            'وصلت': {
                **basic_required,
                'shipment_date': 'تاريخ الشحن',
                'expected_arrival_date': 'تاريخ الوصول المتوقع',
                'actual_arrival_date': 'تاريخ الوصول الفعلي',
                'departure_port': 'ميناء المغادرة',
                'arrival_port': 'ميناء الوصول',
                'shipping_company': 'شركة الشحن',
                'container_number': 'رقم الحاوية'
            },
            'في الجمارك': {
                **basic_required,
                'shipment_date': 'تاريخ الشحن',
                'expected_arrival_date': 'تاريخ الوصول المتوقع',
                'actual_arrival_date': 'تاريخ الوصول الفعلي',
                'departure_port': 'ميناء المغادرة',
                'arrival_port': 'ميناء الوصول',
                'shipping_company': 'شركة الشحن',
                'container_number': 'رقم الحاوية',
                'bill_of_lading': 'بوليصة الشحن'
            },
            'تم التسليم': {
                **basic_required,
                'shipment_date': 'تاريخ الشحن',
                'expected_arrival_date': 'تاريخ الوصول المتوقع',
                'actual_arrival_date': 'تاريخ الوصول الفعلي',
                'departure_port': 'ميناء المغادرة',
                'arrival_port': 'ميناء الوصول',
                'shipping_company': 'شركة الشحن',
                'container_number': 'رقم الحاوية',
                'bill_of_lading': 'بوليصة الشحن',
                'total_value': 'القيمة الإجمالية'
            }
        }

        # إرجاع الحقول المطلوبة للحالة المحددة أو الحقول الأساسية
        return status_requirements.get(status, basic_required)

    def get_required_sections_by_status(self, status):
        """تحديد الأقسام المطلوبة حسب حالة الشحنة"""
        section_requirements = {
            'تحت الطلب': [0],  # فقط القسم الأساسي
            'في الانتظار': [0],  # فقط القسم الأساسي
            'مؤكدة': [0, 1],  # الأساسي + الأصناف
            'تم الشحن': [0, 1, 2, 3, 4, 5],  # الأساسي + الأصناف + الشحن + الحاوية + المالية + المستندات
            'في الطريق': [0, 1, 2, 3, 6],  # الأساسي + الأصناف + الشحن + الحاوية + التتبع
            'وصلت': [0, 1, 2, 3, 4, 6],  # الأساسي + الأصناف + الشحن + الحاوية + المالية + التتبع
            'في الجمارك': [0, 1, 2, 3, 4, 5, 6],  # جميع الأقسام عدا الملاحظات
            'تم التسليم': [0, 1, 2, 3, 4, 5, 6, 7],  # جميع الأقسام
            'ملغية': [0, 7],  # الأساسي + الملاحظات
            'متأخرة': [0, 1, 2, 6, 7]  # الأساسي + الأصناف + الشحن + التتبع + الملاحظات
        }

        return section_requirements.get(status, [0])  # افتراضياً القسم الأساسي فقط

    def validate_form(self):
        """التحقق من صحة النموذج بناءً على حالة الشحنة"""
        errors = []

        # الحصول على حالة الشحنة الحالية
        current_status = self.form_vars.get('status', tk.StringVar()).get().strip()

        # الحصول على الحقول المطلوبة للحالة الحالية
        required_fields = self.get_required_fields_by_status(current_status)

        # التحقق من الحقول المطلوبة
        for field, label in required_fields.items():
            value = self.form_vars.get(field, tk.StringVar()).get().strip()
            if not value:
                errors.append(f"حقل {label} مطلوب لحالة '{current_status}'")

        # التحقق من صحة التواريخ (فقط للحقول المملوءة)
        date_fields = ['shipment_date', 'expected_arrival_date', 'actual_departure_date', 'actual_arrival_date']
        for field in date_fields:
            value = self.form_vars.get(field, tk.StringVar()).get().strip()
            if value and not self.is_valid_date(value):
                errors.append(f"تاريخ {field} غير صحيح (استخدم YYYY-MM-DD)")

        # التحقق من صحة الأرقام (فقط للحقول المملوءة)
        numeric_fields = ['total_value', 'shipping_cost', 'weight', 'volume', 'pieces_count']
        for field in numeric_fields:
            value = self.form_vars.get(field, tk.StringVar()).get().strip()
            if value and not self.is_valid_number(value):
                errors.append(f"قيمة {field} يجب أن تكون رقماً")

        # عرض الأخطاء إن وجدت
        if errors:
            error_message = f"يرجى تصحيح الأخطاء التالية لحالة '{current_status}':\n\n" + "\n".join(f"• {error}" for error in errors)
            messagebox.showerror("أخطاء في البيانات", error_message)
            return False

        return True

    def is_valid_date(self, date_string):
        """التحقق من صحة التاريخ"""
        try:
            datetime.strptime(date_string, '%Y-%m-%d')
            return True
        except:
            return False

    def is_valid_number(self, number_string):
        """التحقق من صحة الرقم"""
        try:
            float(number_string)
            return True
        except:
            return False

    def collect_form_data(self):
        """جمع بيانات النموذج"""
        data = {}

        # جمع البيانات من المتغيرات
        for field, var in self.form_vars.items():
            value = var.get().strip()
            data[field] = value if value else None

        # معالجة خاصة للتواريخ والقيم الافتراضية
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        data['created_at'] = current_time
        data['updated_at'] = current_time
        data['created_by'] = auth_manager.current_user.get('username', 'system') if auth_manager.current_user else 'system'

        # إنشاء رقم شحنة تلقائي إذا لم يكن موجوداً
        if not data.get('shipment_number'):
            data['shipment_number'] = self.generate_shipment_number()

        # معالجة خاصة للمورد
        supplier_text = self.form_vars.get('supplier_id', tk.StringVar()).get()
        if supplier_text and '(ID:' in supplier_text:
            try:
                supplier_id = int(supplier_text.split('(ID:')[1].split(')')[0].strip())
                data['supplier_id'] = supplier_id
            except:
                data['supplier_id'] = None

        # جمع الملاحظات
        notes = self.notes_text.get('1.0', tk.END).strip()
        data['notes'] = notes if notes else None

        # جمع وصف البضائع
        goods_desc = self.goods_description_text.get('1.0', tk.END).strip()
        data['goods_description'] = goods_desc if goods_desc else None

        # جمع ملاحظات المستندات
        if hasattr(self, 'documents_notes_text'):
            docs_notes = self.documents_notes_text.get('1.0', tk.END).strip()
            data['documents_notes'] = docs_notes if docs_notes else None

        # جمع المرفقات
        attachments = list(self.attachments_list.get(0, tk.END))
        data['attachments'] = json.dumps(attachments) if attachments else None

        # معالجة خاصة لأرقام الحاويات المتعددة
        container_numbers = []
        container_count = int(data.get('container_count', 1) or 1)

        for i in range(1, container_count + 1):
            var_name = f'container_number_{i}' if i > 1 else 'container_number'
            container_num = data.get(var_name, '').strip()
            if container_num and container_num != "XXXX-XXXXXXX-X":
                container_numbers.append(container_num)

        # حفظ أرقام الحاويات كـ JSON
        data['container_numbers_json'] = json.dumps(container_numbers) if container_numbers else None
        # الاحتفاظ بالحقل الأول للتوافق مع النظام الحالي
        data['container_number'] = container_numbers[0] if container_numbers else None

        # إضافة معلومات النظام
        current_user = auth_manager.get_current_user()
        data['created_by'] = current_user.get('username', 'system')
        data['updated_at'] = datetime.now().isoformat()

        if self.mode == 'add' or self.mode == 'duplicate':
            data['id'] = str(uuid.uuid4())
            data['created_at'] = datetime.now().isoformat()

        return data

    def create_new_shipment(self, data):
        """إنشاء شحنة جديدة"""
        query = """
            INSERT INTO advanced_shipments (
                shipment_number, customer_name, supplier_name, supplier_invoice_number, departure_port, arrival_port,
                shipment_date, expected_arrival_date, status, priority, total_value, currency,
                container_type, container_number, container_count, container_numbers_json, seal_number,
                weight, volume, pieces_count, tracking_number, shipping_company, vessel_name, voyage_number,
                bill_of_lading, customs_status, release_status, insurance_cost, shipping_cost, additional_fees,
                notes, documents_json, created_by, created_at, updated_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
            )
        """

        params = (
            data.get('shipment_number'),
            data.get('customer_name', ''),  # اسم العميل
            data.get('supplier_name', ''),  # اسم المورد
            data.get('supplier_invoice_number', ''),  # رقم فاتورة المورد
            data.get('departure_port', ''),  # ميناء المنشأ
            data.get('arrival_port', ''),    # ميناء الوصول
            data.get('shipment_date'),
            data.get('expected_arrival_date'),
            data.get('status', 'في الانتظار'),
            data.get('priority', 'عادي'),
            data.get('total_value', 0),
            data.get('currency', 'USD'),
            data.get('container_type'),
            data.get('container_number'),
            data.get('container_count', 1),
            data.get('container_numbers_json'),
            data.get('seal_number'),
            data.get('weight', 0),
            data.get('volume', 0),
            data.get('pieces_count', 0),
            data.get('tracking_number'),
            data.get('shipping_company', ''),  # خط الشحن
            data.get('vessel_name'),
            data.get('voyage_number'),
            data.get('bill_of_lading'),
            data.get('customs_status', 'لم يتم التخليص'),
            data.get('release_status', 'بدون إفراج'),  # حالة الإفراج الجديدة
            data.get('insurance_cost', 0),
            data.get('shipping_cost', 0),
            data.get('additional_fees', 0),
            data.get('notes'),
            data.get('attachments'),  # documents_json
            data.get('created_by'),
            data.get('created_at'),
            data.get('updated_at')
        )

        # تنفيذ الاستعلام والحصول على معرف الشحنة
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute(query, params)
            shipment_id = cursor.lastrowid
            conn.commit()

            # حفظ الأصناف
            try:
                self.save_shipment_items(shipment_id)
            except Exception as items_error:
                print(f"❌ خطأ في حفظ الأصناف: {items_error}")
                # الشحنة تم حفظها بنجاح، لكن الأصناف فشلت
                messagebox.showwarning("تحذير", f"تم حفظ الشحنة بنجاح ولكن فشل في حفظ الأصناف:\n{str(items_error)}")
                return True  # الشحنة نفسها نجحت

        except Exception as e:
            conn.rollback()
            print(f"❌ خطأ في إنشاء الشحنة: {e}")
            messagebox.showerror("خطأ", f"فشل في حفظ الشحنة:\n{str(e)}")
            return False
        finally:
            conn.close()

        messagebox.showinfo("نجح", "تم حفظ الشحنة بنجاح!")
        return True

    def update_existing_shipment(self, data):
        """تحديث شحنة موجودة"""
        query = """
            UPDATE advanced_shipments SET
                shipment_number = ?, customer_name = ?, supplier_name = ?, supplier_invoice_number = ?, departure_port = ?,
                arrival_port = ?, shipment_date = ?, expected_arrival_date = ?, status = ?,
                priority = ?, total_value = ?, currency = ?, container_type = ?,
                container_number = ?, container_count = ?, container_numbers_json = ?, seal_number = ?,
                weight = ?, volume = ?, pieces_count = ?, tracking_number = ?, shipping_company = ?,
                vessel_name = ?, voyage_number = ?, bill_of_lading = ?, customs_status = ?,
                release_status = ?, insurance_cost = ?, shipping_cost = ?, additional_fees = ?,
                notes = ?, documents_json = ?, updated_at = ?
            WHERE id = ?
        """

        params = (
            data.get('shipment_number'),
            data.get('customer_name', ''),
            data.get('supplier_name', ''),
            data.get('supplier_invoice_number', ''),
            data.get('departure_port', ''),
            data.get('arrival_port', ''),
            data.get('shipment_date'),
            data.get('expected_arrival_date'),
            data.get('status', 'في الانتظار'),
            data.get('priority', 'عادي'),
            data.get('total_value', 0),
            data.get('currency', 'USD'),
            data.get('container_type'),
            data.get('container_number'),
            data.get('container_count', 1),
            data.get('container_numbers_json'),
            data.get('seal_number'),
            data.get('weight', 0),
            data.get('volume', 0),
            data.get('pieces_count', 0),
            data.get('tracking_number'),
            data.get('shipping_company', ''),
            data.get('vessel_name'),
            data.get('voyage_number'),
            data.get('bill_of_lading'),
            data.get('customs_status', 'لم يتم التخليص'),
            data.get('release_status', 'بدون إفراج'),  # حالة الإفراج الجديدة
            data.get('insurance_cost', 0),
            data.get('shipping_cost', 0),
            data.get('additional_fees', 0),
            data.get('notes'),
            data.get('attachments'),
            data.get('updated_at'),
            self.shipment_data.get('id')
        )

        try:
            self.db_manager.execute_query(query, params)

            # حفظ الأصناف
            try:
                self.save_shipment_items(self.shipment_data.get('id'))
            except Exception as items_error:
                print(f"❌ خطأ في حفظ الأصناف: {items_error}")
                # الشحنة تم تحديثها بنجاح، لكن الأصناف فشلت
                messagebox.showwarning("تحذير", f"تم تحديث الشحنة بنجاح ولكن فشل في حفظ الأصناف:\n{str(items_error)}")
                return True  # الشحنة نفسها نجحت

            messagebox.showinfo("نجح", "تم تحديث الشحنة بنجاح!")
            return True

        except Exception as e:
            print(f"❌ خطأ في تحديث الشحنة: {e}")
            messagebox.showerror("خطأ", f"فشل في تحديث الشحنة:\n{str(e)}")
            return False

    def save_shipment_items(self, shipment_id):
        """حفظ أصناف الشحنة"""
        try:
            # حذف الأصناف الموجودة
            self.db_manager.execute_query(
                "DELETE FROM shipment_items WHERE shipment_id = ?",
                (shipment_id,)
            )

            # إضافة الأصناف الجديدة
            for item in self.items_data:
                query = """
                    INSERT INTO shipment_items (
                        id, shipment_id, item_code, item_name, item_id, warehouse_id,
                        description, quantity, unit, unit_price, total_price, weight,
                        volume, hs_code, origin_country, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                # إنشاء معرف فريد للصنف إذا لم يكن موجوداً
                item_id = item.get('id') or str(uuid.uuid4())

                params = (
                    item_id,
                    shipment_id,
                    item.get('item_code'),
                    item.get('item_name'),
                    item.get('item_id'),
                    item.get('warehouse_id'),
                    item.get('description'),
                    float(item.get('quantity', 0)),
                    item.get('unit'),
                    float(item.get('unit_price', 0)),
                    float(item.get('total_price', 0)),
                    float(item.get('weight', 0)),
                    float(item.get('volume', 0)),
                    item.get('hs_code'),
                    item.get('origin_country'),
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                )

                self.db_manager.execute_query(query, params)

            # تحديث المخزون للأصناف المربوطة
            self.update_inventory_for_shipment(shipment_id)

        except Exception as e:
            print(f"❌ خطأ في حفظ أصناف الشحنة: {e}")
            # لا نرفع الخطأ هنا لأن الشحنة الأساسية قد تكون نجحت
            # سيتم التعامل مع هذا في المستوى الأعلى

    def update_inventory_for_shipment(self, shipment_id):
        """تحديث المخزون للشحنة"""
        try:
            # جلب حالة الشحنة لتحديد نوع التحديث
            shipment_status = self.form_vars.get('status', tk.StringVar()).get()

            # تحديث المخزون حسب حالة الشحنة
            if shipment_status in ['تم الاستلام', 'مكتملة']:
                # إضافة للمخزون عند الاستلام
                self.db_manager.update_inventory_from_shipment(shipment_id, 'add')
            elif shipment_status in ['تم الشحن', 'في الطريق']:
                # خصم من المخزون عند الشحن
                self.db_manager.update_inventory_from_shipment(shipment_id, 'remove')

        except Exception as e:
            print(f"خطأ في تحديث المخزون للشحنة: {e}")

    def load_shipment_items(self, shipment_id):
        """تحميل أصناف الشحنة"""
        try:
            query = """
                SELECT * FROM shipment_items
                WHERE shipment_id = ?
                ORDER BY created_at
            """

            items = self.db_manager.fetch_all(query, (shipment_id,))
            self.items_data = []

            for item in items:
                item_data = {
                    'id': item['id'],
                    'item_code': item['item_code'],
                    'item_name': item['item_name'],
                    'item_id': item.get('item_id'),
                    'warehouse_id': item.get('warehouse_id'),
                    'description': item['description'],
                    'quantity': str(item['quantity']),
                    'unit': item['unit'],
                    'unit_price': str(item['unit_price']),
                    'total_price': str(item['total_price']),
                    'weight': str(item['weight']),
                    'volume': str(item['volume']),
                    'hs_code': item['hs_code'],
                    'origin_country': item['origin_country']
                }
                self.items_data.append(item_data)

            # تحديث الجدول والملخص
            if hasattr(self, 'items_tree'):
                self.update_items_tree()
                self.update_items_summary()

        except Exception as e:
            print(f"خطأ في تحميل أصناف الشحنة: {e}")

    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        try:
            current_state = self.root.state()
            if current_state == 'zoomed':
                self.root.state('normal')
            else:
                self.root.state('zoomed')
        except:
            pass

    def show_help(self):
        """إظهار المساعدة"""
        help_text = """
🚢 نموذج الشحنة بملء الشاشة - المساعدة

🎯 النظام الديناميكي الجديد:
• الأقسام المطلوبة تتغير حسب حالة الشحنة
• "تحت الطلب" - يكفي القسم الأساسي فقط
• "مؤكدة" - مطلوب الأساسي + الأصناف
• "تم الشحن" - مطلوب الأساسي + الأصناف + الشحن
• الأقسام غير المطلوبة تظهر باللون الرمادي

📋 الأقسام المتاحة:
1. المعلومات الأساسية: رقم الشحنة، المورد، التاريخ، الحالة
2. الأصناف: قائمة الأصناف والمنتجات مع الكميات والأسعار
3. معلومات الشحن: الموانئ، شركة الشحن، بوليصة الشحن
4. معلومات الحاوية: رقم الحاوية، النوع، الوزن، الحجم
5. المعلومات المالية: القيمة، التكاليف، طريقة الدفع
6. المستندات: الفواتير، الشهادات، التراخيص، حالة المستندات
7. التتبع والحالة: رقم التتبع، الموقع، نسبة التقدم
8. الملاحظات والمرفقات: ملاحظات إضافية، ملفات مرفقة

⌨️ اختصارات لوحة المفاتيح:
• Ctrl+S: حفظ الشحنة
• Ctrl+W / Esc: إغلاق النموذج
• F1: هذه المساعدة
• F2: معلومات الحالة والأقسام المطلوبة
• F11: تبديل وضع ملء الشاشة
• Ctrl+→ / Page Down: القسم التالي
• Ctrl+← / Page Up: القسم السابق
• Ctrl+1-8: الانتقال المباشر للقسم
• F9: البحث في الأصناف (في قسم الأصناف) أو البحث في الموردين (في القسم الأساسي)

🎯 التنقل:
• استخدم الأزرار العلوية للانتقال المباشر
• استخدم أزرار التنقل السفلية للتنقل التسلسلي
• الحقول المطلوبة مميزة باللون الأحمر
• الحسابات التلقائية تحدث فورياً

💡 نصائح:
• يتم إنشاء رقم الشحنة تلقائياً
• نسبة التقدم تحدث حسب الحالة
• الأولوية محسوبة تلقائياً
• يمكن إرفاق ملفات متعددة
• البيانات تحفظ تلقائياً عند التبديل
        """

        messagebox.showinfo("مساعدة - نموذج الشحنة", help_text)

def show_fullscreen_shipment_form(parent, mode='add', shipment_data=None):
    """إظهار نموذج الشحنة بملء الشاشة"""
    form = FullscreenShipmentForm(parent, mode, shipment_data)
    return form

if __name__ == "__main__":
    # اختبار النموذج
    root = tk.Tk()
    root.withdraw()

    form = FullscreenShipmentForm(root, mode='add')
    root.mainloop()
