#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار واجهة البحث المحسنة
"""

import sys
import os
import tkinter as tk

# إضافة المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_search_ui():
    """اختبار واجهة البحث"""
    print("🔍 اختبار واجهة البحث المحسنة")
    
    # إنشاء نافذة رئيسية
    root = tk.Tk()
    root.title("اختبار البحث المحسن")
    root.geometry("300x200")
    
    def open_search():
        """فتح نافذة البحث"""
        try:
            from src.item_search_dialog import ItemSearchDialog
            
            # بيانات تجريبية
            test_items = [
                {
                    'id': 1,
                    'item_code': 'ELEC001',
                    'item_name': 'لابتوب ديل XPS 13',
                    'description': 'لابتوب عالي الأداء',
                    'unit': 'قطعة',
                    'unit_price': '3500.00',
                    'category': 'إلكترونيات',
                    'current_stock': '5',
                    'origin_country': 'الصين'
                },
                {
                    'id': 2,
                    'item_code': 'FOOD001',
                    'item_name': 'أرز بسمتي',
                    'description': 'أرز بسمتي فاخر',
                    'unit': 'كيس',
                    'unit_price': '25.00',
                    'category': 'مواد غذائية',
                    'current_stock': '100',
                    'origin_country': 'الهند'
                }
            ]
            
            # إنشاء نافذة البحث
            search_dialog = ItemSearchDialog(root, test_items, None)
            
        except Exception as e:
            print(f"❌ خطأ: {e}")
            import traceback
            traceback.print_exc()
    
    # واجهة الاختبار
    title_label = tk.Label(
        root,
        text="🔍 اختبار البحث المحسن",
        font=('Arial', 16, 'bold'),
        pady=20
    )
    title_label.pack()
    
    instructions = tk.Label(
        root,
        text="التحسينات المطبقة:\n" +
             "✅ حقول البحث المتقدم مخفية\n" +
             "✅ زر البحث المتقدم - F3\n" +
             "✅ مساحة أكبر للنتائج",
        font=('Arial', 10),
        justify='center',
        pady=10
    )
    instructions.pack()
    
    # زر فتح البحث
    search_btn = tk.Button(
        root,
        text="🔍 فتح البحث (F9)",
        command=open_search,
        font=('Arial', 12),
        bg='#007bff',
        fg='white',
        padx=20,
        pady=10
    )
    search_btn.pack(pady=20)
    
    # ربط F9
    root.bind('<F9>', lambda e: open_search())
    
    root.mainloop()

if __name__ == "__main__":
    test_search_ui()
