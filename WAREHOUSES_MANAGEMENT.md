# 🏪 إدارة المخازن - Warehouses Management

## نظرة عامة
تم تطوير نظام إدارة المخازن كجزء من نظام متابعة الشحنات، ويوفر إدارة شاملة للمخازن والمواقع التخزينية.

---

## ✨ الميزات الرئيسية

### 🏢 إدارة المخازن
- **إضافة مخازن جديدة**: إنشاء مخازن بكود فريد واسم ومعلومات تفصيلية
- **تعديل المخازن**: تحديث معلومات المخازن الموجودة
- **حذف المخازن**: إزالة المخازن غير المستخدمة
- **تفعيل/إلغاء تفعيل**: التحكم في حالة المخازن

### 📋 معلومات المخزن
- **كود المخزن**: رمز فريد لكل مخزن
- **اسم المخزن**: الاسم الوصفي للمخزن
- **الموقع**: العنوان التفصيلي للمخزن
- **مدير المخزن**: الشخص المسؤول عن المخزن
- **معلومات الاتصال**: رقم الهاتف والتواصل

### 🔍 البحث والتصفية
- **البحث السريع**: البحث في جميع حقول المخزن
- **البحث الفوري**: النتائج تظهر أثناء الكتابة
- **تصفية متقدمة**: فلترة حسب الحالة والموقع

### 📦 عرض المخزون
- **مخزون المخزن**: عرض جميع الأصناف في المخزن المحدد
- **الكميات المتاحة**: عرض الكميات الإجمالية والمحجوزة والمتاحة
- **تحديث فوري**: تحديث بيانات المخزون في الوقت الفعلي

---

## 🚀 كيفية الاستخدام

### الوصول إلى إدارة المخازن
1. من القائمة الرئيسية، انقر على **"🏪 إدارة المخازن"**
2. أو من قائمة **"المخزون"** > **"إدارة المخازن"**

### إضافة مخزن جديد
1. انقر على زر **"➕ إضافة مخزن جديد"**
2. املأ المعلومات المطلوبة:
   - **كود المخزن**: رمز فريد (مطلوب)
   - **اسم المخزن**: الاسم الوصفي (مطلوب)
   - **الموقع**: العنوان التفصيلي
   - **مدير المخزن**: اسم المسؤول
   - **الهاتف**: رقم التواصل
   - **الحالة**: نشط/غير نشط
3. انقر على **"💾 حفظ"**

### تعديل مخزن موجود
1. حدد المخزن من القائمة
2. انقر على زر **"✏️ تعديل"** أو انقر نقراً مزدوجاً على المخزن
3. عدّل المعلومات المطلوبة
4. انقر على **"💾 حفظ"**

### حذف مخزن
1. حدد المخزن من القائمة
2. انقر على زر **"🗑️ حذف"**
3. أكد عملية الحذف

### عرض مخزون المخزن
1. حدد المخزن من القائمة
2. انقر على زر **"📦 عرض المخزون"**
3. ستظهر نافذة جديدة تعرض جميع الأصناف في المخزن

### البحث في المخازن
1. استخدم حقل البحث في أعلى النافذة
2. اكتب أي جزء من:
   - كود المخزن
   - اسم المخزن
   - الموقع
   - اسم المدير
3. النتائج ستظهر فوراً أثناء الكتابة

---

## 🗄️ هيكل قاعدة البيانات

### جدول المخازن (warehouses)
```sql
CREATE TABLE warehouses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    warehouse_code TEXT UNIQUE NOT NULL,
    warehouse_name TEXT NOT NULL,
    location TEXT,
    manager_name TEXT,
    phone TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### جدول المخزون (inventory)
```sql
CREATE TABLE inventory (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_id INTEGER NOT NULL,
    warehouse_id INTEGER NOT NULL,
    quantity INTEGER DEFAULT 0,
    reserved_quantity INTEGER DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items (id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses (id),
    UNIQUE(item_id, warehouse_id)
);
```

---

## 📊 المخازن التجريبية

يتم إنشاء المخازن التجريبية التالية تلقائياً:

| كود المخزن | اسم المخزن | الموقع | المدير | الحالة |
|------------|------------|---------|---------|---------|
| WH001 | المخزن الرئيسي | الرياض - حي الصناعية | أحمد محمد | نشط |
| WH002 | مخزن جدة | جدة - المنطقة الصناعية الثانية | سارة أحمد | نشط |
| WH003 | مخزن الدمام | الدمام - المنطقة الصناعية الأولى | محمد علي | نشط |
| WH004 | مخزن الطائف | الطائف - المنطقة الصناعية | فاطمة حسن | نشط |
| WH005 | مخزن المدينة | المدينة المنورة - المنطقة الصناعية | عبدالله أحمد | غير نشط |

---

## 🔧 الملفات التقنية

### الملفات الرئيسية
- `src/warehouses_window.py` - نافذة إدارة المخازن
- `database/database_manager.py` - إدارة قاعدة البيانات (تم تحديثها)

### الفئات (Classes)
- `WarehousesWindow` - النافذة الرئيسية لإدارة المخازن
- `WarehouseForm` - نموذج إضافة/تعديل المخزن
- `WarehouseInventoryWindow` - نافذة عرض مخزون المخزن

---

## 🎯 التطويرات المستقبلية

### ميزات مقترحة
- **تحويلات المخزون**: نقل الأصناف بين المخازن
- **تقارير المخازن**: تقارير تفصيلية لكل مخزن
- **إنذارات المخزون**: تنبيهات عند انخفاض المخزون
- **خريطة المخازن**: عرض المخازن على الخريطة
- **باركود المخازن**: طباعة باركود لكل مخزن
- **صلاحيات المخازن**: تحديد صلاحيات الوصول لكل مخزن

### تحسينات تقنية
- **API للمخازن**: واجهة برمجية للتكامل مع أنظمة أخرى
- **تصدير البيانات**: تصدير قوائم المخازن إلى Excel
- **نسخ احتياطي**: نسخ احتياطي منفصل لبيانات المخازن
- **سجل التغييرات**: تتبع جميع التعديلات على المخازن

---

## 📞 الدعم والمساعدة

للحصول على المساعدة في استخدام نظام إدارة المخازن:

- راجع دليل المستخدم الرئيسي
- تواصل مع فريق الدعم الفني
- استخدم ميزة البحث في النظام

---

## 📝 ملاحظات مهمة

- **كود المخزن**: يجب أن يكون فريداً ولا يمكن تعديله بعد الإنشاء
- **حذف المخازن**: تأكد من عدم وجود مخزون قبل حذف المخزن
- **الحالة**: المخازن غير النشطة لا تظهر في قوائم الاختيار
- **البحث**: يدعم البحث باللغة العربية والإنجليزية

---

*تم تطوير هذه الميزة كجزء من نظام متابعة الشحنات المتكامل*
