# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('config', 'config'), ('database', 'database'), ('templates', 'templates'), ('docs', 'docs'), ('requirements.txt', '.'), ('README.md', '.'), ('دليل_المستخدم.md', '.'), ('تعليمات_التشغيل.txt', '.')],
    hiddenimports=['tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog', 'sqlite3', 'pandas', 'openpyxl', 'xlsxwriter', 'reportlab', 'PIL', 'PIL.Image', 'PIL.ImageTk', 'arabic_reshaper', 'bidi', 'bidi.algorithm', 'tkcalendar', 'matplotlib', 'matplotlib.backends.backend_tkagg'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='ShipManagementSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',
)
