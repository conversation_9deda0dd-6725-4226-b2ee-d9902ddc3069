#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظيفة الحفظ في نموذج الشحنة
Test Save Functionality in Shipment Form
"""

import tkinter as tk
import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from src.fullscreen_shipment_form import FullscreenShipmentForm

def test_save_functionality():
    """اختبار وظيفة الحفظ"""
    print("🧪 بدء اختبار وظيفة الحفظ...")
    
    # إنشاء نافذة رئيسية
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    try:
        # إنشاء نموذج الشحنة
        form = FullscreenShipmentForm(root, mode='add')
        
        # ملء بعض البيانات الأساسية
        form.form_vars['shipment_number'].set('TEST-001')
        form.form_vars['customer_name'].set('عميل تجريبي')
        form.form_vars['supplier_name'].set('مورد تجريبي')
        form.form_vars['status'].set('في الانتظار')
        form.form_vars['priority'].set('عادي')
        
        print("✅ تم إنشاء النموذج وملء البيانات الأساسية")
        
        # التحقق من وجود الدالة
        if hasattr(form, 'save_shipment'):
            print("✅ دالة save_shipment موجودة")
            # اختبار وظيفة الحفظ
            print("🔄 اختبار وظيفة الحفظ...")
            result = form.save_shipment()

            if result:
                print("✅ نجح اختبار الحفظ!")
            else:
                print("❌ فشل اختبار الحفظ!")

            return result
        else:
            print("❌ دالة save_shipment غير موجودة!")
            print(f"الدوال المتاحة: {[method for method in dir(form) if not method.startswith('_')]}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False
    finally:
        root.destroy()

if __name__ == "__main__":
    success = test_save_functionality()
    if success:
        print("\n🎉 جميع الاختبارات نجحت!")
    else:
        print("\n💥 فشل في الاختبارات!")
