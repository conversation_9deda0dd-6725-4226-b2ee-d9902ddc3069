#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.config import COLORS, FONTS
from database.database_manager import DatabaseManager

class ItemForm:
    def __init__(self, parent, db_manager, item_code=None, callback=None):
        self.parent = parent
        self.db_manager = db_manager
        self.item_code = item_code
        self.callback = callback
        self.is_edit_mode = item_code is not None
        
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()
        self.load_categories()
        
        if self.is_edit_mode:
            self.load_item_data()
    
    def setup_window(self):
        """إعداد النافذة"""
        title = "تعديل الصنف" if self.is_edit_mode else "إضافة صنف جديد"
        self.window.title(f"{title} - نظام متابعة الشحنات")
        self.window.geometry("600x700")
        self.window.configure(bg=COLORS['background'])
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_text = "✏️ تعديل الصنف" if self.is_edit_mode else "➕ إضافة صنف جديد"
        title_label = tk.Label(
            main_frame,
            text=title_text,
            font=(FONTS['arabic']['family'], 16, 'bold'),
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 20))
        
        # إطار الحقول
        fields_frame = tk.Frame(main_frame, bg=COLORS['background'])
        fields_frame.pack(fill='both', expand=True)
        
        # متغيرات الحقول
        self.item_code_var = tk.StringVar()
        self.item_name_var = tk.StringVar()
        self.category_var = tk.StringVar()
        self.unit_var = tk.StringVar()
        self.cost_price_var = tk.StringVar()
        self.selling_price_var = tk.StringVar()
        self.current_stock_var = tk.StringVar()
        self.min_stock_var = tk.StringVar()
        self.description_var = tk.StringVar()
        self.is_active_var = tk.BooleanVar(value=True)
        
        # إنشاء الحقول
        self.create_field(fields_frame, "كود الصنف:", self.item_code_var, 0, required=True)
        self.create_field(fields_frame, "اسم الصنف:", self.item_name_var, 1, required=True)
        
        # حقل الفئة (قائمة منسدلة)
        self.create_category_field(fields_frame, 2)
        
        self.create_field(fields_frame, "وحدة القياس:", self.unit_var, 3)
        self.create_field(fields_frame, "سعر التكلفة:", self.cost_price_var, 4, field_type="number")
        self.create_field(fields_frame, "سعر البيع:", self.selling_price_var, 5, field_type="number")
        self.create_field(fields_frame, "المخزون الحالي:", self.current_stock_var, 6, field_type="number")
        self.create_field(fields_frame, "الحد الأدنى للمخزون:", self.min_stock_var, 7, field_type="number")
        
        # حقل الوصف (نص متعدد الأسطر)
        self.create_description_field(fields_frame, 8)
        
        # حقل الحالة
        self.create_status_field(fields_frame, 9)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg=COLORS['background'])
        buttons_frame.pack(fill='x', pady=(20, 0))
        
        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['success'],
            fg='white',
            command=self.save_item,
            relief='flat',
            padx=20,
            pady=8
        )
        save_btn.pack(side='right', padx=(0, 10))
        
        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['secondary'],
            fg='white',
            command=self.window.destroy,
            relief='flat',
            padx=20,
            pady=8
        )
        cancel_btn.pack(side='right')
    
    def create_field(self, parent, label_text, variable, row, required=False, field_type="text"):
        """إنشاء حقل إدخال"""
        # التسمية
        label = tk.Label(
            parent,
            text=label_text + (" *" if required else ""),
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['background'],
            fg=COLORS['text_primary']
        )
        label.grid(row=row, column=1, sticky='e', padx=(0, 10), pady=10)
        
        # حقل الإدخال
        entry = tk.Entry(
            parent,
            textvariable=variable,
            font=(FONTS['arabic']['family'], 12),
            width=30,
            justify='right'
        )
        entry.grid(row=row, column=0, sticky='w', padx=(10, 0), pady=10)
        
        # تعطيل كود الصنف في وضع التعديل
        if label_text == "كود الصنف:" and self.is_edit_mode:
            entry.config(state='readonly')
        
        return entry
    
    def create_category_field(self, parent, row):
        """إنشاء حقل الفئة"""
        # التسمية
        tk.Label(
            parent,
            text="الفئة:",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['background'],
            fg=COLORS['text_primary']
        ).grid(row=row, column=1, sticky='e', padx=(0, 10), pady=10)
        
        # القائمة المنسدلة
        self.category_combo = ttk.Combobox(
            parent,
            textvariable=self.category_var,
            font=(FONTS['arabic']['family'], 12),
            width=28,
            justify='right',
            state='readonly'
        )
        self.category_combo.grid(row=row, column=0, sticky='w', padx=(10, 0), pady=10)
    
    def create_description_field(self, parent, row):
        """إنشاء حقل الوصف"""
        # التسمية
        tk.Label(
            parent,
            text="الوصف:",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['background'],
            fg=COLORS['text_primary']
        ).grid(row=row, column=1, sticky='ne', padx=(0, 10), pady=10)
        
        # حقل النص المتعدد
        self.description_text = tk.Text(
            parent,
            font=(FONTS['arabic']['family'], 12),
            width=30,
            height=3,
            wrap='word'
        )
        self.description_text.grid(row=row, column=0, sticky='w', padx=(10, 0), pady=10)
    
    def create_status_field(self, parent, row):
        """إنشاء حقل الحالة"""
        # التسمية
        tk.Label(
            parent,
            text="الحالة:",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['background'],
            fg=COLORS['text_primary']
        ).grid(row=row, column=1, sticky='e', padx=(0, 10), pady=10)
        
        # إطار الحالة
        status_frame = tk.Frame(parent, bg=COLORS['background'])
        status_frame.grid(row=row, column=0, sticky='w', padx=(10, 0), pady=10)
        
        tk.Checkbutton(
            status_frame,
            text="نشط",
            variable=self.is_active_var,
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['background'],
            fg=COLORS['text_primary']
        ).pack(side='right')
    
    def load_categories(self):
        """تحميل فئات الأصناف"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT category_name FROM item_categories ORDER BY category_name")
            categories = cursor.fetchall()
            
            category_list = ["بدون فئة"] + [cat['category_name'] for cat in categories]
            self.category_combo['values'] = category_list
            
            if not self.is_edit_mode:
                self.category_combo.set("بدون فئة")
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الفئات: {str(e)}")
    
    def load_item_data(self):
        """تحميل بيانات الصنف للتعديل"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT i.*, c.category_name
                FROM items i
                LEFT JOIN item_categories c ON i.category_id = c.id
                WHERE i.item_code = ?
            ''', (self.item_code,))
            
            item = cursor.fetchone()
            
            if item:
                self.item_code_var.set(item['item_code'])
                self.item_name_var.set(item['item_name'])
                self.category_var.set(item['category_name'] or "بدون فئة")
                self.unit_var.set(item['unit_of_measure'] or "")
                self.cost_price_var.set(str(item['cost_price'] or 0))
                self.selling_price_var.set(str(item['selling_price'] or 0))
                self.current_stock_var.set(str(item['current_stock'] or 0))
                self.min_stock_var.set(str(item['min_stock_level'] or 0))
                self.description_text.insert('1.0', item['description'] or "")
                self.is_active_var.set(bool(item['is_active']))
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل بيانات الصنف: {str(e)}")
    
    def save_item(self):
        """حفظ الصنف"""
        # التحقق من صحة البيانات
        if not self.validate_data():
            return
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # الحصول على معرف الفئة
            category_id = None
            if self.category_var.get() != "بدون فئة":
                cursor.execute("SELECT id FROM item_categories WHERE category_name = ?", 
                             (self.category_var.get(),))
                category_result = cursor.fetchone()
                if category_result:
                    category_id = category_result['id']
            
            # إعداد البيانات
            item_data = {
                'item_code': self.item_code_var.get().strip(),
                'item_name': self.item_name_var.get().strip(),
                'category_id': category_id,
                'unit_of_measure': self.unit_var.get().strip() or None,
                'cost_price': float(self.cost_price_var.get()) if self.cost_price_var.get() else 0,
                'selling_price': float(self.selling_price_var.get()) if self.selling_price_var.get() else 0,
                'current_stock': int(self.current_stock_var.get()) if self.current_stock_var.get() else 0,
                'min_stock_level': int(self.min_stock_var.get()) if self.min_stock_var.get() else 0,
                'description': self.description_text.get('1.0', 'end-1c').strip() or None,
                'is_active': self.is_active_var.get()
            }
            
            if self.is_edit_mode:
                # تحديث الصنف
                cursor.execute('''
                    UPDATE items SET
                        item_name = ?, category_id = ?, unit_of_measure = ?,
                        cost_price = ?, selling_price = ?, current_stock = ?,
                        min_stock_level = ?, description = ?, is_active = ?
                    WHERE item_code = ?
                ''', (
                    item_data['item_name'], item_data['category_id'], item_data['unit_of_measure'],
                    item_data['cost_price'], item_data['selling_price'], item_data['current_stock'],
                    item_data['min_stock_level'], item_data['description'], item_data['is_active'],
                    self.item_code
                ))
                message = "تم تحديث الصنف بنجاح"
            else:
                # إضافة صنف جديد
                cursor.execute('''
                    INSERT INTO items (
                        item_code, item_name, category_id, unit_of_measure,
                        cost_price, selling_price, current_stock, min_stock_level,
                        description, is_active
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    item_data['item_code'], item_data['item_name'], item_data['category_id'],
                    item_data['unit_of_measure'], item_data['cost_price'], item_data['selling_price'],
                    item_data['current_stock'], item_data['min_stock_level'],
                    item_data['description'], item_data['is_active']
                ))
                message = "تم إضافة الصنف بنجاح"
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("نجح", message)
            
            # استدعاء دالة التحديث إذا كانت متوفرة
            if self.callback:
                self.callback()
            
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ الصنف: {str(e)}")
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.item_code_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال كود الصنف")
            return False
        
        if not self.item_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم الصنف")
            return False
        
        # التحقق من الأرقام
        try:
            if self.cost_price_var.get():
                float(self.cost_price_var.get())
            if self.selling_price_var.get():
                float(self.selling_price_var.get())
            if self.current_stock_var.get():
                int(self.current_stock_var.get())
            if self.min_stock_var.get():
                int(self.min_stock_var.get())
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم رقمية صحيحة")
            return False
        
        return True


if __name__ == "__main__":
    # تشغيل نموذج الصنف للاختبار
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    db_manager = DatabaseManager()
    item_form = ItemForm(root, db_manager)
    item_form.window.mainloop()
