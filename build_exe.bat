@echo off
chcp 65001 >nul
echo ========================================
echo    بناء ملف تنفيذي لنظام متابعة الشحنات
echo    Building Ship Management System EXE
echo ========================================
echo.

echo جاري التحقق من المتطلبات...
echo Checking requirements...

:: التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo Error: Python is not installed or not in PATH
    pause
    exit /b 1
)

:: التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: pip غير مثبت أو غير موجود في PATH
    echo Error: pip is not installed or not in PATH
    pause
    exit /b 1
)

echo تم العثور على Python و pip بنجاح
echo Python and pip found successfully
echo.

echo جاري تثبيت المتطلبات...
echo Installing requirements...
pip install -r requirements.txt
if errorlevel 1 (
    echo تحذير: فشل في تثبيت بعض المتطلبات
    echo Warning: Failed to install some requirements
)

echo جاري تثبيت PyInstaller...
echo Installing PyInstaller...
pip install pyinstaller
if errorlevel 1 (
    echo خطأ: فشل في تثبيت PyInstaller
    echo Error: Failed to install PyInstaller
    pause
    exit /b 1
)

echo.
echo جاري إنشاء مجلد البناء...
echo Creating build directory...
if not exist "build" mkdir build
if not exist "dist" mkdir dist

echo.
echo جاري بناء الملف التنفيذي...
echo Building executable file...
echo هذا قد يستغرق عدة دقائق، يرجى الانتظار...
echo This may take several minutes, please wait...
echo.

:: بناء الملف التنفيذي باستخدام ملف spec المخصص
pyinstaller --clean ship_management_system.spec

if errorlevel 1 (
    echo.
    echo خطأ: فشل في بناء الملف التنفيذي
    echo Error: Failed to build executable
    echo.
    echo جاري المحاولة بطريقة بديلة...
    echo Trying alternative method...
    
    :: طريقة بديلة بدون ملف spec
    pyinstaller --onefile --windowed --name="ShipManagementSystem" ^
                --add-data="config;config" ^
                --add-data="database;database" ^
                --add-data="templates;templates" ^
                --add-data="docs;docs" ^
                --hidden-import="tkinter" ^
                --hidden-import="sqlite3" ^
                --hidden-import="pandas" ^
                --hidden-import="openpyxl" ^
                --hidden-import="reportlab" ^
                --hidden-import="PIL" ^
                --hidden-import="arabic_reshaper" ^
                --hidden-import="bidi" ^
                --hidden-import="tkcalendar" ^
                --hidden-import="matplotlib" ^
                main.py
    
    if errorlevel 1 (
        echo.
        echo خطأ: فشل في بناء الملف التنفيذي بالطريقة البديلة أيضاً
        echo Error: Failed to build executable with alternative method too
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo تم بناء الملف التنفيذي بنجاح!
echo Executable built successfully!
echo ========================================
echo.

if exist "dist\ShipManagementSystem.exe" (
    echo الملف التنفيذي موجود في: dist\ShipManagementSystem.exe
    echo Executable file location: dist\ShipManagementSystem.exe
) else if exist "dist\ShipManagementSystem_dist\ShipManagementSystem.exe" (
    echo الملف التنفيذي موجود في: dist\ShipManagementSystem_dist\ShipManagementSystem.exe
    echo Executable file location: dist\ShipManagementSystem_dist\ShipManagementSystem.exe
) else (
    echo تحذير: لم يتم العثور على الملف التنفيذي في المكان المتوقع
    echo Warning: Executable file not found in expected location
    echo يرجى التحقق من مجلد dist
    echo Please check the dist folder
)

echo.
echo ملاحظات مهمة:
echo Important notes:
echo - تأكد من وجود جميع ملفات قاعدة البيانات في نفس مجلد الملف التنفيذي
echo - Make sure all database files are in the same folder as the executable
echo - قم بتشغيل الملف التنفيذي كمدير إذا واجهت مشاكل في الصلاحيات
echo - Run the executable as administrator if you face permission issues
echo.

pause
