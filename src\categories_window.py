#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.config import COLORS, FONTS
from database.database_manager import DatabaseManager

class CategoriesWindow:
    def __init__(self, parent, callback=None):
        self.parent = parent
        self.db_manager = DatabaseManager()
        self.callback = callback
        
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()
        self.load_categories()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة فئات الأصناف - نظام متابعة الشحنات")
        self.window.geometry("800x600")
        self.window.configure(bg=COLORS['background'])
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(
            main_frame,
            text="🏷️ إدارة فئات الأصناف",
            font=(FONTS['arabic']['family'], 16, 'bold'),
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 20))
        
        # إطار الأزرار العلوية
        top_buttons_frame = tk.Frame(main_frame, bg=COLORS['background'])
        top_buttons_frame.pack(fill='x', pady=(0, 10))
        
        # زر إضافة فئة جديدة
        add_btn = tk.Button(
            top_buttons_frame,
            text="➕ إضافة فئة جديدة",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['success'],
            fg='white',
            command=self.add_category,
            relief='flat',
            padx=20,
            pady=8
        )
        add_btn.pack(side='right', padx=(0, 10))
        
        # إطار البحث
        search_frame = tk.Frame(main_frame, bg=COLORS['background'])
        search_frame.pack(fill='x', pady=(0, 10))
        
        # حقل البحث
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.search_categories)
        
        search_entry = tk.Entry(
            search_frame,
            textvariable=self.search_var,
            font=(FONTS['arabic']['family'], 12),
            width=30,
            justify='right'
        )
        search_entry.pack(side='left', padx=(10, 0))
        
        tk.Label(
            search_frame,
            text="🔍 البحث:",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['background'],
            fg=COLORS['text_primary']
        ).pack(side='right', padx=(0, 10))
        
        # إطار الجدول
        table_frame = tk.Frame(main_frame, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True)
        
        # إنشاء Treeview للفئات
        columns = ('category_name', 'description', 'items_count', 'is_active')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعريف العناوين
        self.tree.heading('category_name', text='اسم الفئة')
        self.tree.heading('description', text='الوصف')
        self.tree.heading('items_count', text='عدد الأصناف')
        self.tree.heading('is_active', text='الحالة')
        
        # تعريف عرض الأعمدة
        self.tree.column('category_name', width=200, anchor='center')
        self.tree.column('description', width=300, anchor='center')
        self.tree.column('items_count', width=100, anchor='center')
        self.tree.column('is_active', width=100, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول وشريط التمرير
        self.tree.pack(side='right', fill='both', expand=True)
        scrollbar.pack(side='left', fill='y')
        
        # ربط الأحداث
        self.tree.bind('<<TreeviewSelect>>', self.on_category_select)
        self.tree.bind('<Double-1>', self.edit_category)
        
        # إطار الأزرار السفلية
        bottom_buttons_frame = tk.Frame(main_frame, bg=COLORS['background'])
        bottom_buttons_frame.pack(fill='x', pady=(20, 0))
        
        # زر التعديل
        self.edit_btn = tk.Button(
            bottom_buttons_frame,
            text="✏️ تعديل",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['info'],
            fg='white',
            command=self.edit_category,
            relief='flat',
            padx=20,
            pady=8,
            state='disabled'
        )
        self.edit_btn.pack(side='right', padx=(0, 10))
        
        # زر الحذف
        self.delete_btn = tk.Button(
            bottom_buttons_frame,
            text="🗑️ حذف",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['danger'],
            fg='white',
            command=self.delete_category,
            relief='flat',
            padx=20,
            pady=8,
            state='disabled'
        )
        self.delete_btn.pack(side='right', padx=(0, 10))
        
        # زر إغلاق
        close_btn = tk.Button(
            bottom_buttons_frame,
            text="❌ إغلاق",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['secondary'],
            fg='white',
            command=self.window.destroy,
            relief='flat',
            padx=20,
            pady=8
        )
        close_btn.pack(side='right')
    
    def load_categories(self):
        """تحميل الفئات من قاعدة البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            # جلب الفئات مع عدد الأصناف في كل فئة
            cursor.execute('''
                SELECT c.category_name, c.description, c.is_active,
                       COUNT(i.id) as items_count
                FROM item_categories c
                LEFT JOIN items i ON c.id = i.category_id
                GROUP BY c.id, c.category_name, c.description, c.is_active
                ORDER BY c.category_name
            ''')
            
            categories = cursor.fetchall()
            
            for category in categories:
                status = "نشط" if category['is_active'] else "غير نشط"
                self.tree.insert('', 'end', values=(
                    category['category_name'],
                    category['description'] or '',
                    category['items_count'],
                    status
                ))
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الفئات: {str(e)}")
    
    def search_categories(self, *args):
        """البحث في الفئات"""
        search_term = self.search_var.get().strip()
        
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            if search_term:
                cursor.execute('''
                    SELECT c.category_name, c.description, c.is_active,
                           COUNT(i.id) as items_count
                    FROM item_categories c
                    LEFT JOIN items i ON c.id = i.category_id
                    WHERE c.category_name LIKE ? OR c.description LIKE ?
                    GROUP BY c.id, c.category_name, c.description, c.is_active
                    ORDER BY c.category_name
                ''', (f'%{search_term}%', f'%{search_term}%'))
            else:
                cursor.execute('''
                    SELECT c.category_name, c.description, c.is_active,
                           COUNT(i.id) as items_count
                    FROM item_categories c
                    LEFT JOIN items i ON c.id = i.category_id
                    GROUP BY c.id, c.category_name, c.description, c.is_active
                    ORDER BY c.category_name
                ''')
            
            categories = cursor.fetchall()
            
            for category in categories:
                status = "نشط" if category['is_active'] else "غير نشط"
                self.tree.insert('', 'end', values=(
                    category['category_name'],
                    category['description'] or '',
                    category['items_count'],
                    status
                ))
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في البحث: {str(e)}")
    
    def on_category_select(self, event=None):
        """عند اختيار فئة من الجدول"""
        selection = self.tree.selection()
        if selection:
            self.edit_btn.config(state='normal')
            self.delete_btn.config(state='normal')
        else:
            self.edit_btn.config(state='disabled')
            self.delete_btn.config(state='disabled')
    
    def add_category(self):
        """إضافة فئة جديدة"""
        CategoryForm(self.window, self.db_manager, None, self.load_categories)
    
    def edit_category(self, event=None):
        """تعديل فئة موجودة"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار فئة للتعديل")
            return
        
        category_name = self.tree.item(selected_item[0])['values'][0]
        CategoryForm(self.window, self.db_manager, category_name, self.load_categories)
    
    def delete_category(self):
        """حذف فئة"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار فئة للحذف")
            return
        
        category_name = self.tree.item(selected_item[0])['values'][0]
        items_count = self.tree.item(selected_item[0])['values'][2]
        
        if int(items_count) > 0:
            messagebox.showerror("خطأ", f"لا يمكن حذف الفئة '{category_name}' لأنها تحتوي على {items_count} صنف")
            return
        
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف الفئة '{category_name}'؟"):
            try:
                conn = self.db_manager.get_connection()
                cursor = conn.cursor()
                
                cursor.execute("DELETE FROM item_categories WHERE category_name = ?", (category_name,))
                conn.commit()
                conn.close()
                
                messagebox.showinfo("نجح", "تم حذف الفئة بنجاح")
                self.load_categories()
                
                # استدعاء دالة التحديث إذا كانت متوفرة
                if self.callback:
                    self.callback()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف الفئة: {str(e)}")


class CategoryForm:
    def __init__(self, parent, db_manager, category_name=None, callback=None):
        self.parent = parent
        self.db_manager = db_manager
        self.category_name = category_name
        self.callback = callback
        self.is_edit_mode = category_name is not None
        
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()
        
        if self.is_edit_mode:
            self.load_category_data()
    
    def setup_window(self):
        """إعداد النافذة"""
        title = "تعديل الفئة" if self.is_edit_mode else "إضافة فئة جديدة"
        self.window.title(f"{title} - نظام متابعة الشحنات")
        self.window.geometry("500x400")
        self.window.configure(bg=COLORS['background'])
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_text = "✏️ تعديل الفئة" if self.is_edit_mode else "➕ إضافة فئة جديدة"
        title_label = tk.Label(
            main_frame,
            text=title_text,
            font=(FONTS['arabic']['family'], 16, 'bold'),
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 20))
        
        # إطار الحقول
        fields_frame = tk.Frame(main_frame, bg=COLORS['background'])
        fields_frame.pack(fill='both', expand=True)
        
        # متغيرات الحقول
        self.category_name_var = tk.StringVar()
        self.description_var = tk.StringVar()
        self.is_active_var = tk.BooleanVar(value=True)
        
        # حقل اسم الفئة
        tk.Label(
            fields_frame,
            text="اسم الفئة: *",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['background'],
            fg=COLORS['text_primary']
        ).grid(row=0, column=1, sticky='e', padx=(0, 10), pady=10)
        
        tk.Entry(
            fields_frame,
            textvariable=self.category_name_var,
            font=(FONTS['arabic']['family'], 12),
            width=30,
            justify='right'
        ).grid(row=0, column=0, sticky='w', padx=(10, 0), pady=10)
        
        # حقل الوصف
        tk.Label(
            fields_frame,
            text="الوصف:",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['background'],
            fg=COLORS['text_primary']
        ).grid(row=1, column=1, sticky='ne', padx=(0, 10), pady=10)
        
        self.description_text = tk.Text(
            fields_frame,
            font=(FONTS['arabic']['family'], 12),
            width=30,
            height=5,
            wrap='word'
        )
        self.description_text.grid(row=1, column=0, sticky='w', padx=(10, 0), pady=10)
        
        # حقل الحالة
        tk.Label(
            fields_frame,
            text="الحالة:",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['background'],
            fg=COLORS['text_primary']
        ).grid(row=2, column=1, sticky='e', padx=(0, 10), pady=10)
        
        status_frame = tk.Frame(fields_frame, bg=COLORS['background'])
        status_frame.grid(row=2, column=0, sticky='w', padx=(10, 0), pady=10)
        
        tk.Checkbutton(
            status_frame,
            text="نشط",
            variable=self.is_active_var,
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['background'],
            fg=COLORS['text_primary']
        ).pack(side='right')
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg=COLORS['background'])
        buttons_frame.pack(fill='x', pady=(20, 0))
        
        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['success'],
            fg='white',
            command=self.save_category,
            relief='flat',
            padx=20,
            pady=8
        )
        save_btn.pack(side='right', padx=(0, 10))
        
        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['secondary'],
            fg='white',
            command=self.window.destroy,
            relief='flat',
            padx=20,
            pady=8
        )
        cancel_btn.pack(side='right')
    
    def load_category_data(self):
        """تحميل بيانات الفئة للتعديل"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM item_categories WHERE category_name = ?", (self.category_name,))
            category = cursor.fetchone()
            
            if category:
                self.category_name_var.set(category['category_name'])
                self.description_text.insert('1.0', category['description'] or "")
                self.is_active_var.set(bool(category['is_active']))
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل بيانات الفئة: {str(e)}")
    
    def save_category(self):
        """حفظ الفئة"""
        if not self.category_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم الفئة")
            return
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            category_data = {
                'category_name': self.category_name_var.get().strip(),
                'description': self.description_text.get('1.0', 'end-1c').strip() or None,
                'is_active': self.is_active_var.get()
            }
            
            if self.is_edit_mode:
                # تحديث الفئة
                cursor.execute('''
                    UPDATE item_categories SET
                        category_name = ?, description = ?, is_active = ?
                    WHERE category_name = ?
                ''', (
                    category_data['category_name'], category_data['description'],
                    category_data['is_active'], self.category_name
                ))
                message = "تم تحديث الفئة بنجاح"
            else:
                # إضافة فئة جديدة
                cursor.execute('''
                    INSERT INTO item_categories (category_name, description, is_active)
                    VALUES (?, ?, ?)
                ''', (
                    category_data['category_name'], category_data['description'],
                    category_data['is_active']
                ))
                message = "تم إضافة الفئة بنجاح"
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("نجح", message)
            
            # استدعاء دالة التحديث إذا كانت متوفرة
            if self.callback:
                self.callback()
            
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ الفئة: {str(e)}")


if __name__ == "__main__":
    # تشغيل نافذة الفئات للاختبار
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    categories_window = CategoriesWindow(root)
    categories_window.window.mainloop()
