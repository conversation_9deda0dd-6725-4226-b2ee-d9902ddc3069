#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة الشحنات المتقدمة مع دعم RTL
Advanced Shipments Management Window with RTL Support
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from datetime import datetime, timedelta, date
import json
import uuid

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS, RTL_CONFIG, STYLE_CONFIG, SHIPMENT_STATUS, RELEASE_STATUS, PORTS, SHIPPING_COMPANIES, CURRENCIES
from database.database_manager import DatabaseManager
from src.ui_styles import get_style_manager
from src.rtl_components import *
from src.auth_manager import auth_manager

class AdvancedShipmentsWindow:
    def __init__(self, parent=None):
        self.parent = parent
        self.root = tk.Toplevel(parent) if parent else tk.Tk()
        self.style_manager = get_style_manager()
        self.db_manager = DatabaseManager()
        
        # متغيرات البيانات
        self.shipments_data = []
        self.filtered_data = []
        self.selected_shipment = None
        self.current_page = 1
        self.items_per_page = 20
        
        # متغيرات البحث والتصفية
        self.search_var = tk.StringVar()
        self.status_filter_var = tk.StringVar()
        self.supplier_filter_var = tk.StringVar()
        self.release_filter_var = tk.StringVar()
        self.date_from_var = tk.StringVar()
        self.date_to_var = tk.StringVar()
        
        self.setup_window()
        self.create_advanced_interface()
        self.load_data()
        self.center_window()
        
    def setup_window(self):
        """إعداد النافذة المتقدمة"""
        self.root.title("🚢 إدارة الشحنات المتقدمة")
        self.root.geometry("1400x900")
        self.root.configure(bg=COLORS['background'])
        self.root.state('zoomed')  # ملء الشاشة
        
        # ربط أحداث لوحة المفاتيح
        self.root.bind('<F5>', lambda e: self.refresh_data())
        self.root.bind('<Control-n>', lambda e: self.add_shipment())
        self.root.bind('<Control-f>', lambda e: self.focus_search())
        self.root.bind('<Delete>', lambda e: self.delete_shipment())
        self.root.bind('<F1>', lambda e: self.show_help())
        
        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def create_advanced_interface(self):
        """إنشاء واجهة متقدمة مع تصميم RTL"""
        # الإطار الرئيسي
        main_frame = create_rtl_frame(self.root, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # شريط العنوان والأدوات
        self.create_header_toolbar(main_frame)
        
        # منطقة البحث والتصفية المتقدمة
        self.create_advanced_search_panel(main_frame)
        
        # منطقة المحتوى الرئيسية
        content_frame = create_rtl_frame(main_frame)
        content_frame.pack(fill='both', expand=True, pady=(10, 0))
        
        # الجانب الأيمن - قائمة الشحنات
        self.create_shipments_list_panel(content_frame)
        
        # الجانب الأيسر - تفاصيل الشحنة
        self.create_shipment_details_panel(content_frame)
        
        # شريط الحالة والتنقل
        self.create_status_navigation_bar(main_frame)
        
    def create_header_toolbar(self, parent):
        """إنشاء شريط العنوان والأدوات"""
        header_frame = create_rtl_frame(parent, bg=COLORS['primary'], height=80)
        header_frame.pack(fill='x', pady=(0, 10))
        header_frame.pack_propagate(False)
        
        # الجانب الأيمن - العنوان والوصف
        title_frame = create_rtl_frame(header_frame, bg=COLORS['primary'])
        title_frame.pack(side='right', padx=20, pady=15)
        
        title_label = create_rtl_label(
            title_frame,
            text="🚢 إدارة الشحنات المتقدمة",
            font=self.style_manager.get_font('arabic_title', size=20, weight='bold'),
            bg=COLORS['primary'],
            fg=COLORS['text_white']
        )
        title_label.pack(anchor='e')
        
        subtitle_label = create_rtl_label(
            title_frame,
            text="نظام شامل لإدارة ومتابعة جميع الشحنات مع تتبع متقدم",
            font=self.style_manager.get_font('arabic', size=11),
            bg=COLORS['primary'],
            fg=COLORS['text_light']
        )
        subtitle_label.pack(anchor='e', pady=(5, 0))
        
        # الجانب الأيسر - أزرار الأدوات
        toolbar_frame = create_rtl_frame(header_frame, bg=COLORS['primary'])
        toolbar_frame.pack(side='left', padx=20, pady=15)
        
        # أزرار الأدوات الرئيسية
        toolbar_buttons = [
            ("➕ شحنة جديدة", self.add_shipment, "success", "Ctrl+N"),
            ("✏️ تعديل", self.edit_shipment, "warning", "F2"),
            ("🗑️ حذف", self.delete_shipment, "danger", "Del"),
            ("📊 تقرير", self.generate_report, "info", "Ctrl+R"),
            ("🔄 تحديث", self.refresh_data, "secondary", "F5"),
            ("❓ مساعدة", self.show_help, "ghost", "F1")
        ]
        
        for i, (text, command, style, shortcut) in enumerate(toolbar_buttons):
            btn = create_rtl_button(
                toolbar_frame,
                text=text,
                command=command,
                style=style,
                font=self.style_manager.get_font('arabic_button', size=10),
                width=12
            )
            btn.grid(row=0, column=i, padx=3, pady=0)
            
            # إضافة تلميح مع الاختصار
            self.create_tooltip(btn, f"{text}\nاختصار: {shortcut}")
        
    def create_advanced_search_panel(self, parent):
        """إنشاء لوحة البحث والتصفية المتقدمة"""
        search_card = create_rtl_card(parent, title="🔍 البحث والتصفية المتقدمة")
        search_card.pack(fill='x', pady=(0, 10))
        
        search_content = create_rtl_frame(search_card, bg=COLORS['surface'])
        search_content.pack(fill='x', padx=20, pady=15)
        
        # الصف الأول - البحث العام والحالة
        row1_frame = create_rtl_frame(search_content, bg=COLORS['surface'])
        row1_frame.pack(fill='x', pady=(0, 10))
        
        # البحث العام
        search_frame = create_rtl_frame(row1_frame, bg=COLORS['surface'])
        search_frame.pack(side='right', fill='x', expand=True, padx=(0, 10))
        
        search_label = create_rtl_label(
            search_frame,
            text="🔍 البحث العام:",
            font=self.style_manager.get_font('arabic', size=11, weight='bold')
        )
        search_label.pack(anchor='e', pady=(0, 5))
        
        self.search_entry = create_rtl_entry(
            search_frame,
            textvariable=self.search_var,
            font=self.style_manager.get_font('arabic', size=11)
        )
        self.search_entry.pack(fill='x', ipady=5)
        self.search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # تصفية الحالة
        status_frame = create_rtl_frame(row1_frame, bg=COLORS['surface'])
        status_frame.pack(side='right', padx=(0, 10))
        
        status_label = create_rtl_label(
            status_frame,
            text="📊 حالة الشحنة:",
            font=self.style_manager.get_font('arabic', size=11, weight='bold')
        )
        status_label.pack(anchor='e', pady=(0, 5))
        
        self.status_combo = create_rtl_combobox(
            status_frame,
            textvariable=self.status_filter_var,
            values=["جميع الحالات"] + list(SHIPMENT_STATUS.values()),
            width=15
        )
        self.status_combo.pack(ipady=5)
        self.status_combo.bind('<<ComboboxSelected>>', self.on_filter_change)
        
        # تصفية المورد
        supplier_frame = create_rtl_frame(row1_frame, bg=COLORS['surface'])
        supplier_frame.pack(side='right')
        
        supplier_label = create_rtl_label(
            supplier_frame,
            text="🏢 المورد:",
            font=self.style_manager.get_font('arabic', size=11, weight='bold')
        )
        supplier_label.pack(anchor='e', pady=(0, 5))
        
        self.supplier_combo = create_rtl_combobox(
            supplier_frame,
            textvariable=self.supplier_filter_var,
            values=["جميع الموردين"],
            width=15
        )
        self.supplier_combo.pack(ipady=5)
        self.supplier_combo.bind('<<ComboboxSelected>>', self.on_filter_change)

        # تصفية حالة الإفراج
        release_frame = create_rtl_frame(row1_frame, bg=COLORS['surface'])
        release_frame.pack(side='right', padx=(0, 10))

        release_label = create_rtl_label(
            release_frame,
            text="🔓 حالة الإفراج:",
            font=self.style_manager.get_font('arabic', size=11, weight='bold')
        )
        release_label.pack(anchor='e', pady=(0, 5))

        self.release_combo = create_rtl_combobox(
            release_frame,
            textvariable=self.release_filter_var,
            values=["جميع الحالات"] + list(RELEASE_STATUS.values()),
            width=15
        )
        self.release_combo.pack(ipady=5)
        self.release_combo.bind('<<ComboboxSelected>>', self.on_filter_change)

        # الصف الثاني - تصفية التاريخ والأزرار
        row2_frame = create_rtl_frame(search_content, bg=COLORS['surface'])
        row2_frame.pack(fill='x', pady=(10, 0))
        
        # تاريخ من
        date_from_frame = create_rtl_frame(row2_frame, bg=COLORS['surface'])
        date_from_frame.pack(side='right', padx=(0, 10))
        
        date_from_label = create_rtl_label(
            date_from_frame,
            text="📅 من تاريخ:",
            font=self.style_manager.get_font('arabic', size=11, weight='bold')
        )
        date_from_label.pack(anchor='e', pady=(0, 5))
        
        self.date_from_entry = create_rtl_entry(
            date_from_frame,
            textvariable=self.date_from_var,
            width=12
        )
        self.date_from_entry.pack(ipady=5)
        
        # تاريخ إلى
        date_to_frame = create_rtl_frame(row2_frame, bg=COLORS['surface'])
        date_to_frame.pack(side='right', padx=(0, 10))
        
        date_to_label = create_rtl_label(
            date_to_frame,
            text="📅 إلى تاريخ:",
            font=self.style_manager.get_font('arabic', size=11, weight='bold')
        )
        date_to_label.pack(anchor='e', pady=(0, 5))
        
        self.date_to_entry = create_rtl_entry(
            date_to_frame,
            textvariable=self.date_to_var,
            width=12
        )
        self.date_to_entry.pack(ipady=5)
        
        # أزرار البحث
        search_buttons_frame = create_rtl_frame(row2_frame, bg=COLORS['surface'])
        search_buttons_frame.pack(side='left', padx=(10, 0))
        
        search_btn = create_rtl_button(
            search_buttons_frame,
            text="🔍 بحث",
            command=self.apply_filters,
            style='primary',
            width=10
        )
        search_btn.pack(side='top', pady=(20, 5))
        
        clear_btn = create_rtl_button(
            search_buttons_frame,
            text="🗑️ مسح",
            command=self.clear_filters,
            style='outline',
            width=10
        )
        clear_btn.pack(side='top')
        
    def create_shipments_list_panel(self, parent):
        """إنشاء لوحة قائمة الشحنات"""
        # الجانب الأيمن - قائمة الشحنات
        list_frame = create_rtl_frame(parent)
        list_frame.pack(side='right', fill='both', expand=True, padx=(0, 10))
        
        # عنوان القائمة
        list_header = create_rtl_frame(list_frame, bg=COLORS['primary'], height=40)
        list_header.pack(fill='x', pady=(0, 5))
        list_header.pack_propagate(False)
        
        list_title = create_rtl_label(
            list_header,
            text="📋 قائمة الشحنات",
            font=self.style_manager.get_font('arabic_header', size=14, weight='bold'),
            bg=COLORS['primary'],
            fg=COLORS['text_white']
        )
        list_title.pack(pady=8)
        
        # إطار الجدول
        table_frame = create_rtl_frame(list_frame, bg=COLORS['surface'], relief='raised', bd=1)
        table_frame.pack(fill='both', expand=True)
        
        # إنشاء الجدول المتقدم
        self.create_advanced_table(table_frame)
        
    def create_advanced_table(self, parent):
        """إنشاء جدول متقدم للشحنات"""
        # إطار الجدول مع شريط التمرير
        table_container = create_rtl_frame(parent, bg=COLORS['surface'])
        table_container.pack(fill='both', expand=True, padx=10, pady=10)
        
        # تعريف الأعمدة مع الحقول الجديدة
        columns = [
            ('shipment_number', 'رقم الشحنة', 120),
            ('shipping_company', 'شركة الشحن', 130),
            ('bill_of_lading', 'بوليصة الشحن', 120),
            ('tracking_number', 'رقم التتبع', 110),
            ('container_count', 'عدد الحاويات', 80),
            ('container_number', 'رقم الحاوية', 120),
            ('supplier_name', 'المورد', 150),
            ('status', 'الحالة', 100),
            ('release_status', 'حالة الإفراج', 100),
            ('shipment_date', 'تاريخ الشحن', 100),
            ('arrival_date', 'تاريخ الوصول', 100),
            ('departure_port', 'ميناء المغادرة', 120),
            ('arrival_port', 'ميناء الوصول', 120),
            ('total_value', 'القيمة الإجمالية', 120)
        ]
        
        # إنشاء Treeview مع تصميم RTL
        self.shipments_tree = create_rtl_treeview(
            table_container,
            columns=[col[0] for col in columns],
            show='headings',
            height=15
        )
        
        # إصلاح مشكلة رؤية عناوين الأعمدة
        style = ttk.Style()

        # إزالة أي أنماط متضاربة أولاً
        try:
            style.theme_use('clam')  # استخدام ثيم أساسي
        except:
            pass

        # نمط قوي ومرئي للعناوين - إصلاح مشكلة الاختفاء
        style.configure('SuperVisible.Treeview.Heading',
                       background='#0F172A',  # أسود مزرق قوي جداً
                       foreground='#FFFFFF',  # أبيض نقي 100%
                       font=('Arial', 14, 'bold'),  # خط أكبر وأوضح
                       relief='solid',  # إطار صلب واضح
                       borderwidth=3,  # حدود سميكة جداً
                       anchor='center',  # محاذاة وسط
                       padding=(12, 10),  # مساحة داخلية كبيرة
                       focuscolor='none')  # إزالة لون التركيز المتضارب

        # نمط الحالة العادية والنشطة - ضمان الرؤية دائماً
        style.map('SuperVisible.Treeview.Heading',
                 background=[
                     ('active', '#1E293B'),  # لون أغمق عند التمرير
                     ('pressed', '#334155'),  # لون عند الضغط
                     ('focus', '#0F172A'),    # لون عند التركيز
                     ('!active', '#0F172A')   # اللون الافتراضي
                 ],
                 foreground=[
                     ('active', '#FFFFFF'),   # أبيض دائماً
                     ('pressed', '#FFFFFF'),  # أبيض دائماً
                     ('focus', '#FFFFFF'),    # أبيض دائماً
                     ('!active', '#FFFFFF')   # أبيض دائماً
                 ],
                 relief=[
                     ('active', 'solid'),
                     ('pressed', 'solid'),
                     ('!active', 'solid')
                 ])

        # تحسين نمط الصفوف
        style.configure('SuperVisible.Treeview',
                       rowheight=35,  # ارتفاع أكبر للصفوف
                       font=('Arial', 12, 'normal'),
                       fieldbackground='#FFFFFF',
                       background='#FFFFFF')

        # تطبيق النمط المحسن
        self.shipments_tree.configure(style='SuperVisible.Treeview')

        # إجبار تحديث العرض لضمان ظهور العناوين
        self.shipments_tree.update_idletasks()

        # تكوين الأعمدة مع التحسينات
        for col_id, col_name, col_width in columns:
            self.shipments_tree.heading(col_id, text=col_name, anchor='center')  # محاذاة وسط للعناوين
            self.shipments_tree.column(col_id, width=col_width, anchor='e')  # محاذاة يمين للبيانات
        
        # شريط التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_container, orient='vertical', command=self.shipments_tree.yview)
        self.shipments_tree.configure(yscrollcommand=v_scrollbar.set)
        
        # شريط التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_container, orient='horizontal', command=self.shipments_tree.xview)
        self.shipments_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول وأشرطة التمرير
        self.shipments_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # تكوين الشبكة
        table_container.grid_rowconfigure(0, weight=1)
        table_container.grid_columnconfigure(0, weight=1)
        
        # ربط الأحداث
        self.shipments_tree.bind('<<TreeviewSelect>>', self.on_shipment_select)
        self.shipments_tree.bind('<Double-1>', self.on_shipment_double_click)
        self.shipments_tree.bind('<Button-3>', self.show_context_menu)
        
        # تلوين الصفوف حسب الحالة
        self.setup_row_colors()
        
    def setup_row_colors(self):
        """إعداد ألوان الصفوف حسب حالة الشحنة"""
        # تعريف الألوان لكل حالة
        status_colors = {
            'في الانتظار': '#FEF3C7',      # أصفر فاتح
            'مؤكدة': '#DBEAFE',           # أزرق فاتح
            'تم الشحن': '#E0E7FF',        # بنفسجي فاتح
            'في الطريق': '#FDE68A',       # أصفر
            'وصلت': '#D1FAE5',           # أخضر فاتح
            'في الجمارك': '#FED7AA',      # برتقالي فاتح
            'تم التسليم': '#BBF7D0',      # أخضر
            'ملغية': '#FECACA',           # أحمر فاتح
            'متأخرة': '#F87171'           # أحمر
        }
        
        # تطبيق الألوان
        for status, color in status_colors.items():
            self.shipments_tree.tag_configure(f'status_{status}', background=color)
    
    def create_shipment_details_panel(self, parent):
        """إنشاء لوحة تفاصيل الشحنة"""
        # الجانب الأيسر - تفاصيل الشحنة
        details_frame = create_rtl_frame(parent, width=400)
        details_frame.pack(side='left', fill='y', padx=(10, 0))
        details_frame.pack_propagate(False)
        
        # عنوان التفاصيل
        details_header = create_rtl_frame(details_frame, bg=COLORS['secondary'], height=40)
        details_header.pack(fill='x', pady=(0, 5))
        details_header.pack_propagate(False)
        
        details_title = create_rtl_label(
            details_header,
            text="📋 تفاصيل الشحنة",
            font=self.style_manager.get_font('arabic_header', size=14, weight='bold'),
            bg=COLORS['secondary'],
            fg=COLORS['text_white']
        )
        details_title.pack(pady=8)
        
        # محتوى التفاصيل
        self.details_content = create_rtl_frame(details_frame, bg=COLORS['surface'], relief='raised', bd=1)
        self.details_content.pack(fill='both', expand=True, padx=5, pady=5)
        
        # رسالة افتراضية
        self.create_default_details_message()
        
    def create_default_details_message(self):
        """إنشاء رسالة افتراضية لتفاصيل الشحنة"""
        default_frame = create_rtl_frame(self.details_content, bg=COLORS['surface'])
        default_frame.pack(fill='both', expand=True)
        
        icon_label = create_rtl_label(
            default_frame,
            text="📦",
            font=self.style_manager.get_font('arabic_title', size=48),
            bg=COLORS['surface'],
            fg=COLORS['text_muted']
        )
        icon_label.pack(expand=True, pady=(50, 10))
        
        message_label = create_rtl_label(
            default_frame,
            text="اختر شحنة من القائمة\nلعرض التفاصيل",
            font=self.style_manager.get_font('arabic', size=14),
            bg=COLORS['surface'],
            fg=COLORS['text_muted'],
            justify='center'
        )
        message_label.pack(expand=True)
        
    def create_status_navigation_bar(self, parent):
        """إنشاء شريط الحالة والتنقل"""
        status_frame = create_rtl_frame(parent, bg=COLORS['dark'], height=35)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)
        
        # الجانب الأيمن - معلومات الحالة
        status_info_frame = create_rtl_frame(status_frame, bg=COLORS['dark'])
        status_info_frame.pack(side='right', padx=15, pady=5)
        
        self.status_label = create_rtl_label(
            status_info_frame,
            text="جاهز",
            font=self.style_manager.get_font('arabic_small', size=10),
            bg=COLORS['dark'],
            fg=COLORS['text_white']
        )
        self.status_label.pack(side='right', padx=5)
        
        self.count_label = create_rtl_label(
            status_info_frame,
            text="إجمالي الشحنات: 0",
            font=self.style_manager.get_font('arabic_small', size=10),
            bg=COLORS['dark'],
            fg=COLORS['text_light']
        )
        self.count_label.pack(side='right', padx=10)
        
        # الجانب الأيسر - أزرار التنقل
        nav_frame = create_rtl_frame(status_frame, bg=COLORS['dark'])
        nav_frame.pack(side='left', padx=15, pady=5)
        
        self.prev_btn = create_rtl_button(
            nav_frame,
            text="◀ السابق",
            command=self.prev_page,
            style='ghost',
            font=self.style_manager.get_font('arabic_small', size=9),
            width=8
        )
        self.prev_btn.pack(side='left', padx=2)
        
        self.page_label = create_rtl_label(
            nav_frame,
            text="صفحة 1 من 1",
            font=self.style_manager.get_font('arabic_small', size=10),
            bg=COLORS['dark'],
            fg=COLORS['text_white']
        )
        self.page_label.pack(side='left', padx=10)
        
        self.next_btn = create_rtl_button(
            nav_frame,
            text="التالي ▶",
            command=self.next_page,
            style='ghost',
            font=self.style_manager.get_font('arabic_small', size=9),
            width=8
        )
        self.next_btn.pack(side='left', padx=2)

    def create_tooltip(self, widget, text):
        """إنشاء تلميح للعنصر"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")

            label = create_rtl_label(
                tooltip,
                text=text,
                bg=COLORS['dark'],
                fg=COLORS['text_white'],
                font=self.style_manager.get_font('arabic_small'),
                relief='solid',
                bd=1
            )
            label.pack(padx=5, pady=3)

            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)

    def load_data(self):
        """تحميل بيانات الشحنات"""
        try:
            # تحميل الشحنات من قاعدة البيانات
            query = """
                SELECT s.*, sup.supplier_name
                FROM shipments s
                LEFT JOIN suppliers sup ON s.supplier_id = sup.id
                ORDER BY s.created_at DESC
            """

            self.shipments_data = self.db_manager.fetch_all(query)
            self.filtered_data = self.shipments_data.copy()

            # تحميل قائمة الموردين للتصفية
            self.load_suppliers_for_filter()

            # تحديث الجدول
            self.update_table()

            # تحديث شريط الحالة
            self.update_status_bar()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def load_suppliers_for_filter(self):
        """تحميل قائمة الموردين للتصفية"""
        try:
            suppliers = self.db_manager.fetch_all("SELECT id, supplier_name FROM suppliers WHERE is_active = 1")
            supplier_names = ["جميع الموردين"] + [supplier['supplier_name'] for supplier in suppliers]
            self.supplier_combo['values'] = supplier_names
            self.supplier_combo.set("جميع الموردين")
        except Exception as e:
            print(f"خطأ في تحميل الموردين: {e}")

    def update_table(self):
        """تحديث جدول الشحنات"""
        # مسح البيانات الحالية
        for item in self.shipments_tree.get_children():
            self.shipments_tree.delete(item)

        # حساب البيانات للصفحة الحالية
        start_index = (self.current_page - 1) * self.items_per_page
        end_index = start_index + self.items_per_page
        page_data = self.filtered_data[start_index:end_index]

        # إضافة البيانات للجدول
        for shipment in page_data:
            # تحديد لون الصف حسب الحالة
            status = shipment.get('status', '')
            tag = f'status_{status}' if status in SHIPMENT_STATUS else ''

            # معالجة أرقام الحاويات المتعددة
            container_numbers = self.get_container_numbers_display(shipment)

            # تنسيق البيانات مع الحقول الجديدة
            values = [
                shipment.get('shipment_number', ''),
                SHIPPING_COMPANIES.get(shipment.get('shipping_company', ''), shipment.get('shipping_company', '')),  # شركة الشحن
                shipment.get('bill_of_lading', ''),  # بوليصة الشحن
                shipment.get('tracking_number', ''),  # رقم التتبع
                str(shipment.get('container_count', 1)),  # عدد الحاويات
                container_numbers,  # رقم الحاوية
                shipment.get('supplier_name', ''),
                SHIPMENT_STATUS.get(shipment.get('status', ''), shipment.get('status', '')),
                RELEASE_STATUS.get(shipment.get('release_status', ''), shipment.get('release_status', '')),
                self.format_date(shipment.get('shipment_date')),
                self.format_date(shipment.get('expected_arrival_date')),
                PORTS.get(shipment.get('departure_port', ''), shipment.get('departure_port', '')),
                PORTS.get(shipment.get('arrival_port', ''), shipment.get('arrival_port', '')),
                self.format_currency(shipment.get('total_value', 0))
            ]

            item = self.shipments_tree.insert('', 'end', values=values, tags=(tag,))
            # حفظ معرف الشحنة مع العنصر
            self.shipments_tree.set(item, '#0', shipment.get('id', ''))

    def get_container_numbers_display(self, shipment):
        """الحصول على عرض أرقام الحاويات للجدول"""
        try:
            container_count = shipment.get('container_count', 1)
            container_numbers_json = shipment.get('container_numbers_json', '')

            # إذا كان هناك أكثر من حاوية واحدة
            if container_count and container_count > 1:
                if container_numbers_json:
                    try:
                        import json
                        container_numbers = json.loads(container_numbers_json)
                        if len(container_numbers) > 1:
                            # عرض الرقم الأول + عدد الباقي
                            return f"{container_numbers[0]} +{len(container_numbers)-1}"
                        elif len(container_numbers) == 1:
                            return container_numbers[0]
                    except:
                        pass

                # في حالة عدم وجود JSON، استخدم الرقم الواحد مع إشارة للمتعدد
                single_container = shipment.get('container_number', '')
                if single_container:
                    return f"{single_container} +{container_count-1}"
                else:
                    return f"متعدد ({container_count})"
            else:
                # حاوية واحدة فقط
                return shipment.get('container_number', '')

        except Exception as e:
            print(f"خطأ في عرض أرقام الحاويات: {e}")
            return shipment.get('container_number', '')

    def format_date(self, date_value):
        """تنسيق التاريخ"""
        if not date_value:
            return ""

        if isinstance(date_value, str):
            try:
                date_obj = datetime.strptime(date_value, '%Y-%m-%d')
                return date_obj.strftime('%Y/%m/%d')
            except:
                return date_value
        elif isinstance(date_value, (date, datetime)):
            return date_value.strftime('%Y/%m/%d')

        return str(date_value)

    def format_currency(self, amount):
        """تنسيق العملة"""
        try:
            return f"{float(amount):,.2f}"
        except:
            return "0.00"

    def on_search_change(self, event=None):
        """عند تغيير نص البحث"""
        # تطبيق البحث تلقائياً بعد توقف قصير
        if hasattr(self, 'search_timer'):
            self.root.after_cancel(self.search_timer)

        self.search_timer = self.root.after(500, self.apply_filters)

    def on_filter_change(self, event=None):
        """عند تغيير التصفية"""
        self.apply_filters()

    def apply_filters(self):
        """تطبيق المرشحات والبحث"""
        try:
            search_text = self.search_var.get().strip().lower()
            status_filter = self.status_filter_var.get()
            supplier_filter = self.supplier_filter_var.get()
            release_filter = self.release_filter_var.get()
            date_from = self.date_from_var.get().strip()
            date_to = self.date_to_var.get().strip()

            # تصفية البيانات
            self.filtered_data = []

            for shipment in self.shipments_data:
                # فلتر البحث النصي
                if search_text:
                    searchable_text = f"{shipment.get('shipment_number', '')} {shipment.get('supplier_name', '')} {shipment.get('container_number', '')}".lower()
                    if search_text not in searchable_text:
                        continue

                # فلتر الحالة
                if status_filter and status_filter != "جميع الحالات":
                    if shipment.get('status', '') != status_filter:
                        continue

                # فلتر المورد
                if supplier_filter and supplier_filter != "جميع الموردين":
                    if shipment.get('supplier_name', '') != supplier_filter:
                        continue

                # فلتر حالة الإفراج
                if release_filter and release_filter != "جميع الحالات":
                    if shipment.get('release_status', '') != release_filter:
                        continue

                # فلتر التاريخ
                if date_from or date_to:
                    shipment_date = shipment.get('shipment_date')
                    if shipment_date:
                        try:
                            if isinstance(shipment_date, str):
                                ship_date = datetime.strptime(shipment_date, '%Y-%m-%d').date()
                            else:
                                ship_date = shipment_date

                            if date_from:
                                from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
                                if ship_date < from_date:
                                    continue

                            if date_to:
                                to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
                                if ship_date > to_date:
                                    continue
                        except:
                            continue

                self.filtered_data.append(shipment)

            # إعادة تعيين الصفحة الحالية
            self.current_page = 1

            # تحديث الجدول وشريط الحالة
            self.update_table()
            self.update_status_bar()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تطبيق المرشحات: {str(e)}")

    def clear_filters(self):
        """مسح جميع المرشحات"""
        self.search_var.set("")
        self.status_filter_var.set("جميع الحالات")
        self.supplier_filter_var.set("جميع الموردين")
        self.release_filter_var.set("جميع الحالات")
        self.date_from_var.set("")
        self.date_to_var.set("")

        # إعادة تحميل جميع البيانات
        self.filtered_data = self.shipments_data.copy()
        self.current_page = 1
        self.update_table()
        self.update_status_bar()

    def update_status_bar(self):
        """تحديث شريط الحالة"""
        total_items = len(self.filtered_data)
        total_pages = max(1, (total_items + self.items_per_page - 1) // self.items_per_page)

        # تحديث عداد العناصر
        self.count_label.config(text=f"إجمالي الشحنات: {total_items}")

        # تحديث معلومات الصفحة
        self.page_label.config(text=f"صفحة {self.current_page} من {total_pages}")

        # تحديث حالة أزرار التنقل
        self.prev_btn.config(state='normal' if self.current_page > 1 else 'disabled')
        self.next_btn.config(state='normal' if self.current_page < total_pages else 'disabled')

        # تحديث حالة النظام
        if total_items == 0:
            self.status_label.config(text="لا توجد شحنات")
        else:
            start_item = (self.current_page - 1) * self.items_per_page + 1
            end_item = min(self.current_page * self.items_per_page, total_items)
            self.status_label.config(text=f"عرض {start_item}-{end_item} من {total_items}")

    def prev_page(self):
        """الصفحة السابقة"""
        if self.current_page > 1:
            self.current_page -= 1
            self.update_table()
            self.update_status_bar()

    def next_page(self):
        """الصفحة التالية"""
        total_pages = max(1, (len(self.filtered_data) + self.items_per_page - 1) // self.items_per_page)
        if self.current_page < total_pages:
            self.current_page += 1
            self.update_table()
            self.update_status_bar()

    def on_shipment_select(self, event):
        """عند اختيار شحنة من الجدول"""
        selection = self.shipments_tree.selection()
        if selection:
            item = selection[0]
            shipment_id = self.shipments_tree.set(item, '#0')

            # البحث عن الشحنة في البيانات
            for shipment in self.shipments_data:
                if str(shipment.get('id', '')) == str(shipment_id):
                    self.selected_shipment = shipment
                    self.show_shipment_details(shipment)
                    break

    def on_shipment_double_click(self, event):
        """عند النقر المزدوج على شحنة"""
        if self.selected_shipment:
            self.edit_shipment()

    def show_context_menu(self, event):
        """إظهار قائمة السياق"""
        # إنشاء قائمة السياق
        context_menu = tk.Menu(self.root, tearoff=0)
        context_menu.add_command(label="✏️ تعديل", command=self.edit_shipment)
        context_menu.add_command(label="👁️ عرض التفاصيل", command=self.view_details)
        context_menu.add_separator()
        context_menu.add_command(label="📊 تتبع الشحنة", command=self.track_shipment)
        context_menu.add_command(label="📋 نسخ رقم الشحنة", command=self.copy_shipment_number)
        context_menu.add_separator()
        context_menu.add_command(label="🗑️ حذف", command=self.delete_shipment)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def show_shipment_details(self, shipment):
        """عرض تفاصيل الشحنة"""
        # مسح المحتوى السابق
        for widget in self.details_content.winfo_children():
            widget.destroy()

        # إنشاء محتوى التفاصيل
        self.create_detailed_shipment_view(shipment)

    def create_detailed_shipment_view(self, shipment):
        """إنشاء عرض مفصل للشحنة"""
        # إطار قابل للتمرير
        canvas = tk.Canvas(self.details_content, bg=COLORS['surface'])
        scrollbar = ttk.Scrollbar(self.details_content, orient="vertical", command=canvas.yview)
        scrollable_frame = create_rtl_frame(canvas, bg=COLORS['surface'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # معلومات أساسية
        self.create_basic_info_section(scrollable_frame, shipment)

        # معلومات الشحن
        self.create_shipping_info_section(scrollable_frame, shipment)

        # معلومات مالية
        self.create_financial_info_section(scrollable_frame, shipment)

        # حالة الشحنة
        self.create_status_section(scrollable_frame, shipment)

        # أزرار الإجراءات
        self.create_action_buttons_section(scrollable_frame, shipment)

        # تخطيط الكانفاس
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # ربط عجلة الماوس
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

    def create_basic_info_section(self, parent, shipment):
        """إنشاء قسم المعلومات الأساسية"""
        section_frame = create_rtl_card(parent, title="📋 المعلومات الأساسية")
        section_frame.pack(fill='x', padx=10, pady=5)

        content_frame = create_rtl_frame(section_frame, bg=COLORS['surface'])
        content_frame.pack(fill='x', padx=15, pady=10)

        # معلومات أساسية
        info_items = [
            ("رقم الشحنة:", shipment.get('shipment_number', '')),
            ("المورد:", shipment.get('supplier_name', '')),
            ("تاريخ الإنشاء:", self.format_date(shipment.get('created_at'))),
            ("المستخدم:", shipment.get('created_by', ''))
        ]

        for i, (label, value) in enumerate(info_items):
            row_frame = create_rtl_frame(content_frame, bg=COLORS['surface'])
            row_frame.pack(fill='x', pady=2)

            label_widget = create_rtl_label(
                row_frame,
                text=label,
                font=self.style_manager.get_font('arabic', size=11, weight='bold'),
                bg=COLORS['surface'],
                fg=COLORS['text_secondary']
            )
            label_widget.pack(side='right', padx=(0, 10))

            value_widget = create_rtl_label(
                row_frame,
                text=str(value),
                font=self.style_manager.get_font('arabic', size=11),
                bg=COLORS['surface'],
                fg=COLORS['text_primary']
            )
            value_widget.pack(side='right')

    def create_shipping_info_section(self, parent, shipment):
        """إنشاء قسم معلومات الشحن"""
        section_frame = create_rtl_card(parent, title="🚢 معلومات الشحن")
        section_frame.pack(fill='x', padx=10, pady=5)

        content_frame = create_rtl_frame(section_frame, bg=COLORS['surface'])
        content_frame.pack(fill='x', padx=15, pady=10)

        # معلومات الشحن
        shipping_items = [
            ("تاريخ الشحن:", self.format_date(shipment.get('shipment_date'))),
            ("تاريخ الوصول المتوقع:", self.format_date(shipment.get('expected_arrival_date'))),
            ("ميناء المغادرة:", PORTS.get(shipment.get('departure_port', ''), shipment.get('departure_port', ''))),
            ("ميناء الوصول:", PORTS.get(shipment.get('arrival_port', ''), shipment.get('arrival_port', ''))),
            ("شركة الشحن:", SHIPPING_COMPANIES.get(shipment.get('shipping_company', ''), shipment.get('shipping_company', ''))),
            ("رقم الحاوية:", shipment.get('container_number', '')),
            ("رقم بوليصة الشحن:", shipment.get('bill_of_lading', ''))
        ]

        for label, value in shipping_items:
            row_frame = create_rtl_frame(content_frame, bg=COLORS['surface'])
            row_frame.pack(fill='x', pady=2)

            label_widget = create_rtl_label(
                row_frame,
                text=label,
                font=self.style_manager.get_font('arabic', size=11, weight='bold'),
                bg=COLORS['surface'],
                fg=COLORS['text_secondary']
            )
            label_widget.pack(side='right', padx=(0, 10))

            value_widget = create_rtl_label(
                row_frame,
                text=str(value),
                font=self.style_manager.get_font('arabic', size=11),
                bg=COLORS['surface'],
                fg=COLORS['text_primary']
            )
            value_widget.pack(side='right')

    def create_financial_info_section(self, parent, shipment):
        """إنشاء قسم المعلومات المالية"""
        section_frame = create_rtl_card(parent, title="💰 المعلومات المالية")
        section_frame.pack(fill='x', padx=10, pady=5)

        content_frame = create_rtl_frame(section_frame, bg=COLORS['surface'])
        content_frame.pack(fill='x', padx=15, pady=10)

        # معلومات مالية
        financial_items = [
            ("القيمة الإجمالية:", f"{self.format_currency(shipment.get('total_value', 0))} {shipment.get('currency', 'USD')}"),
            ("تكلفة الشحن:", f"{self.format_currency(shipment.get('shipping_cost', 0))} {shipment.get('currency', 'USD')}"),
            ("رسوم إضافية:", f"{self.format_currency(shipment.get('additional_fees', 0))} {shipment.get('currency', 'USD')}"),
            ("العملة:", CURRENCIES.get(shipment.get('currency', ''), shipment.get('currency', '')))
        ]

        for label, value in financial_items:
            row_frame = create_rtl_frame(content_frame, bg=COLORS['surface'])
            row_frame.pack(fill='x', pady=2)

            label_widget = create_rtl_label(
                row_frame,
                text=label,
                font=self.style_manager.get_font('arabic', size=11, weight='bold'),
                bg=COLORS['surface'],
                fg=COLORS['text_secondary']
            )
            label_widget.pack(side='right', padx=(0, 10))

            value_widget = create_rtl_label(
                row_frame,
                text=str(value),
                font=self.style_manager.get_font('arabic', size=11),
                bg=COLORS['surface'],
                fg=COLORS['text_primary']
            )
            value_widget.pack(side='right')

    def create_status_section(self, parent, shipment):
        """إنشاء قسم حالة الشحنة"""
        section_frame = create_rtl_card(parent, title="📊 حالة الشحنة")
        section_frame.pack(fill='x', padx=10, pady=5)

        content_frame = create_rtl_frame(section_frame, bg=COLORS['surface'])
        content_frame.pack(fill='x', padx=15, pady=10)

        # حالة الشحنة الحالية
        status = shipment.get('status', '')
        status_text = SHIPMENT_STATUS.get(status, status)

        # تحديد لون الحالة
        status_colors = {
            'في الانتظار': COLORS['warning'],
            'مؤكدة': COLORS['info'],
            'تم الشحن': COLORS['primary'],
            'في الطريق': COLORS['secondary'],
            'وصلت': COLORS['success'],
            'في الجمارك': COLORS['warning'],
            'تم التسليم': COLORS['success'],
            'ملغية': COLORS['danger'],
            'متأخرة': COLORS['danger']
        }

        status_color = status_colors.get(status, COLORS['text_muted'])

        status_frame = create_rtl_frame(content_frame, bg=status_color, relief='raised', bd=2)
        status_frame.pack(fill='x', pady=5)

        status_label = create_rtl_label(
            status_frame,
            text=f"🔄 الحالة الحالية: {status_text}",
            font=self.style_manager.get_font('arabic', size=12, weight='bold'),
            bg=status_color,
            fg=COLORS['text_white']
        )
        status_label.pack(pady=10)

        # ملاحظات
        notes = shipment.get('notes', '')
        if notes:
            notes_label = create_rtl_label(
                content_frame,
                text=f"📝 ملاحظات: {notes}",
                font=self.style_manager.get_font('arabic', size=11),
                bg=COLORS['surface'],
                fg=COLORS['text_primary'],
                wraplength=300,
                justify='right'
            )
            notes_label.pack(fill='x', pady=(10, 0))

    def create_action_buttons_section(self, parent, shipment):
        """إنشاء قسم أزرار الإجراءات"""
        section_frame = create_rtl_frame(parent, bg=COLORS['surface'])
        section_frame.pack(fill='x', padx=10, pady=15)

        # أزرار الإجراءات
        actions_frame = create_rtl_frame(section_frame, bg=COLORS['surface'])
        actions_frame.pack(fill='x')

        action_buttons = [
            ("✏️ تعديل", self.edit_shipment, "primary"),
            ("📊 تتبع", self.track_shipment, "info"),
            ("📋 تقرير", self.shipment_report, "secondary"),
            ("🗑️ حذف", self.delete_shipment, "danger")
        ]

        for i, (text, command, style) in enumerate(action_buttons):
            btn = create_rtl_button(
                actions_frame,
                text=text,
                command=command,
                style=style,
                width=12
            )
            btn.pack(side='top', fill='x', pady=2)

    # وظائف الإجراءات
    def add_shipment(self):
        """إضافة شحنة جديدة"""
        try:
            # فتح نافذة إضافة شحنة جديدة
            from src.shipment_form_window import ShipmentFormWindow
            form_window = ShipmentFormWindow(self.root, mode='add')
            self.root.wait_window(form_window.root)

            # تحديث البيانات بعد الإضافة
            self.refresh_data()

        except ImportError:
            # إنشاء نافذة بسيطة للإضافة
            self.show_simple_add_form()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة شحنة جديدة: {str(e)}")

    def edit_shipment(self):
        """تعديل الشحنة المحددة"""
        if not self.selected_shipment:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للتعديل")
            return

        try:
            # فتح نافذة تعديل الشحنة
            from src.shipment_form_window import ShipmentFormWindow
            form_window = ShipmentFormWindow(self.root, mode='edit', shipment_data=self.selected_shipment)
            self.root.wait_window(form_window.root)

            # تحديث البيانات بعد التعديل
            self.refresh_data()

        except ImportError:
            # إنشاء نافذة بسيطة للتعديل
            self.show_simple_edit_form()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تعديل الشحنة: {str(e)}")

    def delete_shipment(self):
        """حذف الشحنة المحددة"""
        if not self.selected_shipment:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الشحنة رقم {self.selected_shipment.get('shipment_number', '')}؟\n\nهذا الإجراء لا يمكن التراجع عنه."
        )

        if result:
            try:
                # حذف الشحنة من قاعدة البيانات
                self.db_manager.execute_query(
                    "DELETE FROM shipments WHERE id = ?",
                    (self.selected_shipment['id'],)
                )

                messagebox.showinfo("نجح", "تم حذف الشحنة بنجاح")

                # تحديث البيانات
                self.refresh_data()

                # مسح التفاصيل
                self.clear_details()

            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في حذف الشحنة: {str(e)}")

    def track_shipment(self):
        """تتبع الشحنة"""
        if not self.selected_shipment:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة للتتبع")
            return

        # إظهار نافذة تتبع الشحنة
        self.show_tracking_window()

    def generate_report(self):
        """إنشاء تقرير الشحنات"""
        try:
            # فتح نافذة التقارير
            from src.reports_window import ReportsWindow
            reports_window = ReportsWindow(self.root)

        except ImportError:
            messagebox.showinfo("تقرير الشحنات", "سيتم إنشاء تقرير الشحنات قريباً")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء التقرير: {str(e)}")

    def shipment_report(self):
        """تقرير الشحنة المحددة"""
        if not self.selected_shipment:
            messagebox.showwarning("تحذير", "يرجى اختيار شحنة لإنشاء التقرير")
            return

        messagebox.showinfo("تقرير الشحنة", f"سيتم إنشاء تقرير للشحنة رقم {self.selected_shipment.get('shipment_number', '')}")

    def refresh_data(self):
        """تحديث البيانات"""
        self.status_label.config(text="جاري التحديث...")
        self.root.update()

        try:
            self.load_data()
            self.status_label.config(text="تم التحديث بنجاح")
        except Exception as e:
            self.status_label.config(text="خطأ في التحديث")
            messagebox.showerror("خطأ", f"خطأ في تحديث البيانات: {str(e)}")

    def focus_search(self):
        """تركيز على حقل البحث"""
        self.search_entry.focus()

    def show_help(self):
        """إظهار المساعدة"""
        help_text = """
🚢 نافذة إدارة الشحنات المتقدمة

🔍 البحث والتصفية:
• البحث العام: ابحث في رقم الشحنة، المورد، أو رقم الحاوية
• تصفية الحالة: اختر حالة معينة لعرض الشحنات
• تصفية المورد: اختر مورد معين
• تصفية التاريخ: حدد فترة زمنية

📋 إدارة الشحنات:
• انقر على شحنة لعرض التفاصيل
• انقر نقراً مزدوجاً للتعديل
• انقر بالزر الأيمن لقائمة الخيارات

⌨️ اختصارات لوحة المفاتيح:
• Ctrl+N: شحنة جديدة
• F2: تعديل الشحنة المحددة
• Delete: حذف الشحنة المحددة
• Ctrl+F: التركيز على البحث
• F5: تحديث البيانات
• F1: هذه المساعدة

🎨 ألوان الحالات:
• أصفر: في الانتظار/في الجمارك
• أزرق: مؤكدة/تم الشحن
• أخضر: وصلت/تم التسليم
• أحمر: ملغية/متأخرة
        """

        messagebox.showinfo("مساعدة - إدارة الشحنات", help_text)

    def view_details(self):
        """عرض تفاصيل الشحنة"""
        if self.selected_shipment:
            self.show_shipment_details(self.selected_shipment)

    def copy_shipment_number(self):
        """نسخ رقم الشحنة"""
        if self.selected_shipment:
            shipment_number = self.selected_shipment.get('shipment_number', '')
            self.root.clipboard_clear()
            self.root.clipboard_append(shipment_number)
            messagebox.showinfo("تم النسخ", f"تم نسخ رقم الشحنة: {shipment_number}")

    def clear_details(self):
        """مسح تفاصيل الشحنة"""
        for widget in self.details_content.winfo_children():
            widget.destroy()

        self.create_default_details_message()
        self.selected_shipment = None

    def show_simple_add_form(self):
        """إظهار نموذج إضافة بسيط"""
        messagebox.showinfo("إضافة شحنة", "سيتم فتح نموذج إضافة شحنة جديدة قريباً")

    def show_simple_edit_form(self):
        """إظهار نموذج تعديل بسيط"""
        messagebox.showinfo("تعديل شحنة", f"سيتم فتح نموذج تعديل الشحنة رقم {self.selected_shipment.get('shipment_number', '')} قريباً")

    def show_tracking_window(self):
        """إظهار نافذة تتبع الشحنة"""
        tracking_info = f"""
🚢 تتبع الشحنة رقم: {self.selected_shipment.get('shipment_number', '')}

📊 الحالة الحالية: {SHIPMENT_STATUS.get(self.selected_shipment.get('status', ''), self.selected_shipment.get('status', ''))}

🏢 المورد: {self.selected_shipment.get('supplier_name', '')}

📅 تاريخ الشحن: {self.format_date(self.selected_shipment.get('shipment_date'))}
📅 تاريخ الوصول المتوقع: {self.format_date(self.selected_shipment.get('expected_arrival_date'))}

🚢 من: {PORTS.get(self.selected_shipment.get('departure_port', ''), self.selected_shipment.get('departure_port', ''))}
🏁 إلى: {PORTS.get(self.selected_shipment.get('arrival_port', ''), self.selected_shipment.get('arrival_port', ''))}

📦 رقم الحاوية: {self.selected_shipment.get('container_number', '')}
📋 بوليصة الشحن: {self.selected_shipment.get('bill_of_lading', '')}
        """

        messagebox.showinfo("تتبع الشحنة", tracking_info)

    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def on_closing(self):
        """عند إغلاق النافذة"""
        self.root.destroy()

def show_advanced_shipments_window(parent=None):
    """إظهار نافذة إدارة الشحنات المتقدمة"""
    window = AdvancedShipmentsWindow(parent)
    return window

if __name__ == "__main__":
    # اختبار النافذة
    app = AdvancedShipmentsWindow()
    app.root.mainloop()
