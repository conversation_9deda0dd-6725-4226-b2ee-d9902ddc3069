#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام البحث في الأصناف
"""

from database.database_manager import DatabaseManager

def test_item_search():
    """اختبار البحث في الأصناف"""
    print("🔍 اختبار نظام البحث في الأصناف")
    print("=" * 50)
    
    db_manager = DatabaseManager()
    
    try:
        # اختبار الاستعلام المستخدم في ItemCodeSearchDialog
        query = """
            SELECT i.id, i.item_code, i.item_name, i.description, i.unit_of_measure,
                   i.cost_price, i.selling_price, i.current_stock, c.category_name,
                   i.barcode, i.is_active
            FROM items i
            LEFT JOIN item_categories c ON i.category_id = c.id
            WHERE i.is_active = 1
            ORDER BY i.item_code
        """
        
        items = db_manager.fetch_all(query)
        
        print(f"📦 عدد الأصناف الموجودة: {len(items)}")
        print("\n📋 قائمة الأصناف:")
        print("-" * 80)
        
        if items:
            for item in items:
                print(f"🔢 الكود: {item['item_code']}")
                print(f"📦 الاسم: {item['item_name']}")
                print(f"📝 الوصف: {item['description'] or 'غير محدد'}")
                print(f"📏 الوحدة: {item['unit_of_measure']}")
                print(f"💰 السعر: {item['selling_price'] or item['cost_price'] or 0}")
                print(f"📊 المخزون: {item['current_stock'] or 0}")
                print(f"🏷️ الفئة: {item['category_name'] or 'غير محدد'}")
                print("-" * 80)
        else:
            print("❌ لا توجد أصناف في قاعدة البيانات")
            print("💡 تشغيل add_sample_items.py لإضافة بيانات تجريبية")
        
        # اختبار الاستعلام المستخدم في ItemSearchDialog
        print("\n🔍 اختبار استعلام ItemSearchDialog:")
        query2 = """
            SELECT i.id, i.item_code, i.item_name, i.description, i.unit_of_measure,
                   i.cost_price, i.selling_price, i.current_stock, c.category_name,
                   i.barcode, i.is_active
            FROM items i
            LEFT JOIN item_categories c ON i.category_id = c.id
            WHERE i.is_active = 1
            ORDER BY i.item_name
        """
        
        items2 = db_manager.fetch_all(query2)
        print(f"✅ ItemSearchDialog - عدد الأصناف: {len(items2)}")
        
        if len(items) == len(items2):
            print("✅ كلا الاستعلامين يعطيان نفس النتائج")
        else:
            print("⚠️ هناك اختلاف في النتائج بين الاستعلامين")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    test_item_search()
