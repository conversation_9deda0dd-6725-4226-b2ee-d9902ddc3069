#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لميزة استيراد Excel
Full Excel Import Feature Test
"""

import sys
import os
import traceback
import tkinter as tk
from tkinter import ttk

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_excel_file():
    """إنشاء ملف Excel للاختبار"""
    print("📁 إنشاء ملف Excel للاختبار...")
    
    try:
        import pandas as pd
        
        # بيانات اختبار شاملة
        test_data = {
            'item_code': [
                'EXCEL_TEST_001',
                'EXCEL_TEST_002', 
                'EXCEL_TEST_003',
                'EXCEL_TEST_004',
                'EXCEL_TEST_005'
            ],
            'item_name': [
                'صنف اختبار Excel 1',
                'صنف اختبار Excel 2',
                'صنف اختبار Excel 3',
                'صنف اختبار Excel 4',
                'صنف اختبار Excel 5'
            ],
            'unit_of_measure': [
                'قطعة',
                'كيلو',
                'متر',
                'لتر',
                'قطعة'
            ],
            'category_name': [
                'فئة اختبار Excel',
                'فئة اختبار Excel',
                'فئة اختبار أخرى',
                'فئة اختبار Excel',
                'فئة جديدة للاختبار'
            ],
            'cost_price': [
                100.50,
                75.25,
                200.00,
                50.75,
                150.00
            ],
            'selling_price': [
                120.00,
                90.00,
                250.00,
                65.00,
                180.00
            ],
            'current_stock': [
                25,
                50,
                10,
                100,
                30
            ],
            'min_stock_level': [
                10,
                20,
                5,
                25,
                15
            ],
            'description': [
                'وصف الصنف الأول للاختبار',
                'وصف الصنف الثاني مع تفاصيل أكثر',
                'صنف للاختبار بوصف مختصر',
                'وصف مفصل للصنف الرابع في الاختبار',
                'الصنف الخامس للاختبار النهائي'
            ]
        }
        
        df = pd.DataFrame(test_data)
        test_file = 'full_excel_import_test.xlsx'
        df.to_excel(test_file, index=False)
        
        print(f"✅ تم إنشاء ملف الاختبار: {test_file}")
        print(f"📊 يحتوي على {len(df)} صف و {len(df.columns)} عمود")
        
        return test_file
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف الاختبار: {e}")
        return None

def test_import_process():
    """اختبار عملية الاستيراد"""
    print("\n🧪 اختبار عملية الاستيراد...")
    
    try:
        import pandas as pd
        from database.database_manager import DatabaseManager
        from datetime import datetime
        
        # إنشاء ملف الاختبار
        test_file = create_test_excel_file()
        if not test_file:
            return False
        
        # قراءة الملف
        df = pd.read_excel(test_file)
        print(f"✅ تم قراءة الملف - {len(df)} صف")
        
        # اختبار معالجة البيانات
        db_manager = DatabaseManager()
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # حذف بيانات الاختبار السابقة
        cursor.execute("DELETE FROM items WHERE item_code LIKE 'EXCEL_TEST_%'")
        cursor.execute("DELETE FROM item_categories WHERE category_name LIKE '%اختبار%'")
        conn.commit()
        
        print("🔄 بدء معالجة البيانات...")
        
        imported_count = 0
        error_count = 0
        
        for index, row in df.iterrows():
            try:
                # استخراج البيانات
                item_code = str(row['item_code']).strip()
                item_name = str(row['item_name']).strip()
                unit_of_measure = str(row['unit_of_measure']).strip()
                category_name = str(row['category_name']).strip() if pd.notna(row['category_name']) else None
                cost_price = float(row['cost_price']) if pd.notna(row['cost_price']) else 0.0
                selling_price = float(row['selling_price']) if pd.notna(row['selling_price']) else 0.0
                current_stock = float(row['current_stock']) if pd.notna(row['current_stock']) else 0.0
                min_stock_level = float(row['min_stock_level']) if pd.notna(row['min_stock_level']) else 0.0
                description = str(row['description']).strip() if pd.notna(row['description']) else ''
                
                # البحث عن الفئة أو إنشاؤها
                category_id = None
                if category_name:
                    cursor.execute("SELECT id FROM item_categories WHERE category_name = ?", (category_name,))
                    category_result = cursor.fetchone()
                    if category_result:
                        category_id = category_result[0]
                    else:
                        cursor.execute(
                            "INSERT INTO item_categories (category_name, is_active) VALUES (?, 1)",
                            (category_name,)
                        )
                        category_id = cursor.lastrowid
                
                # إدراج الصنف
                cursor.execute("""
                    INSERT INTO items (
                        item_code, item_name, category_id, unit_of_measure,
                        cost_price, selling_price, current_stock, min_stock_level,
                        description, is_active, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?)
                """, (
                    item_code, item_name, category_id, unit_of_measure,
                    cost_price, selling_price, current_stock, min_stock_level,
                    description, datetime.now(), datetime.now()
                ))
                
                imported_count += 1
                print(f"  ✅ تم استيراد: {item_code} - {item_name}")
                
            except Exception as e:
                error_count += 1
                print(f"  ❌ خطأ في الصف {index + 1}: {e}")
        
        conn.commit()
        
        # التحقق من النتائج
        cursor.execute("SELECT COUNT(*) FROM items WHERE item_code LIKE 'EXCEL_TEST_%'")
        final_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM item_categories WHERE category_name LIKE '%اختبار%'")
        categories_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"\n📊 نتائج الاستيراد:")
        print(f"  📥 تم استيراد: {imported_count} صنف")
        print(f"  ❌ أخطاء: {error_count}")
        print(f"  📋 أصناف في قاعدة البيانات: {final_count}")
        print(f"  🏷️ فئات تم إنشاؤها: {categories_count}")
        
        # تنظيف - حذف بيانات الاختبار
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        cursor.execute("DELETE FROM items WHERE item_code LIKE 'EXCEL_TEST_%'")
        cursor.execute("DELETE FROM item_categories WHERE category_name LIKE '%اختبار%'")
        conn.commit()
        conn.close()
        
        # حذف ملف الاختبار
        os.remove(test_file)
        
        print("🧹 تم تنظيف بيانات الاختبار")
        
        return imported_count > 0 and error_count == 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الاستيراد: {e}")
        print(f"📋 تفاصيل: {traceback.format_exc()}")
        return False

def test_ui_components():
    """اختبار مكونات واجهة المستخدم"""
    print("\n🧪 اختبار مكونات واجهة المستخدم...")
    
    try:
        from src.items_window import ItemsWindow
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # إنشاء نافذة الأصناف
        items_window = ItemsWindow(root)
        
        # التحقق من وجود الأزرار
        has_import_button = hasattr(items_window, 'import_button')
        has_template_button = hasattr(items_window, 'template_button')
        
        # التحقق من وجود الدوال
        has_import_function = hasattr(items_window, 'import_from_excel')
        has_template_function = hasattr(items_window, 'create_excel_template')
        has_process_function = hasattr(items_window, 'process_excel_import')
        
        root.destroy()
        
        print(f"  {'✅' if has_import_button else '❌'} زر الاستيراد")
        print(f"  {'✅' if has_template_button else '❌'} زر القالب")
        print(f"  {'✅' if has_import_function else '❌'} دالة الاستيراد")
        print(f"  {'✅' if has_template_function else '❌'} دالة القالب")
        print(f"  {'✅' if has_process_function else '❌'} دالة المعالجة")
        
        return all([
            has_import_button, has_template_button,
            has_import_function, has_template_function, has_process_function
        ])
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة المستخدم: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار الشامل"""
    print("🚀 اختبار شامل لميزة استيراد Excel")
    print("=" * 50)
    
    # اختبار مكونات واجهة المستخدم
    ui_ok = test_ui_components()
    
    # اختبار عملية الاستيراد
    import_ok = test_import_process()
    
    print("\n" + "=" * 50)
    print("📋 النتائج النهائية:")
    print(f"{'✅' if ui_ok else '❌'} مكونات واجهة المستخدم")
    print(f"{'✅' if import_ok else '❌'} عملية الاستيراد")
    
    if ui_ok and import_ok:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✨ ميزة استيراد Excel جاهزة للاستخدام")
        print("\n📝 للاستخدام:")
        print("1. افتح شاشة إدارة الأصناف")
        print("2. اضغط '📋 قالب Excel' لإنشاء قالب")
        print("3. املأ البيانات واحفظ الملف")
        print("4. اضغط '📥 استيراد من Excel' لاستيراد البيانات")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى إصلاح")
        if not ui_ok:
            print("  - مشكلة في مكونات واجهة المستخدم")
        if not import_ok:
            print("  - مشكلة في عملية الاستيراد")

if __name__ == "__main__":
    main()
