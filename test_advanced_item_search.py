#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لشاشة البحث المتقدم في أكواد الأصناف
Comprehensive Test for Advanced Item Code Search Screen
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
import time
import threading

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from src.item_code_search_dialog import ItemCodeSearchDialog
from database.database_manager import DatabaseManager

class AdvancedItemSearchTester:
    """فئة اختبار البحث المتقدم"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.test_results = []
        self.setup_test_data()
        
    def setup_test_data(self):
        """إعداد بيانات الاختبار"""
        print("🔧 إعداد بيانات الاختبار...")
        
        # إنشاء بيانات اختبار متنوعة
        test_items = [
            {
                'item_code': 'TEST001',
                'item_name': 'جهاز كمبيوتر محمول',
                'description': 'جهاز كمبيوتر محمول عالي الأداء للألعاب',
                'unit': 'قطعة',
                'unit_price': 2500.00,
                'origin_country': 'الصين'
            },
            {
                'item_code': 'TEST002', 
                'item_name': 'هاتف ذكي',
                'description': 'هاتف ذكي بكاميرا عالية الدقة',
                'unit': 'قطعة',
                'unit_price': 800.00,
                'origin_country': 'كوريا الجنوبية'
            },
            {
                'item_code': 'TEST003',
                'item_name': 'طابعة ليزر',
                'description': 'طابعة ليزر ملونة للمكاتب',
                'unit': 'قطعة', 
                'unit_price': 450.00,
                'origin_country': 'اليابان'
            },
            {
                'item_code': 'ELEC001',
                'item_name': 'كابل USB',
                'description': 'كابل USB عالي السرعة',
                'unit': 'متر',
                'unit_price': 15.50,
                'origin_country': 'الصين'
            },
            {
                'item_code': 'ELEC002',
                'item_name': 'شاحن لاسلكي',
                'description': 'شاحن لاسلكي سريع للهواتف الذكية',
                'unit': 'قطعة',
                'unit_price': 75.00,
                'origin_country': 'الصين'
            }
        ]
        
        try:
            # حذف بيانات الاختبار السابقة
            self.db_manager.execute_query(
                "DELETE FROM shipment_items WHERE item_code LIKE 'TEST%' OR item_code LIKE 'ELEC%'"
            )
            
            # إدراج بيانات الاختبار الجديدة
            for item in test_items:
                query = """
                    INSERT INTO shipment_items
                    (item_code, item_name, description, unit, unit_price, origin_country, quantity, shipment_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, 1)
                """
                self.db_manager.execute_query(query, (
                    item['item_code'],
                    item['item_name'],
                    item['description'],
                    item['unit'],
                    item['unit_price'],
                    item['origin_country'],
                    1  # quantity
                ))
            
            print("✅ تم إعداد بيانات الاختبار بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في إعداد بيانات الاختبار: {e}")
    
    def run_automated_tests(self):
        """تشغيل الاختبارات الآلية"""
        print("\n🧪 بدء الاختبارات الآلية...")
        
        tests = [
            self.test_database_connection,
            self.test_data_loading,
            self.test_general_search,
            self.test_advanced_search_fields,
            self.test_price_range_filter,
            self.test_country_filter,
            self.test_search_performance
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                result = test()
                if result:
                    passed += 1
                    print(f"✅ {test.__name__}: نجح")
                else:
                    print(f"❌ {test.__name__}: فشل")
            except Exception as e:
                print(f"❌ {test.__name__}: خطأ - {e}")
        
        print(f"\n📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
        return passed == total
    
    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            result = self.db_manager.fetch_all("SELECT COUNT(*) as count FROM shipment_items")
            return len(result) > 0
        except:
            return False
    
    def test_data_loading(self):
        """اختبار تحميل البيانات"""
        try:
            query = "SELECT COUNT(*) as count FROM shipment_items WHERE item_code LIKE 'TEST%'"
            result = self.db_manager.fetch_all(query)
            return result[0]['count'] >= 3
        except:
            return False
    
    def test_general_search(self):
        """اختبار البحث العام"""
        try:
            # محاكاة البحث العام
            query = """
                SELECT * FROM shipment_items 
                WHERE LOWER(item_code || ' ' || item_name || ' ' || COALESCE(description, '')) 
                LIKE '%كمبيوتر%'
            """
            result = self.db_manager.fetch_all(query)
            return len(result) > 0
        except:
            return False
    
    def test_advanced_search_fields(self):
        """اختبار حقول البحث المتقدم"""
        try:
            # اختبار البحث بكود الصنف
            query = "SELECT * FROM shipment_items WHERE LOWER(item_code) LIKE '%test%'"
            result = self.db_manager.fetch_all(query)
            return len(result) >= 3
        except:
            return False
    
    def test_price_range_filter(self):
        """اختبار فلتر نطاق السعر"""
        try:
            query = "SELECT * FROM shipment_items WHERE unit_price BETWEEN 100 AND 1000"
            result = self.db_manager.fetch_all(query)
            return len(result) > 0
        except:
            return False
    
    def test_country_filter(self):
        """اختبار فلتر بلد المنشأ"""
        try:
            query = "SELECT * FROM shipment_items WHERE origin_country = 'الصين'"
            result = self.db_manager.fetch_all(query)
            return len(result) > 0
        except:
            return False
    
    def test_search_performance(self):
        """اختبار أداء البحث"""
        try:
            start_time = time.time()
            query = "SELECT * FROM shipment_items LIMIT 1000"
            self.db_manager.fetch_all(query)
            end_time = time.time()
            
            # يجب أن يكون البحث أسرع من ثانية واحدة
            return (end_time - start_time) < 1.0
        except:
            return False
    
    def run_interactive_test(self):
        """تشغيل الاختبار التفاعلي"""
        print("\n🖥️ بدء الاختبار التفاعلي...")
        
        root = tk.Tk()
        root.title("اختبار البحث المتقدم في أكواد الأصناف")
        root.geometry("400x300")
        root.configure(bg='#f0f0f0')
        
        # إنشاء واجهة الاختبار
        title_label = tk.Label(
            root,
            text="🔍 اختبار البحث المتقدم",
            font=('Segoe UI', 16, 'bold'),
            bg='#f0f0f0'
        )
        title_label.pack(pady=20)
        
        instructions = tk.Label(
            root,
            text="انقر على الزر أدناه لفتح شاشة البحث المتقدم\nواختبر جميع الوظائف المتاحة",
            font=('Segoe UI', 12),
            bg='#f0f0f0',
            justify='center'
        )
        instructions.pack(pady=10)
        
        def open_search_dialog():
            try:
                dialog = ItemCodeSearchDialog(root, None)
                root.wait_window(dialog.root)
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في فتح شاشة البحث: {e}")
        
        search_btn = tk.Button(
            root,
            text="🔍 فتح شاشة البحث المتقدم",
            font=('Segoe UI', 14, 'bold'),
            bg='#007bff',
            fg='white',
            command=open_search_dialog,
            padx=20,
            pady=10
        )
        search_btn.pack(pady=20)
        
        # معلومات الاختبار
        test_info = tk.Label(
            root,
            text="اختبر الوظائف التالية:\n• البحث الفوري\n• البحث المتقدم\n• فلاتر الأسعار\n• فلتر بلد المنشأ",
            font=('Segoe UI', 10),
            bg='#f0f0f0',
            justify='center'
        )
        test_info.pack(pady=10)
        
        root.mainloop()
    
    def cleanup_test_data(self):
        """تنظيف بيانات الاختبار"""
        try:
            self.db_manager.execute_query(
                "DELETE FROM shipment_items WHERE item_code LIKE 'TEST%' OR item_code LIKE 'ELEC%'"
            )
            print("🧹 تم تنظيف بيانات الاختبار")
        except Exception as e:
            print(f"❌ خطأ في تنظيف بيانات الاختبار: {e}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار البحث المتقدم في أكواد الأصناف")
    print("=" * 60)
    
    tester = AdvancedItemSearchTester()
    
    try:
        # تشغيل الاختبارات الآلية
        automated_success = tester.run_automated_tests()
        
        if automated_success:
            print("\n✅ جميع الاختبارات الآلية نجحت!")
            
            # تشغيل الاختبار التفاعلي
            response = input("\n❓ هل تريد تشغيل الاختبار التفاعلي؟ (y/n): ")
            if response.lower() in ['y', 'yes', 'نعم']:
                tester.run_interactive_test()
        else:
            print("\n❌ بعض الاختبارات الآلية فشلت!")
    
    finally:
        # تنظيف بيانات الاختبار
        tester.cleanup_test_data()
    
    print("\n🏁 انتهى الاختبار")

if __name__ == "__main__":
    main()
