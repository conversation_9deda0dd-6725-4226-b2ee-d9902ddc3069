#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع لنموذج الشحنة
Quick test for shipment form
"""

import tkinter as tk
import sys
import os

# إضافة مجلد src للمسار
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from fullscreen_shipment_form import FullscreenShipmentForm
    print("✅ تم استيراد FullscreenShipmentForm بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد FullscreenShipmentForm: {e}")
    sys.exit(1)

def test_shipment_form():
    """اختبار فتح نموذج الشحنة"""
    print("🧪 اختبار فتح نموذج الشحنة...")
    
    try:
        # إنشاء نافذة رئيسية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        print("📋 إنشاء نموذج الشحنة...")
        
        # إنشاء نموذج الشحنة
        form = FullscreenShipmentForm(root, mode='add')
        
        print("✅ تم إنشاء نموذج الشحنة بنجاح!")
        print("🎯 اختبار الوظائف الأساسية...")
        
        # اختبار الوظائف الأساسية
        if hasattr(form, 'save_shipment'):
            print("✅ دالة save_shipment موجودة")
        else:
            print("❌ دالة save_shipment مفقودة")
            
        if hasattr(form, 'save_and_close'):
            print("✅ دالة save_and_close موجودة")
        else:
            print("❌ دالة save_and_close مفقودة")
            
        if hasattr(form, 'close_form'):
            print("✅ دالة close_form موجودة")
        else:
            print("❌ دالة close_form مفقودة")
            
        if hasattr(form, 'show_item_search'):
            print("✅ دالة show_item_search موجودة")
        else:
            print("❌ دالة show_item_search مفقودة")
            
        if hasattr(form, 'fill_simple_form_from_search'):
            print("✅ دالة fill_simple_form_from_search موجودة")
        else:
            print("❌ دالة fill_simple_form_from_search مفقودة")
        
        print("🎉 جميع الاختبارات نجحت!")
        print("📝 يمكنك الآن استخدام نموذج الشحنة بأمان")
        
        # إغلاق النموذج
        form.root.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نموذج الشحنة: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 اختبار نموذج الشحنة - Shipment Form Test")
    print("=" * 60)
    
    success = test_shipment_form()
    
    if success:
        print("\n✅ جميع الاختبارات نجحت - All tests passed!")
        print("🚀 نموذج الشحنة جاهز للاستخدام")
    else:
        print("\n❌ فشل في الاختبار - Test failed!")
        sys.exit(1)
