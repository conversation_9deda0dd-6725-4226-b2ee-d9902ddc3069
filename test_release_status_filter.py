#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار فلتر حالة الإفراج في شاشات إدارة الشحنات
Test Release Status Filter in Shipment Management Screens
"""

import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def test_config_import():
    """اختبار استيراد إعدادات حالة الإفراج"""
    try:
        from config.config import RELEASE_STATUS
        print("✅ تم استيراد RELEASE_STATUS بنجاح:")
        for key, value in RELEASE_STATUS.items():
            print(f"   {key}: {value}")
        return True
    except ImportError as e:
        print(f"❌ خطأ في استيراد RELEASE_STATUS: {e}")
        return False

def test_advanced_shipments_manager_import():
    """اختبار استيراد مدير الشحنات المتقدم"""
    try:
        from src.advanced_shipments_manager import AdvancedShipmentsManager
        print("✅ تم استيراد AdvancedShipmentsManager بنجاح")
        return True
    except ImportError as e:
        print(f"❌ خطأ في استيراد AdvancedShipmentsManager: {e}")
        return False

def test_advanced_shipments_window_import():
    """اختبار استيراد نافذة الشحنات المتقدمة"""
    try:
        from src.advanced_shipments_window import AdvancedShipmentsWindow
        print("✅ تم استيراد AdvancedShipmentsWindow بنجاح")
        return True
    except ImportError as e:
        print(f"❌ خطأ في استيراد AdvancedShipmentsWindow: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار فلتر حالة الإفراج...")
    print("=" * 50)
    
    tests = [
        ("اختبار إعدادات حالة الإفراج", test_config_import),
        ("اختبار مدير الشحنات المتقدم", test_advanced_shipments_manager_import),
        ("اختبار نافذة الشحنات المتقدمة", test_advanced_shipments_window_import),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"   فشل الاختبار: {test_name}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! فلتر حالة الإفراج جاهز للاستخدام.")
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
