========================================
نظام متابعة شحنات الموردين
تعليمات التشغيل والاستخدام
========================================

1. متطلبات النظام:
   - نظام التشغيل: Windows 10 أو أحدث
   - Python 3.8 أو أحدث
   - ذاكرة: 4 GB RAM كحد أدنى
   - مساحة القرص: 500 MB

2. تثبيت المكتبات المطلوبة:
   افتح موجه الأوامر (Command Prompt) في مجلد البرنامج وقم بتشغيل:
   pip install -r requirements.txt

3. تشغيل البرنامج:
   طريقة 1: انقر نقراً مزدوجاً على ملف run.bat
   طريقة 2: افتح موجه الأوامر وقم بتشغيل: python main.py

4. تسجيل الدخول الافتراضي:
   اسم المستخدم: admin
   كلمة المرور: admin123

5. الوظائف الرئيسية:

   أ) إدارة الموردين:
      - إضافة موردين جدد
      - تعديل بيانات الموردين
      - البحث والتصفية
      - إدارة معلومات الاتصال

   ب) إدارة الأصناف:
      - إضافة أصناف جديدة
      - تصنيف الأصناف
      - إدارة المخزون
      - تحديد الحد الأدنى للمخزون

   ج) إدارة الشحنات:
      - إنشاء شحنات جديدة
      - تتبع حالة الشحنات
      - إدارة تفاصيل الشحن
      - متابعة الوصول والتسليم

   د) التقارير:
      - تقارير الشحنات
      - تقارير الموردين
      - تقارير المخزون
      - تصدير للـ Excel و PDF

6. إدارة المستخدمين:
   - يمكن للمدير إضافة مستخدمين جدد
   - تحديد الصلاحيات لكل مستخدم
   - تغيير كلمات المرور

7. النسخ الاحتياطي:
   - يتم إنشاء نسخ احتياطية تلقائياً
   - يمكن إنشاء نسخة احتياطية يدوياً من قائمة "ملف"

8. استكشاف الأخطاء:
   - تحقق من تثبيت Python بشكل صحيح
   - تأكد من تثبيت جميع المكتبات المطلوبة
   - راجع ملف السجلات في مجلد logs

9. الدعم الفني:
   في حالة وجود مشاكل، يرجى مراجعة:
   - ملف README.md للتفاصيل الكاملة
   - ملفات السجلات في مجلد logs
   - التأكد من صحة إعدادات قاعدة البيانات

10. ملاحظات مهمة:
    - احتفظ بنسخ احتياطية منتظمة
    - لا تحذف ملفات قاعدة البيانات
    - استخدم كلمات مرور قوية
    - قم بتحديث البرنامج بانتظام

========================================
تم تطوير هذا النظام خصيصاً لإدارة شحنات الموردين
جميع الحقوق محفوظة
========================================
