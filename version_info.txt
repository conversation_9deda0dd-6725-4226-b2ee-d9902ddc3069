# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Ship Management System'),
        StringStruct(u'FileDescription', u'نظام متابعة شحنات الموردين'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'ShipManagementSystem'),
        StringStruct(u'LegalCopyright', u'© 2024 Ship Management System'),
        StringStruct(u'OriginalFilename', u'ShipManagementSystem.exe'),
        StringStruct(u'ProductName', u'Ship Management System'),
        StringStruct(u'ProductVersion', u'*******')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)