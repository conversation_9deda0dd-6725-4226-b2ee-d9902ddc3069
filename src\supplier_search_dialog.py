#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة حوار البحث في الموردين مع دعم RTL كامل
Supplier Search Dialog with Full RTL Support
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS
from src.simple_rtl_components import *
from database.database_manager import DatabaseManager

class SupplierSearchDialog:
    """نافذة حوار البحث في الموردين"""
    
    def __init__(self, parent, shipment_form):
        self.parent = parent
        self.shipment_form = shipment_form
        self.db_manager = DatabaseManager()
        self.suppliers_data = []
        self.filtered_suppliers = []
        self.selected_supplier = None
        
        # إنشاء النافذة
        self.root = tk.Toplevel(parent)
        self.setup_window()
        
        # متغيرات البحث
        self.search_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_suppliers_from_database()
        
        # توسيط النافذة
        self.center_window()
        
        # تركيز على حقل البحث
        self.focus_search_field()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.root.title("🔍 البحث المتقدم في الموردين - F9")
        self.root.geometry("1200x800")
        self.root.configure(bg=COLORS['background'])
        self.root.resizable(True, True)
        self.root.minsize(1000, 700)

        # جعل النافذة modal
        self.root.transient(self.parent)
        self.root.grab_set()

        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.close_dialog)

        # ربط اختصارات لوحة المفاتيح المحسنة
        self.root.bind('<Return>', lambda e: self.select_supplier())
        self.root.bind('<Escape>', lambda e: self.close_dialog())
        self.root.bind('<F9>', lambda e: self.close_dialog())
        self.root.bind('<Double-Button-1>', lambda e: self.select_supplier())
        self.root.bind('<Control-f>', lambda e: self.focus_search_field())
        self.root.bind('<F5>', lambda e: self.refresh_data())
        
    def create_interface(self):
        """إنشاء الواجهة المحسنة"""
        # الإطار الرئيسي
        main_frame = create_simple_rtl_frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=25, pady=25)

        # شريط العنوان المحسن
        header_frame = create_simple_rtl_frame(main_frame)
        header_frame.pack(fill='x', pady=(0, 25))

        # العنوان الرئيسي
        title_label = create_simple_rtl_label(
            header_frame,
            text="🔍 البحث المتقدم في الموردين",
            font=('Segoe UI', 20, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e', pady=(0, 8))

        # وصف محسن
        desc_label = create_simple_rtl_label(
            header_frame,
            text="🚀 بحث فوري ومتقدم في قاعدة بيانات الموردين مع نتائج لحظية",
            font=('Segoe UI', 13, 'normal'),
            fg=COLORS['text_secondary']
        )
        desc_label.pack(anchor='e', pady=(0, 5))

        # مؤشر الحالة
        self.status_label = create_simple_rtl_label(
            header_frame,
            text="⚡ جاهز للبحث...",
            font=('Segoe UI', 11, 'normal'),
            fg=COLORS['success']
        )
        self.status_label.pack(anchor='e')

        # منطقة البحث المحسنة
        search_container = create_simple_rtl_frame(main_frame)
        search_container.pack(fill='x', pady=(0, 25))

        # عنوان منطقة البحث
        search_title = create_simple_rtl_label(
            search_container,
            text="🎯 أدوات البحث المتقدم",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['primary']
        )
        search_title.pack(anchor='e', pady=(0, 15))

        # إطار البحث الرئيسي
        search_frame = create_simple_rtl_frame(search_container)
        search_frame.pack(fill='x', pady=(0, 15))

        # البحث السريع المحسن
        quick_search_frame = create_simple_rtl_frame(search_frame)
        quick_search_frame.pack(fill='x', pady=(0, 15))

        search_label = create_simple_rtl_label(
            quick_search_frame,
            text="⚡ البحث السريع (فوري):",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['primary']
        )
        search_label.pack(anchor='e', pady=(0, 8))

        self.search_vars['general'] = tk.StringVar()
        self.general_search_entry = create_simple_rtl_entry(
            quick_search_frame,
            textvariable=self.search_vars['general'],
            placeholder="ابحث في كود المورد، الاسم، الهاتف، الإيميل، العنوان..."
        )
        self.general_search_entry.configure(
            font=('Segoe UI', 13, 'normal'),
            relief='solid',
            borderwidth=2
        )
        self.general_search_entry.pack(fill='x', ipady=12)

        # ربط البحث الفوري
        self.search_vars['general'].trace('w', self.on_search_change)

        # زر البحث المتقدم
        advanced_button_frame = create_simple_rtl_frame(search_frame)
        advanced_button_frame.pack(fill='x', pady=(15, 5))

        self.advanced_search_button = tk.Button(
            advanced_button_frame,
            text="🔧 البحث المتقدم",
            font=('Segoe UI', 10, 'bold'),
            bg=COLORS['primary'],
            fg='white',
            relief='raised',
            borderwidth=2,
            padx=20,
            pady=8,
            command=self.toggle_advanced_search
        )
        self.advanced_search_button.pack(side='right', padx=(0, 10))

        # إطار البحث المتقدم (مخفي افتراضياً)
        self.advanced_search_frame = create_simple_rtl_frame(search_frame)
        self.advanced_search_visible = False  # حالة الإظهار/الإخفاء

        # عنوان البحث المتقدم (داخل الإطار المخفي)
        advanced_title = create_simple_rtl_label(
            self.advanced_search_frame,
            text="🔧 البحث المتقدم:",
            font=('Segoe UI', 12, 'bold'),
            fg=COLORS['secondary']
        )
        advanced_title.pack(anchor='e', pady=(10, 10))

        # الصف الأول من البحث المتقدم
        advanced_row1 = create_simple_rtl_frame(self.advanced_search_frame)
        advanced_row1.pack(fill='x', pady=(0, 10))

        # كود المورد
        self.create_advanced_search_field(
            advanced_row1, "🔢 كود المورد:", "supplier_code",
            "كود المورد...", side='right', padx=(0, 15)
        )

        # اسم المورد
        self.create_advanced_search_field(
            advanced_row1, "🏢 اسم المورد:", "supplier_name",
            "اسم المورد...", side='right', padx=(0, 15)
        )

        # الشخص المسؤول
        self.create_advanced_search_field(
            advanced_row1, "👤 الشخص المسؤول:", "contact_person",
            "اسم الشخص المسؤول...", side='right'
        )

        # الصف الثاني من البحث المتقدم
        advanced_row2 = create_simple_rtl_frame(self.advanced_search_frame)
        advanced_row2.pack(fill='x', pady=(0, 10))

        # الهاتف
        self.create_advanced_search_field(
            advanced_row2, "📞 الهاتف:", "phone",
            "رقم الهاتف...", side='right', padx=(0, 15)
        )

        # البريد الإلكتروني
        self.create_advanced_search_field(
            advanced_row2, "📧 البريد الإلكتروني:", "email",
            "البريد الإلكتروني...", side='right', padx=(0, 15)
        )

        # المدينة
        self.create_advanced_search_field(
            advanced_row2, "🏙️ المدينة:", "city",
            "المدينة...", side='right'
        )

        # الصف الثالث من البحث المتقدم
        advanced_row3 = create_simple_rtl_frame(self.advanced_search_frame)
        advanced_row3.pack(fill='x', pady=(0, 10))

        # البلد مع قائمة منسدلة
        self.create_advanced_search_combobox(
            advanced_row3, "🌍 البلد:", "country",
            ["الكل", "السعودية", "الإمارات", "الكويت", "قطر", "البحرين", "عمان", "مصر", "الأردن", "لبنان", "العراق", "الصين", "الهند", "تركيا", "ألمانيا", "إيطاليا", "فرنسا", "أمريكا", "كندا"],
            side='right', padx=(0, 15)
        )

        # حالة المورد
        self.create_advanced_search_combobox(
            advanced_row3, "✅ حالة المورد:", "status",
            ["الكل", "نشط", "غير نشط"],
            side='right', padx=(0, 15)
        )

        # نوع المورد
        self.create_advanced_search_combobox(
            advanced_row3, "🏷️ نوع المورد:", "supplier_type",
            ["الكل", "محلي", "دولي", "مصنع", "موزع", "وكيل"],
            side='right'
        )

        # أزرار التحكم المحسنة (داخل إطار البحث المتقدم)
        buttons_row = create_simple_rtl_frame(self.advanced_search_frame)
        buttons_row.pack(fill='x', pady=(20, 0))

        # الأزرار على اليمين
        right_buttons = create_simple_rtl_frame(buttons_row)
        right_buttons.pack(side='right')

        # زر تحديث البيانات
        refresh_btn = create_simple_rtl_button(
            right_buttons,
            text="🔄 تحديث البيانات",
            button_type="primary",
            command=self.refresh_data
        )
        refresh_btn.pack(side='right', padx=5)

        # زر مسح البحث
        clear_btn = create_simple_rtl_button(
            right_buttons,
            text="🗑️ مسح جميع الحقول",
            button_type="secondary",
            command=self.clear_search
        )
        clear_btn.pack(side='right', padx=5)

        # زر البحث المتقدم
        advanced_btn = create_simple_rtl_button(
            right_buttons,
            text="🎯 بحث متقدم",
            button_type="info",
            command=self.toggle_advanced_search
        )
        advanced_btn.pack(side='right', padx=5)

        # المعلومات على اليسار
        left_info = create_simple_rtl_frame(buttons_row)
        left_info.pack(side='left')

        # عداد النتائج
        self.results_label = create_simple_rtl_label(
            left_info,
            text="",
            font=('Segoe UI', 12, 'bold'),
            fg=COLORS['primary']
        )
        self.results_label.pack(side='left', padx=10)

        # مؤشر الأداء
        self.performance_label = create_simple_rtl_label(
            left_info,
            text="",
            font=('Segoe UI', 10, 'normal'),
            fg=COLORS['text_secondary']
        )
        self.performance_label.pack(side='left', padx=10)
        
        # جدول النتائج المحسن
        results_frame = create_simple_rtl_frame(main_frame)
        results_frame.pack(fill='both', expand=True, pady=(25, 0))

        # شريط عنوان النتائج
        results_header = create_simple_rtl_frame(results_frame)
        results_header.pack(fill='x', pady=(0, 15))

        # عنوان النتائج
        results_title = create_simple_rtl_label(
            results_header,
            text="📊 نتائج البحث في الموردين",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['primary']
        )
        results_title.pack(side='right')

        # مؤشر حالة البحث
        self.search_status = create_simple_rtl_label(
            results_header,
            text="🔍 جاري البحث...",
            font=('Segoe UI', 12, 'normal'),
            fg=COLORS['warning']
        )
        self.search_status.pack(side='left')

        # إنشاء جدول النتائج المحسن
        table_frame = create_simple_rtl_frame(results_frame)
        table_frame.pack(fill='both', expand=True)

        # تعريف أعمدة الجدول المحسنة
        columns = ['supplier_code', 'supplier_name', 'contact_person', 'phone', 'email', 'city', 'country', 'status']
        column_names = ['كود المورد', 'اسم المورد', 'الشخص المسؤول', 'الهاتف', 'البريد الإلكتروني', 'المدينة', 'البلد', 'الحالة']

        # إنشاء Treeview محسن
        self.results_tree = create_simple_rtl_treeview(
            table_frame,
            columns=columns,
            show='tree headings',
            height=18
        )

        # تكوين الأعمدة مع عروض محسنة
        widths = [100, 180, 140, 120, 160, 100, 100, 80]
        for i, (col_id, col_name, width) in enumerate(zip(columns, column_names, widths)):
            self.results_tree.heading(col_id, text=col_name, anchor='e')
            self.results_tree.column(col_id, width=width, anchor='e', minwidth=80)

        # العمود الرئيسي للترقيم
        self.results_tree.column('#0', width=40, minwidth=40, anchor='e')
        self.results_tree.heading('#0', text='#', anchor='e')
        
        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.results_tree.xview)
        self.results_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول
        self.results_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط أحداث الجدول
        self.results_tree.bind('<<TreeviewSelect>>', self.on_result_select)
        self.results_tree.bind('<Double-1>', self.on_result_double_click)
        
        # إعداد ألوان الجدول
        self.setup_tree_colors()
        
        # أزرار التحكم
        control_frame = create_simple_rtl_frame(main_frame)
        control_frame.pack(fill='x', pady=(20, 0))
        
        # زر الاختيار
        select_btn = create_simple_rtl_button(
            control_frame,
            text="✅ اختيار المورد",
            button_type="success",
            command=self.select_supplier
        )
        select_btn.pack(side='right', padx=5)
        
        # زر الإغلاق
        close_btn = create_simple_rtl_button(
            control_frame,
            text="❌ إغلاق",
            button_type="danger",
            command=self.close_dialog
        )
        close_btn.pack(side='right', padx=5)
        
        # معلومات المساعدة
        help_label = create_simple_rtl_label(
            control_frame,
            text="💡 نصيحة: انقر مرتين على المورد لاختياره مباشرة",
            font=('Segoe UI', 10, 'normal'),
            fg=COLORS['text_secondary']
        )
        help_label.pack(side='left', padx=10)

    def create_advanced_search_field(self, parent, label_text, var_name, placeholder, side='left', padx=0):
        """إنشاء حقل بحث متقدم"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side=side, fill='x', expand=True, padx=padx)

        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 11, 'bold'),
            fg=COLORS['text_primary']
        )
        label.pack(anchor='e', pady=(0, 5))

        self.search_vars[var_name] = tk.StringVar()
        entry = create_simple_rtl_entry(
            field_frame,
            textvariable=self.search_vars[var_name],
            placeholder=placeholder
        )
        entry.configure(font=('Segoe UI', 10, 'normal'))
        entry.pack(fill='x', ipady=6)
        self.search_vars[var_name].trace('w', self.on_search_change)

        return entry

    def create_advanced_search_combobox(self, parent, label_text, var_name, values, side='left', padx=0):
        """إنشاء قائمة منسدلة للبحث المتقدم"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side=side, fill='x', expand=True, padx=padx)

        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 11, 'bold'),
            fg=COLORS['text_primary']
        )
        label.pack(anchor='e', pady=(0, 5))

        self.search_vars[var_name] = tk.StringVar()
        combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.search_vars[var_name]
        )
        combo.configure(
            values=values,
            font=('Segoe UI', 10, 'normal'),
            state='readonly'
        )
        combo.pack(fill='x', ipady=6)
        combo.set(values[0] if values else "")
        self.search_vars[var_name].trace('w', self.on_search_change)

        return combo

    def setup_tree_colors(self):
        """إعداد ألوان الجدول المحسنة"""
        try:
            # ألوان الصفوف
            self.results_tree.tag_configure('odd_row', background='#F8FAFC', foreground='#1F2937')
            self.results_tree.tag_configure('even_row', background='#FFFFFF', foreground='#1F2937')

            # ألوان الحالة
            self.results_tree.tag_configure('active_supplier', background='#ECFDF5', foreground='#065F46')
            self.results_tree.tag_configure('inactive_supplier', background='#FEF2F2', foreground='#991B1B')

            # ألوان خاصة
            self.results_tree.tag_configure('local_supplier', background='#EFF6FF', foreground='#1E40AF')
            self.results_tree.tag_configure('international_supplier', background='#F0F9FF', foreground='#0369A1')

            # ألوان التمييز
            self.results_tree.tag_configure('selected', background='#DBEAFE', foreground='#1E40AF')
            self.results_tree.tag_configure('highlighted', background='#FEF3C7', foreground='#92400E')

        except Exception as e:
            print(f"خطأ في إعداد ألوان الجدول: {e}")
    
    def load_suppliers_from_database(self):
        """تحميل الموردين من قاعدة البيانات الحقيقية"""
        try:
            # تحديث مؤشر الحالة إذا كان متاحاً
            if hasattr(self, 'status_label'):
                self.status_label.configure(text="⏳ جاري تحميل البيانات...", fg=COLORS['warning'])
                self.root.update()

            # استعلام شامل للحصول على جميع بيانات الموردين
            query = """
                SELECT
                    supplier_code,
                    supplier_name,
                    contact_person,
                    phone,
                    mobile,
                    email,
                    address,
                    city,
                    country,
                    tax_number,
                    payment_terms,
                    credit_limit,
                    is_active,
                    notes,
                    created_at,
                    updated_at
                FROM suppliers
                WHERE supplier_code IS NOT NULL AND supplier_code != ''
                ORDER BY supplier_name
            """

            suppliers = self.db_manager.fetch_all(query)
            self.suppliers_data = []

            if suppliers:
                for supplier in suppliers:
                    try:
                        # معالجة البيانات وتنظيفها
                        supplier_data = {
                            'supplier_code': str(supplier.get('supplier_code', '') or ''),
                            'supplier_name': str(supplier.get('supplier_name', '') or ''),
                            'contact_person': str(supplier.get('contact_person', '') or 'غير محدد'),
                            'phone': str(supplier.get('phone', '') or supplier.get('mobile', '') or 'غير محدد'),
                            'email': str(supplier.get('email', '') or 'غير محدد'),
                            'address': str(supplier.get('address', '') or 'غير محدد'),
                            'city': str(supplier.get('city', '') or 'غير محدد'),
                            'country': str(supplier.get('country', '') or 'غير محدد'),
                            'tax_number': str(supplier.get('tax_number', '') or 'غير محدد'),
                            'payment_terms': str(supplier.get('payment_terms', '') or 'غير محدد'),
                            'credit_limit': float(supplier.get('credit_limit', 0) or 0),
                            'is_active': bool(supplier.get('is_active', True)),
                            'status': 'نشط' if supplier.get('is_active', True) else 'غير نشط',
                            'notes': str(supplier.get('notes', '') or ''),
                            'created_at': str(supplier.get('created_at', '') or ''),
                            'supplier_type': 'محلي' if str(supplier.get('country', '') or '').lower() in ['السعودية', 'saudi arabia'] else 'دولي'
                        }
                        self.suppliers_data.append(supplier_data)
                    except Exception as supplier_error:
                        print(f"خطأ في معالجة مورد: {supplier_error}")
                        continue

            # إضافة موردين تجريبيين إذا لم توجد بيانات
            if not self.suppliers_data:
                self.create_sample_suppliers()

            # تحديث القائمة المفلترة
            self.filtered_suppliers = self.suppliers_data.copy()
            self.update_results_tree()
            self.update_results_count()

            # تحديث حالة النظام
            if hasattr(self, 'status_label'):
                self.status_label.configure(
                    text=f"✅ تم تحميل {len(self.suppliers_data)} مورد بنجاح",
                    fg=COLORS['success']
                )

        except Exception as e:
            print(f"خطأ في تحميل الموردين من قاعدة البيانات: {e}")
            if hasattr(self, 'status_label'):
                self.status_label.configure(text="❌ خطأ في تحميل البيانات", fg=COLORS['danger'])
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")

            # تحميل بيانات تجريبية في حالة الخطأ
            self.create_sample_suppliers()
            self.filtered_suppliers = self.suppliers_data.copy()
            self.update_results_tree()
            self.update_results_count()

    def create_sample_suppliers(self):
        """إنشاء موردين تجريبيين"""
        self.suppliers_data = [
            {
                'supplier_code': 'SUP001',
                'supplier_name': 'شركة التجارة العالمية المحدودة',
                'contact_person': 'أحمد محمد العلي',
                'phone': '+966501234567',
                'email': '<EMAIL>',
                'address': 'شارع الملك فهد، الرياض',
                'city': 'الرياض',
                'country': 'السعودية',
                'tax_number': '300123456789003',
                'payment_terms': '30 يوم',
                'credit_limit': 100000.0,
                'is_active': True,
                'status': 'نشط',
                'notes': 'مورد موثوق للمواد الخام',
                'created_at': '2024-01-15',
                'supplier_type': 'محلي'
            },
            {
                'supplier_code': 'SUP002',
                'supplier_name': 'مؤسسة الشحن السريع',
                'contact_person': 'سارة أحمد خالد',
                'phone': '+971501234567',
                'email': '<EMAIL>',
                'address': 'دبي مارينا، دبي',
                'city': 'دبي',
                'country': 'الإمارات',
                'tax_number': 'TRN123456789',
                'payment_terms': '15 يوم',
                'credit_limit': 75000.0,
                'is_active': True,
                'status': 'نشط',
                'notes': 'خدمات شحن ممتازة',
                'created_at': '2024-02-10',
                'supplier_type': 'دولي'
            },
            {
                'supplier_code': 'SUP003',
                'supplier_name': 'شركة اللوجستيات المتقدمة',
                'contact_person': 'محمد علي حسن',
                'phone': '+20101234567',
                'email': '<EMAIL>',
                'address': 'مدينة نصر، القاهرة',
                'city': 'القاهرة',
                'country': 'مصر',
                'tax_number': 'EG123456789',
                'payment_terms': '45 يوم',
                'credit_limit': 50000.0,
                'is_active': True,
                'status': 'نشط',
                'notes': 'حلول لوجستية متكاملة',
                'created_at': '2024-03-05',
                'supplier_type': 'دولي'
            }
        ]
    
    def on_search_change(self, *args):
        """عند تغيير نص البحث - مع تأخير للبحث الفوري"""
        if hasattr(self, '_search_timer'):
            self.root.after_cancel(self._search_timer)

        # تحديث مؤشر الحالة
        self.search_status.configure(text="🔍 جاري البحث...", fg=COLORS['warning'])
        self._search_timer = self.root.after(300, self.perform_search)

    def perform_search(self):
        """تنفيذ البحث المتقدم والفوري"""
        try:
            import time
            start_time = time.time()

            # الحصول على جميع معايير البحث
            search_criteria = {}
            placeholder_texts = [
                'كود المورد...', 'اسم المورد...', 'اسم الشخص المسؤول...',
                'رقم الهاتف...', 'البريد الإلكتروني...', 'المدينة...'
            ]

            for key, var in self.search_vars.items():
                value = var.get().strip()
                # تجاهل النصوص الفارغة ونصوص placeholder والقيم الافتراضية
                if (value and
                    value != "الكل" and
                    value not in placeholder_texts and
                    not value.endswith('...')):
                    search_criteria[key] = value.lower()

            # إذا لم توجد معايير بحث، عرض جميع الموردين
            if not search_criteria:
                self.filtered_suppliers = self.suppliers_data.copy()
            else:
                self.filtered_suppliers = []
                for supplier in self.suppliers_data:
                    match = True

                    # البحث العام (في جميع الحقول)
                    if 'general' in search_criteria:
                        general_text = search_criteria['general']
                        supplier_text = ' '.join([
                            str(supplier.get('supplier_code', '')),
                            str(supplier.get('supplier_name', '')),
                            str(supplier.get('contact_person', '')),
                            str(supplier.get('phone', '')),
                            str(supplier.get('email', '')),
                            str(supplier.get('address', '')),
                            str(supplier.get('city', '')),
                            str(supplier.get('country', '')),
                            str(supplier.get('notes', ''))
                        ]).lower()

                        if general_text not in supplier_text:
                            match = False

                    # البحث في كود المورد
                    if match and 'supplier_code' in search_criteria:
                        supplier_code = str(supplier.get('supplier_code', '')).lower()
                        if search_criteria['supplier_code'] not in supplier_code:
                            match = False

                    # البحث في اسم المورد
                    if match and 'supplier_name' in search_criteria:
                        supplier_name = str(supplier.get('supplier_name', '')).lower()
                        if search_criteria['supplier_name'] not in supplier_name:
                            match = False

                    # البحث في الشخص المسؤول
                    if match and 'contact_person' in search_criteria:
                        contact_person = str(supplier.get('contact_person', '')).lower()
                        if search_criteria['contact_person'] not in contact_person:
                            match = False

                    # البحث في الهاتف
                    if match and 'phone' in search_criteria:
                        phone = str(supplier.get('phone', '')).lower()
                        if search_criteria['phone'] not in phone:
                            match = False

                    # البحث في البريد الإلكتروني
                    if match and 'email' in search_criteria:
                        email = str(supplier.get('email', '')).lower()
                        if search_criteria['email'] not in email:
                            match = False

                    # البحث في المدينة
                    if match and 'city' in search_criteria:
                        city = str(supplier.get('city', '')).lower()
                        if search_criteria['city'] not in city:
                            match = False

                    # البحث في البلد
                    if match and 'country' in search_criteria:
                        country = str(supplier.get('country', '')).lower()
                        if search_criteria['country'] not in country:
                            match = False

                    # فلتر حالة المورد
                    if match and 'status' in search_criteria:
                        status_filter = search_criteria['status']
                        supplier_status = 'نشط' if supplier.get('is_active', True) else 'غير نشط'
                        if status_filter != supplier_status.lower():
                            match = False

                    # فلتر نوع المورد
                    if match and 'supplier_type' in search_criteria:
                        type_filter = search_criteria['supplier_type']
                        supplier_type = supplier.get('supplier_type', '').lower()
                        if type_filter != supplier_type:
                            match = False

                    if match:
                        self.filtered_suppliers.append(supplier)

            # تحديث النتائج
            self.update_results_tree()
            self.update_results_count()

            # حساب وقت البحث
            end_time = time.time()
            search_time = round((end_time - start_time) * 1000, 1)

            # تحديث مؤشرات الأداء
            if hasattr(self, 'performance_label'):
                self.performance_label.configure(text=f"⚡ وقت البحث: {search_time} مللي ثانية")
            if hasattr(self, 'search_status'):
                self.search_status.configure(text="✅ تم البحث بنجاح", fg=COLORS['success'])

        except Exception as e:
            print(f"خطأ في البحث: {e}")
            self.search_status.configure(text="❌ خطأ في البحث", fg=COLORS['danger'])
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")
    
    def clear_search(self):
        """مسح البحث"""
        for var in self.search_vars.values():
            var.set('')
        self.perform_search()
        self.focus_search_field()
    
    def update_results_tree(self):
        """تحديث جدول النتائج"""
        # مسح البيانات الحالية
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # إضافة النتائج
        for i, supplier_data in enumerate(self.filtered_suppliers):
            self.add_result_to_tree(supplier_data, i + 1)
    
    def add_result_to_tree(self, supplier_data, index):
        """إضافة نتيجة للجدول مع البيانات المحسنة"""
        try:
            # تحضير البيانات للعرض
            values = [
                str(supplier_data.get('supplier_code', '')),
                str(supplier_data.get('supplier_name', '')),
                str(supplier_data.get('contact_person', '')),
                str(supplier_data.get('phone', '')),
                str(supplier_data.get('email', '')),
                str(supplier_data.get('city', '')),
                str(supplier_data.get('country', '')),
                str(supplier_data.get('status', ''))
            ]

            # تحديد ألوان الصف بناءً على الحالة والنوع
            tags = []

            # لون الصف الأساسي
            if index % 2 == 1:
                tags.append('odd_row')
            else:
                tags.append('even_row')

            # ألوان خاصة بالحالة
            if supplier_data.get('is_active', True):
                tags.append('active_supplier')
            else:
                tags.append('inactive_supplier')

            # ألوان خاصة بالنوع
            if supplier_data.get('supplier_type', '').lower() == 'محلي':
                tags.append('local_supplier')
            else:
                tags.append('international_supplier')

            # إدراج العنصر مع البيانات المحسنة
            item = self.results_tree.insert(
                '', 'end',
                text=str(index),
                values=values,
                tags=tags
            )

        except Exception as e:
            print(f"خطأ في إضافة نتيجة للجدول: {e}")
    
    def update_results_count(self):
        """تحديث عداد النتائج مع إحصائيات مفصلة"""
        total_suppliers = len(self.suppliers_data)
        filtered_suppliers = len(self.filtered_suppliers)

        # حساب إحصائيات إضافية
        active_count = sum(1 for s in self.filtered_suppliers if s.get('is_active', True))
        local_count = sum(1 for s in self.filtered_suppliers if s.get('supplier_type', '').lower() == 'محلي')

        if filtered_suppliers == total_suppliers:
            self.results_label.configure(
                text=f"📊 إجمالي الموردين: {total_suppliers} | نشط: {active_count} | محلي: {local_count}"
            )
        else:
            percentage = round((filtered_suppliers / total_suppliers) * 100, 1) if total_suppliers > 0 else 0
            self.results_label.configure(
                text=f"📊 النتائج: {filtered_suppliers} من {total_suppliers} ({percentage}%) | نشط: {active_count} | محلي: {local_count}"
            )

    def refresh_data(self):
        """تحديث البيانات من قاعدة البيانات"""
        self.load_suppliers_from_database()
        messagebox.showinfo("تحديث", "تم تحديث بيانات الموردين بنجاح!")

    def toggle_advanced_search(self):
        """تبديل عرض البحث المتقدم"""
        # هذه الوظيفة يمكن توسيعها لإخفاء/إظهار حقول البحث المتقدم
        messagebox.showinfo("البحث المتقدم", "جميع حقول البحث المتقدم متاحة حالياً!")

    def on_result_select(self, event):
        """عند اختيار نتيجة"""
        selection = self.results_tree.selection()
        if selection:
            item = selection[0]
            values = self.results_tree.item(item, 'values')

            if values and len(values) > 0:
                # البحث عن بيانات المورد الكاملة باستخدام كود المورد
                supplier_code = str(values[0])  # كود المورد في العمود الأول
                for supplier_data in self.filtered_suppliers:
                    if str(supplier_data.get('supplier_code', '')) == supplier_code:
                        self.selected_supplier = supplier_data
                        break
    
    def on_result_double_click(self, event):
        """عند النقر المزدوج على نتيجة"""
        self.select_supplier()
    
    def select_supplier(self):
        """اختيار المورد وإرجاع البيانات"""
        if not self.selected_supplier:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد من النتائج")
            return

        # إغلاق النافذة - البيانات ستُعالج في النموذج الرئيسي
        self.close_dialog()
    
    def close_dialog(self):
        """إغلاق النافذة"""
        # حفظ البيانات المختارة قبل الإغلاق
        if hasattr(self, 'selected_supplier') and self.selected_supplier:
            # إنشاء نسخة من البيانات لتجنب فقدانها عند تدمير النافذة
            self.final_selected_supplier = self.selected_supplier.copy()
        else:
            self.final_selected_supplier = None

        self.root.destroy()
    
    def focus_search_field(self):
        """تركيز على حقل البحث"""
        self.general_search_entry.focus_set()

    def toggle_advanced_search(self):
        """إظهار/إخفاء البحث المتقدم"""
        if self.advanced_search_visible:
            # إخفاء البحث المتقدم
            self.advanced_search_frame.pack_forget()
            self.advanced_search_button.configure(
                text="🔧 البحث المتقدم",
                bg=COLORS['primary']
            )
            self.advanced_search_visible = False
        else:
            # إظهار البحث المتقدم
            self.advanced_search_frame.pack(fill='x', pady=(10, 0))
            self.advanced_search_button.configure(
                text="🔼 إخفاء البحث المتقدم",
                bg=COLORS['secondary']
            )
            self.advanced_search_visible = True
    
    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

if __name__ == "__main__":
    # اختبار النافذة
    root = tk.Tk()
    root.withdraw()
    
    dialog = SupplierSearchDialog(root, None)
    root.mainloop()
