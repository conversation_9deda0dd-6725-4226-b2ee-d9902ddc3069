#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شاشة البحث في أكواد الأصناف المحسنة
"""

import sys
import os
import tkinter as tk

# إضافة المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_item_code_search():
    """اختبار شاشة البحث في أكواد الأصناف"""
    print("🔍 اختبار شاشة البحث في أكواد الأصناف المحسنة")
    
    # إنشاء نافذة رئيسية
    root = tk.Tk()
    root.title("اختبار البحث في أكواد الأصناف")
    root.geometry("400x300")
    
    def open_item_code_search():
        """فتح شاشة البحث في أكواد الأصناف"""
        try:
            from src.item_code_search_dialog import ItemCodeSearchDialog
            
            # إنشاء نافذة وهمية للـ item_dialog
            class MockItemDialog:
                def __init__(self):
                    self.form_vars = {
                        'item_code': tk.StringVar(),
                        'item_name': tk.StringVar(),
                        'description': tk.StringVar(),
                        'unit': tk.StringVar(),
                        'unit_price': tk.StringVar(),
                        'quantity': tk.StringVar(),
                        'total_price': tk.StringVar(),
                        'origin_country': tk.StringVar(),
                        'hs_code': tk.StringVar()
                    }
            
            mock_item_dialog = MockItemDialog()
            
            # إنشاء شاشة البحث
            search_dialog = ItemCodeSearchDialog(root, mock_item_dialog)
            
        except Exception as e:
            print(f"❌ خطأ: {e}")
            import traceback
            traceback.print_exc()
    
    # واجهة الاختبار
    title_label = tk.Label(
        root,
        text="🔍 اختبار البحث في أكواد الأصناف",
        font=('Arial', 16, 'bold'),
        pady=20
    )
    title_label.pack()
    
    instructions = tk.Label(
        root,
        text="التحسينات المطبقة:\n" +
             "✅ حقول البحث المتقدم مخفية\n" +
             "✅ زر البحث المتقدم - F3\n" +
             "✅ مساحة أكبر للنتائج\n" +
             "✅ واجهة أنظف وأقل ازدحاماً",
        font=('Arial', 10),
        justify='center',
        pady=10
    )
    instructions.pack()
    
    # زر فتح البحث
    search_btn = tk.Button(
        root,
        text="🔍 فتح البحث في أكواد الأصناف",
        command=open_item_code_search,
        font=('Arial', 12),
        bg='#28a745',
        fg='white',
        padx=20,
        pady=10
    )
    search_btn.pack(pady=20)
    
    # تعليمات الاستخدام
    usage_label = tk.Label(
        root,
        text="كيفية الاستخدام:\n" +
             "• اضغط الزر أعلاه لفتح شاشة البحث\n" +
             "• اضغط F3 لإظهار/إخفاء البحث المتقدم\n" +
             "• جرب البحث في الحقل العام\n" +
             "• جرب البحث المتقدم بالحقول المخصصة",
        font=('Arial', 9),
        justify='center',
        pady=10,
        fg='#666'
    )
    usage_label.pack()
    
    root.mainloop()

if __name__ == "__main__":
    test_item_code_search()
