#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار بسيط لوظيفة F9
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_f9_methods():
    """اختبار وجود دوال F9"""
    print("=" * 50)
    print("🧪 اختبار وجود دوال F9")
    print("=" * 50)
    
    try:
        # استيراد الكلاس
        from src.fullscreen_shipment_form import FullscreenShipmentForm
        
        # التحقق من وجود الدوال
        methods_to_check = [
            'handle_f9_key',
            'show_supplier_search', 
            'show_item_search',
            'show_customer_search',
            'show_warehouse_search'
        ]
        
        print("🔍 التحقق من وجود الدوال...")
        
        for method_name in methods_to_check:
            if hasattr(FullscreenShipmentForm, method_name):
                print(f"✅ {method_name} موجودة")
            else:
                print(f"❌ {method_name} مفقودة")
        
        print("\n🎉 تم الانتهاء من الفحص!")
        print("💡 الإصلاحات المطبقة:")
        print("   ✅ نقل show_supplier_search إلى مكان أقرب في الكلاس")
        print("   ✅ حذف الدالة المكررة")
        print("   ✅ إزالة التحقق المكرر من الأقسام")
        
        print("\n🚀 يمكنك الآن تشغيل التطبيق:")
        print("   1. في القسم الأساسي، اضغط F9 على حقل المورد")
        print("   2. يجب أن تفتح نافذة البحث في الموردين")
        print("   3. في قسم الأصناف، اضغط F9")
        print("   4. يجب أن تفتح نافذة البحث في الأصناف")
        print("   5. لن تظهر رسائل خطأ مكررة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    test_f9_methods()
