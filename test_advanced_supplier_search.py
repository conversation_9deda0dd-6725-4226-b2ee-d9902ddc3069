#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام البحث المتقدم في الموردين
Comprehensive Testing for Advanced Supplier Search System
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
import time
import threading

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from src.supplier_search_dialog import SupplierSearchDialog
from database.database_manager import DatabaseManager

class AdvancedSupplierSearchTester:
    """فئة اختبار البحث المتقدم في الموردين"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.test_results = []
        self.setup_test_environment()
    
    def setup_test_environment(self):
        """إعداد بيئة الاختبار"""
        print("🔧 إعداد بيئة الاختبار...")

        # قاعدة البيانات والجداول تم إنشاؤها تلقائياً في __init__
        # إدراج بيانات اختبار
        self.insert_test_data()

        print("✅ تم إعداد بيئة الاختبار بنجاح")
    
    def insert_test_data(self):
        """إدراج بيانات اختبار متنوعة"""
        test_suppliers = [
            {
                'supplier_code': 'TEST001',
                'supplier_name': 'شركة الاختبار التجارية',
                'contact_person': 'أحمد محمد',
                'phone': '+966501111111',
                'email': '<EMAIL>',
                'city': 'الرياض',
                'country': 'السعودية',
                'is_active': 1
            },
            {
                'supplier_code': 'TEST002',
                'supplier_name': 'مؤسسة التطوير الحديثة',
                'contact_person': 'فاطمة علي',
                'phone': '+971502222222',
                'email': '<EMAIL>',
                'city': 'دبي',
                'country': 'الإمارات',
                'is_active': 1
            },
            {
                'supplier_code': 'TEST003',
                'supplier_name': 'شركة التكنولوجيا المتقدمة',
                'contact_person': 'محمد خالد',
                'phone': '+20103333333',
                'email': '<EMAIL>',
                'city': 'القاهرة',
                'country': 'مصر',
                'is_active': 0
            },
            {
                'supplier_code': 'COMP001',
                'supplier_name': 'Computer Systems Ltd',
                'contact_person': 'John Smith',
                'phone': '+1234567890',
                'email': '<EMAIL>',
                'city': 'New York',
                'country': 'أمريكا',
                'is_active': 1
            },
            {
                'supplier_code': 'TECH001',
                'supplier_name': 'شركة التقنيات الذكية',
                'contact_person': 'سارة أحمد',
                'phone': '+966504444444',
                'email': '<EMAIL>',
                'city': 'جدة',
                'country': 'السعودية',
                'is_active': 1
            }
        ]
        
        for supplier in test_suppliers:
            try:
                query = """
                    INSERT OR REPLACE INTO suppliers 
                    (supplier_code, supplier_name, contact_person, phone, email, city, country, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                self.db_manager.execute_query(
                    query,
                    (supplier['supplier_code'], supplier['supplier_name'], 
                     supplier['contact_person'], supplier['phone'], supplier['email'],
                     supplier['city'], supplier['country'], supplier['is_active'])
                )
            except Exception as e:
                print(f"خطأ في إدراج بيانات الاختبار: {e}")
    
    def run_automated_tests(self):
        """تشغيل الاختبارات الآلية"""
        print("\n🧪 بدء الاختبارات الآلية...")
        
        tests = [
            ("اختبار الاتصال بقاعدة البيانات", self.test_database_connection),
            ("اختبار تحميل البيانات", self.test_data_loading),
            ("اختبار البحث العام", self.test_general_search),
            ("اختبار البحث في كود المورد", self.test_supplier_code_search),
            ("اختبار البحث في اسم المورد", self.test_supplier_name_search),
            ("اختبار البحث في الشخص المسؤول", self.test_contact_person_search),
            ("اختبار البحث في الهاتف", self.test_phone_search),
            ("اختبار البحث في البريد الإلكتروني", self.test_email_search),
            ("اختبار فلتر البلد", self.test_country_filter),
            ("اختبار فلتر الحالة", self.test_status_filter),
            ("اختبار الأداء", self.test_performance)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                print(f"\n🔍 {test_name}...")
                result = test_func()
                if result:
                    print(f"✅ {test_name}: نجح")
                    passed += 1
                else:
                    print(f"❌ {test_name}: فشل")
                    failed += 1
                self.test_results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name}: خطأ - {str(e)}")
                failed += 1
                self.test_results.append((test_name, False))
        
        print(f"\n📊 نتائج الاختبارات:")
        print(f"✅ نجح: {passed}")
        print(f"❌ فشل: {failed}")
        print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
        
        return passed, failed
    
    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            suppliers = self.db_manager.fetch_all("SELECT COUNT(*) as count FROM suppliers")
            return suppliers is not None and len(suppliers) > 0
        except:
            return False
    
    def test_data_loading(self):
        """اختبار تحميل البيانات"""
        try:
            suppliers = self.db_manager.fetch_all("SELECT * FROM suppliers LIMIT 5")
            return suppliers is not None and len(suppliers) > 0
        except:
            return False
    
    def test_general_search(self):
        """اختبار البحث العام"""
        try:
            # البحث عن "شركة"
            query = """
                SELECT * FROM suppliers 
                WHERE LOWER(supplier_name) LIKE '%شركة%' 
                   OR LOWER(contact_person) LIKE '%شركة%'
            """
            results = self.db_manager.fetch_all(query)
            return results is not None and len(results) > 0
        except:
            return False
    
    def test_supplier_code_search(self):
        """اختبار البحث في كود المورد"""
        try:
            results = self.db_manager.fetch_all(
                "SELECT * FROM suppliers WHERE LOWER(supplier_code) LIKE '%test%'"
            )
            return results is not None and len(results) > 0
        except:
            return False
    
    def test_supplier_name_search(self):
        """اختبار البحث في اسم المورد"""
        try:
            results = self.db_manager.fetch_all(
                "SELECT * FROM suppliers WHERE LOWER(supplier_name) LIKE '%اختبار%'"
            )
            return results is not None and len(results) > 0
        except:
            return False
    
    def test_contact_person_search(self):
        """اختبار البحث في الشخص المسؤول"""
        try:
            results = self.db_manager.fetch_all(
                "SELECT * FROM suppliers WHERE LOWER(contact_person) LIKE '%أحمد%'"
            )
            return results is not None and len(results) > 0
        except:
            return False
    
    def test_phone_search(self):
        """اختبار البحث في الهاتف"""
        try:
            results = self.db_manager.fetch_all(
                "SELECT * FROM suppliers WHERE phone LIKE '%966%'"
            )
            return results is not None and len(results) > 0
        except:
            return False
    
    def test_email_search(self):
        """اختبار البحث في البريد الإلكتروني"""
        try:
            results = self.db_manager.fetch_all(
                "SELECT * FROM suppliers WHERE email LIKE '%example%'"
            )
            return results is not None and len(results) > 0
        except:
            return False
    
    def test_country_filter(self):
        """اختبار فلتر البلد"""
        try:
            results = self.db_manager.fetch_all(
                "SELECT * FROM suppliers WHERE country = 'السعودية'"
            )
            return results is not None and len(results) > 0
        except:
            return False
    
    def test_status_filter(self):
        """اختبار فلتر الحالة"""
        try:
            active_results = self.db_manager.fetch_all(
                "SELECT * FROM suppliers WHERE is_active = 1"
            )
            inactive_results = self.db_manager.fetch_all(
                "SELECT * FROM suppliers WHERE is_active = 0"
            )
            return (active_results is not None and len(active_results) > 0 and
                    inactive_results is not None and len(inactive_results) > 0)
        except:
            return False
    
    def test_performance(self):
        """اختبار الأداء"""
        try:
            start_time = time.time()
            
            # تشغيل عدة استعلامات
            for _ in range(10):
                self.db_manager.fetch_all("SELECT * FROM suppliers WHERE is_active = 1")
            
            end_time = time.time()
            total_time = (end_time - start_time) * 1000  # بالمللي ثانية
            
            print(f"⚡ وقت تنفيذ 10 استعلامات: {total_time:.1f} مللي ثانية")
            return total_time < 1000  # أقل من ثانية واحدة
        except:
            return False
    
    def run_interactive_test(self):
        """تشغيل الاختبار التفاعلي"""
        print("\n🎮 بدء الاختبار التفاعلي...")
        
        root = tk.Tk()
        root.title("اختبار البحث المتقدم في الموردين")
        root.geometry("400x300")
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        try:
            # إنشاء نافذة البحث
            dialog = SupplierSearchDialog(root, None)
            
            # انتظار إغلاق النافذة
            root.wait_window(dialog.root)
            
            print("✅ تم إنهاء الاختبار التفاعلي")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار التفاعلي: {e}")
            return False
        finally:
            root.destroy()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار نظام البحث المتقدم في الموردين")
    print("=" * 60)
    
    tester = AdvancedSupplierSearchTester()
    
    # تشغيل الاختبارات الآلية
    passed, failed = tester.run_automated_tests()
    
    if failed == 0:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        
        # عرض خيار الاختبار التفاعلي
        response = input("\n❓ هل تريد تشغيل الاختبار التفاعلي؟ (y/n): ")
        if response.lower() in ['y', 'yes', 'نعم']:
            tester.run_interactive_test()
    else:
        print(f"\n⚠️ فشل {failed} اختبار. يرجى مراجعة الأخطاء.")
    
    print("\n✅ انتهى الاختبار")

if __name__ == "__main__":
    main()
