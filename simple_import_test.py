#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لاستيراد نافذة الأصناف
Simple Items Window Import Test
"""

import sys
import os
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """اختبار الاستيرادات الأساسية"""
    print("🧪 اختبار الاستيرادات الأساسية...")
    
    try:
        # اختبار tkinter
        import tkinter as tk
        print("✅ tkinter")
        
        # اختبار pandas
        import pandas as pd
        print("✅ pandas")
        
        # اختبار openpyxl
        import openpyxl
        print("✅ openpyxl")
        
        # اختبار config
        from config.config import COLORS, FONTS
        print("✅ config")
        print(f"  COLORS keys: {list(COLORS.keys())[:5]}...")
        print(f"  FONTS keys: {list(FONTS.keys())}")
        
        # اختبار database
        from database.database_manager import DatabaseManager
        print("✅ database_manager")
        
        # اختبار auth
        from src.auth_manager import auth_manager
        print("✅ auth_manager")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيرادات: {e}")
        print(f"📋 تفاصيل: {traceback.format_exc()}")
        return False

def test_items_window():
    """اختبار نافذة الأصناف"""
    print("\n🧪 اختبار نافذة الأصناف...")
    
    try:
        from src.items_window import ItemsWindow
        print("✅ تم استيراد ItemsWindow")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد ItemsWindow: {e}")
        print(f"📋 تفاصيل: {traceback.format_exc()}")
        return False

def test_excel_functions():
    """اختبار وظائف Excel"""
    print("\n🧪 اختبار وظائف Excel...")
    
    try:
        import pandas as pd
        
        # إنشاء بيانات تجريبية
        data = {
            'item_code': ['TEST001'],
            'item_name': ['صنف تجريبي'],
            'unit_of_measure': ['قطعة']
        }
        
        df = pd.DataFrame(data)
        print("✅ إنشاء DataFrame")
        
        # حفظ كملف Excel
        test_file = 'temp_test.xlsx'
        df.to_excel(test_file, index=False)
        print("✅ حفظ ملف Excel")
        
        # قراءة الملف
        df_read = pd.read_excel(test_file)
        print("✅ قراءة ملف Excel")
        
        # حذف الملف المؤقت
        os.remove(test_file)
        print("✅ حذف الملف المؤقت")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في وظائف Excel: {e}")
        print(f"📋 تفاصيل: {traceback.format_exc()}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار بسيط لميزة استيراد Excel")
    print("=" * 40)
    
    # اختبار الاستيرادات
    imports_ok = test_basic_imports()
    
    # اختبار نافذة الأصناف
    items_ok = test_items_window()
    
    # اختبار وظائف Excel
    excel_ok = test_excel_functions()
    
    print("\n" + "=" * 40)
    print("📋 النتائج:")
    print(f"{'✅' if imports_ok else '❌'} الاستيرادات الأساسية")
    print(f"{'✅' if items_ok else '❌'} نافذة الأصناف")
    print(f"{'✅' if excel_ok else '❌'} وظائف Excel")
    
    if all([imports_ok, items_ok, excel_ok]):
        print("\n🎉 جميع الاختبارات نجحت!")
        print("💡 يمكنك الآن استخدام ميزة استيراد Excel")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى إصلاح")

if __name__ == "__main__":
    main()
