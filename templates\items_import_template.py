#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء قالب Excel لاستيراد الأصناف
Create Excel Template for Items Import
"""

import pandas as pd
import os

def create_items_import_template():
    """إنشاء قالب Excel لاستيراد الأصناف"""
    
    # البيانات النموذجية
    sample_data = {
        'item_code': [
            'ITM001',
            'ITM002', 
            'ITM003',
            'ITM004',
            'ITM005'
        ],
        'item_name': [
            'لابتوب ديل',
            'ماوس لاسلكي',
            'لوحة مفاتيح عربية',
            'شاشة LED 24 بوصة',
            'طابعة ليزر'
        ],
        'unit_of_measure': [
            'قطعة',
            'قطعة',
            'قطعة', 
            'قطعة',
            'قطعة'
        ],
        'category_name': [
            'أجهزة كمبيوتر',
            'ملحقات كمبيوتر',
            'ملحقات كمبيوتر',
            'شاشات',
            'طابعات'
        ],
        'cost_price': [
            2500.00,
            45.00,
            120.00,
            800.00,
            650.00
        ],
        'selling_price': [
            3000.00,
            65.00,
            150.00,
            950.00,
            750.00
        ],
        'current_stock': [
            10,
            25,
            15,
            8,
            5
        ],
        'min_stock_level': [
            5,
            10,
            5,
            3,
            2
        ],
        'description': [
            'لابتوب ديل انسبايرون 15 - معالج i5 - ذاكرة 8GB',
            'ماوس لاسلكي بتقنية البلوتوث',
            'لوحة مفاتيح عربية/إنجليزية',
            'شاشة LED عالية الدقة 24 بوصة',
            'طابعة ليزر أبيض وأسود'
        ]
    }
    
    # إنشاء DataFrame
    df = pd.DataFrame(sample_data)
    
    # إنشاء مجلد القوالب إذا لم يكن موجوداً
    templates_dir = 'templates'
    if not os.path.exists(templates_dir):
        os.makedirs(templates_dir)
    
    # حفظ الملف
    template_path = os.path.join(templates_dir, 'قالب_استيراد_الأصناف.xlsx')
    
    with pd.ExcelWriter(template_path, engine='openpyxl') as writer:
        # ورقة البيانات النموذجية
        df.to_excel(writer, sheet_name='البيانات النموذجية', index=False)
        
        # ورقة فارغة للاستخدام
        empty_df = pd.DataFrame(columns=df.columns)
        empty_df.to_excel(writer, sheet_name='قالب فارغ', index=False)
        
        # ورقة التعليمات
        instructions = pd.DataFrame({
            'العمود': [
                'item_code',
                'item_name', 
                'unit_of_measure',
                'category_name',
                'cost_price',
                'selling_price',
                'current_stock',
                'min_stock_level',
                'description'
            ],
            'الوصف': [
                'كود الصنف (مطلوب - يجب أن يكون فريد)',
                'اسم الصنف (مطلوب)',
                'وحدة القياس (مطلوب - مثل: قطعة، كيلو، متر)',
                'اسم الفئة (اختياري - سيتم إنشاؤها إذا لم تكن موجودة)',
                'سعر التكلفة (اختياري - رقم)',
                'سعر البيع (اختياري - رقم)',
                'المخزون الحالي (اختياري - رقم)',
                'الحد الأدنى للمخزون (اختياري - رقم)',
                'وصف الصنف (اختياري)'
            ],
            'مطلوب': [
                'نعم',
                'نعم',
                'نعم',
                'لا',
                'لا',
                'لا', 
                'لا',
                'لا',
                'لا'
            ]
        })
        instructions.to_excel(writer, sheet_name='التعليمات', index=False)
        
        # تنسيق الأوراق
        for sheet_name in writer.sheets:
            worksheet = writer.sheets[sheet_name]
            
            # تعديل عرض الأعمدة
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
    
    print(f"✅ تم إنشاء قالب استيراد الأصناف: {template_path}")
    return template_path

if __name__ == "__main__":
    create_items_import_template()
