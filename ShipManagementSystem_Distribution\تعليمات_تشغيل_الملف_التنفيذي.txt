========================================
تعليمات تشغيل نظام متابعة الشحنات
Ship Management System - Executable Instructions
========================================

🎯 نظرة عامة / Overview:
هذا الملف التنفيذي (.exe) يحتوي على نظام متابعة شحنات الموردين كاملاً
ولا يحتاج إلى تثبيت Python أو أي مكتبات إضافية.

This executable (.exe) contains the complete Ship Management System
and does not require Python installation or additional libraries.

========================================
📋 متطلبات النظام / System Requirements:
========================================

✅ نظام التشغيل: Windows 10 أو أحدث
   Operating System: Windows 10 or newer

✅ الذاكرة: 4 GB RAM كحد أدنى (8 GB مُوصى به)
   Memory: 4 GB RAM minimum (8 GB recommended)

✅ مساحة القرص: 200 MB مساحة فارغة
   Disk Space: 200 MB free space

✅ دقة الشاشة: 1024x768 كحد أدنى (1920x1080 مُوصى به)
   Screen Resolution: 1024x768 minimum (1920x1080 recommended)

========================================
🚀 طريقة التشغيل / How to Run:
========================================

1️⃣ تأكد من وجود جميع الملفات في نفس المجلد:
   Make sure all files are in the same folder:
   
   📁 ShipManagementSystem.exe (الملف الرئيسي)
   📁 database/ (مجلد قاعدة البيانات)
   📁 docs/ (مجلد الوثائق)
   📄 README.md
   📄 دليل_المستخدم.md
   📄 requirements.txt

2️⃣ انقر نقراً مزدوجاً على ShipManagementSystem.exe
   Double-click on ShipManagementSystem.exe

3️⃣ انتظر تحميل البرنامج (قد يستغرق 10-30 ثانية في المرة الأولى)
   Wait for the program to load (may take 10-30 seconds on first run)

4️⃣ ستظهر شاشة تسجيل الدخول
   Login screen will appear

========================================
🔐 بيانات تسجيل الدخول الافتراضية / Default Login:
========================================

👤 اسم المستخدم / Username: admin
🔑 كلمة المرور / Password: admin

⚠️ تحذير: يُنصح بتغيير كلمة المرور بعد أول تسجيل دخول
   Warning: It's recommended to change the password after first login

========================================
🛠️ حل المشاكل الشائعة / Troubleshooting:
========================================

❌ المشكلة: البرنامج لا يبدأ
   Problem: Program doesn't start
   
   ✅ الحل / Solution:
   - تأكد من تشغيل البرنامج كمدير (Run as Administrator)
   - تأكد من عدم حجب البرنامج بواسطة مكافح الفيروسات
   - Check antivirus isn't blocking the program

❌ المشكلة: رسالة خطأ "ملف قاعدة البيانات غير موجود"
   Problem: "Database file not found" error
   
   ✅ الحل / Solution:
   - تأكد من وجود مجلد database في نفس مكان الملف التنفيذي
   - Make sure database folder is in the same location as the executable

❌ المشكلة: البرنامج بطيء في التشغيل
   Problem: Program is slow to start
   
   ✅ الحل / Solution:
   - هذا طبيعي في المرة الأولى، سيصبح أسرع في المرات التالية
   - This is normal on first run, will be faster on subsequent runs
   - أضف البرنامج إلى استثناءات مكافح الفيروسات
   - Add program to antivirus exceptions

❌ المشكلة: خطأ في الصلاحيات
   Problem: Permission error
   
   ✅ الحل / Solution:
   - انقر بالزر الأيمن على الملف التنفيذي واختر "تشغيل كمدير"
   - Right-click on executable and select "Run as Administrator"

========================================
📁 هيكل الملفات / File Structure:
========================================

📦 ShipManagementSystem_Distribution/
├── 🚀 ShipManagementSystem.exe (الملف الرئيسي)
├── 📁 database/
│   ├── shipment_system.db (قاعدة البيانات الرئيسية)
│   └── database_manager.py (مدير قاعدة البيانات)
├── 📁 docs/ (الوثائق والمساعدة)
├── 📄 README.md (معلومات المشروع)
├── 📄 دليل_المستخدم.md (دليل المستخدم)
├── 📄 requirements.txt (قائمة المتطلبات)
└── 📄 تعليمات_تشغيل_الملف_التنفيذي.txt (هذا الملف)

========================================
🔄 النسخ الاحتياطي / Backup:
========================================

⚠️ مهم جداً: قم بعمل نسخة احتياطية من مجلد database بانتظام
   Very Important: Regularly backup the database folder

📅 يُنصح بعمل نسخة احتياطية يومياً أو أسبوعياً
   Recommended to backup daily or weekly

📂 انسخ مجلد database كاملاً إلى مكان آمن
   Copy the entire database folder to a safe location

========================================
📞 الدعم الفني / Technical Support:
========================================

📧 للدعم الفني أو الاستفسارات:
   For technical support or inquiries:
   
   - راجع دليل المستخدم (دليل_المستخدم.md)
   - Check user manual (دليل_المستخدم.md)
   
   - راجع ملفات الوثائق في مجلد docs/
   - Check documentation files in docs/ folder

========================================
📝 ملاحظات إضافية / Additional Notes:
========================================

🔹 البرنامج يدعم اللغة العربية بالكامل مع دعم RTL
   Program fully supports Arabic language with RTL support

🔹 يمكن تشغيل البرنامج على أي جهاز Windows بدون تثبيت إضافي
   Can run on any Windows machine without additional installation

🔹 جميع البيانات محفوظة محلياً في قاعدة البيانات
   All data is stored locally in the database

🔹 البرنامج لا يحتاج اتصال بالإنترنت للعمل
   Program doesn't require internet connection to work

🔹 حجم الملف التنفيذي حوالي 89 MB
   Executable file size is approximately 89 MB

========================================
✅ تم إنشاء هذا الملف التنفيذي بتاريخ: 2025-07-01
   This executable was created on: 2025-07-01
========================================
