#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشخيص مشكلة البحث في الأصناف
"""

import sys
import os

# إضافة المسار الحالي
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from database.database_manager import DatabaseManager
    print("✅ تم تحميل DatabaseManager بنجاح")
except ImportError as e:
    print(f"❌ خطأ في تحميل DatabaseManager: {e}")
    sys.exit(1)

def debug_search():
    """تشخيص البحث"""
    print("🔍 تشخيص مشكلة البحث في الأصناف")
    print("=" * 50)
    
    db_manager = DatabaseManager()
    
    try:
        # اختبار الاستعلام
        query = '''
            SELECT i.id, i.item_code, i.item_name, i.description, i.unit_of_measure,
                   i.cost_price, i.selling_price, i.current_stock, c.category_name,
                   i.barcode, i.is_active
            FROM items i
            LEFT JOIN item_categories c ON i.category_id = c.id
            WHERE i.is_active = 1
            ORDER BY i.item_name
        '''
        
        print("📋 تنفيذ الاستعلام...")
        items = db_manager.fetch_all(query)
        print(f"✅ تم تحميل {len(items)} صنف")
        
        if items:
            print("\n📦 أول 3 أصناف:")
            for i, item in enumerate(items[:3]):
                print(f"  {i+1}. {item['item_code']} - {item['item_name']}")
                
            # محاكاة تحويل البيانات
            items_data = []
            for item in items:
                item_data = {
                    'id': item['id'],
                    'item_code': item['item_code'] or '',
                    'item_name': item['item_name'] or '',
                    'description': item['description'] or '',
                    'unit': item['unit_of_measure'] or '',
                    'unit_price': str(item['selling_price'] or item['cost_price'] or 0),
                    'category': item['category_name'] or 'غير محدد',
                    'barcode': item['barcode'] or '',
                    'origin_country': 'السعودية',
                    'current_stock': str(item['current_stock'] or 0),
                    'quantity': '1',
                    'total_price': str(item['selling_price'] or item['cost_price'] or 0),
                    'weight': '0',
                    'volume': '0'
                }
                items_data.append(item_data)
            
            print(f"🔄 تم تحويل {len(items_data)} صنف للتنسيق المطلوب")
            
            # اختبار البحث
            search_term = "لابتوب"
            print(f"\n🔍 اختبار البحث عن: '{search_term}'")
            
            filtered_items = []
            for item in items_data:
                item_text = f"{item.get('item_code', '')} {item.get('item_name', '')} {item.get('description', '')}".lower()
                if search_term.lower() in item_text:
                    filtered_items.append(item)
            
            print(f"📊 نتائج البحث: {len(filtered_items)} صنف")
            
            if filtered_items:
                print("📋 النتائج:")
                for item in filtered_items:
                    print(f"  • {item['item_code']} - {item['item_name']}")
            else:
                print("❌ لا توجد نتائج")
                
        else:
            print("❌ لا توجد أصناف في قاعدة البيانات")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_search()
