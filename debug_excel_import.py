#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص خطأ استيراد Excel
Debug Excel Import Error
"""

import sys
import os
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_excel_reading():
    """اختبار قراءة ملف Excel"""
    print("🧪 اختبار قراءة ملف Excel...")
    
    try:
        import pandas as pd
        
        # قراءة الملف النموذجي
        if os.path.exists('test_items_import.xlsx'):
            print("📁 وجد ملف test_items_import.xlsx")
            
            # قراءة الملف
            df = pd.read_excel('test_items_import.xlsx')
            print(f"✅ تم قراءة الملف بنجاح - {len(df)} صف، {len(df.columns)} عمود")
            
            # عرض الأعمدة
            print("📋 الأعمدة الموجودة:")
            for col in df.columns:
                print(f"  - {col}")
            
            # عرض أول صف
            print("\n📊 أول صف من البيانات:")
            if len(df) > 0:
                first_row = df.iloc[0]
                for col, value in first_row.items():
                    print(f"  {col}: {value} (نوع: {type(value)})")
            
            # اختبار معالجة البيانات
            print("\n🔧 اختبار معالجة البيانات...")
            for index, row in df.iterrows():
                print(f"\n--- الصف {index + 1} ---")
                
                # اختبار استخراج البيانات المطلوبة
                try:
                    item_code = str(row['item_code']).strip() if pd.notna(row['item_code']) else ''
                    item_name = str(row['item_name']).strip() if pd.notna(row['item_name']) else ''
                    unit_of_measure = str(row['unit_of_measure']).strip() if pd.notna(row['unit_of_measure']) else ''
                    
                    print(f"  item_code: '{item_code}'")
                    print(f"  item_name: '{item_name}'")
                    print(f"  unit_of_measure: '{unit_of_measure}'")
                    
                    # اختبار البيانات الاختيارية
                    if 'category_name' in row:
                        category_name = str(row['category_name']).strip() if pd.notna(row['category_name']) else None
                        print(f"  category_name: '{category_name}'")
                    
                    if 'cost_price' in row:
                        cost_price = float(row['cost_price']) if pd.notna(row['cost_price']) else 0.0
                        print(f"  cost_price: {cost_price}")
                    
                    if 'selling_price' in row:
                        selling_price = float(row['selling_price']) if pd.notna(row['selling_price']) else 0.0
                        print(f"  selling_price: {selling_price}")
                    
                    if 'current_stock' in row:
                        current_stock = float(row['current_stock']) if pd.notna(row['current_stock']) else 0.0
                        print(f"  current_stock: {current_stock}")
                    
                    if 'min_stock_level' in row:
                        min_stock_level = float(row['min_stock_level']) if pd.notna(row['min_stock_level']) else 0.0
                        print(f"  min_stock_level: {min_stock_level}")
                    
                    if 'description' in row:
                        description = str(row['description']).strip() if pd.notna(row['description']) else ''
                        print(f"  description: '{description}'")
                    
                    print("  ✅ معالجة الصف نجحت")
                    
                except Exception as e:
                    print(f"  ❌ خطأ في معالجة الصف: {e}")
                    print(f"  📋 تفاصيل الخطأ: {traceback.format_exc()}")
            
            return True
            
        else:
            print("❌ ملف test_items_import.xlsx غير موجود")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قراءة Excel: {e}")
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    print("\n🧪 اختبار عمليات قاعدة البيانات...")
    
    try:
        from database.database_manager import DatabaseManager
        from datetime import datetime
        
        db_manager = DatabaseManager()
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # اختبار إدراج صنف تجريبي
        test_item_code = 'DEBUG_TEST_001'
        test_item_name = 'صنف تجريبي للتشخيص'
        test_unit = 'قطعة'
        
        # حذف الصنف التجريبي إذا كان موجوداً
        cursor.execute("DELETE FROM items WHERE item_code = ?", (test_item_code,))
        
        # إدراج صنف جديد
        cursor.execute("""
            INSERT INTO items (
                item_code, item_name, unit_of_measure,
                cost_price, selling_price, current_stock, min_stock_level,
                description, is_active, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?)
        """, (
            test_item_code, test_item_name, test_unit,
            100.0, 120.0, 10.0, 5.0,
            'وصف تجريبي', datetime.now(), datetime.now()
        ))
        
        # التحقق من الإدراج
        cursor.execute("SELECT * FROM items WHERE item_code = ?", (test_item_code,))
        result = cursor.fetchone()
        
        if result:
            print("✅ تم إدراج الصنف التجريبي بنجاح")
            print(f"  ID: {result[0]}")
            print(f"  Code: {result[1]}")
            print(f"  Name: {result[2]}")
        else:
            print("❌ فشل في إدراج الصنف التجريبي")
        
        # تنظيف - حذف الصنف التجريبي
        cursor.execute("DELETE FROM items WHERE item_code = ?", (test_item_code,))
        
        conn.commit()
        conn.close()
        
        print("✅ اختبار قاعدة البيانات نجح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def test_items_window_import():
    """اختبار استيراد نافذة الأصناف"""
    print("\n🧪 اختبار استيراد نافذة الأصناف...")
    
    try:
        from src.items_window import ItemsWindow
        print("✅ تم استيراد ItemsWindow بنجاح")
        
        # اختبار إنشاء النافذة (بدون عرضها)
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        items_window = ItemsWindow(root)
        print("✅ تم إنشاء نافذة الأصناف بنجاح")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد نافذة الأصناف: {e}")
        print(f"📋 تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def main():
    """الدالة الرئيسية للتشخيص"""
    print("🔍 بدء تشخيص خطأ استيراد Excel")
    print("=" * 50)
    
    # اختبار قراءة Excel
    excel_ok = test_excel_reading()
    
    # اختبار قاعدة البيانات
    db_ok = test_database_operations()
    
    # اختبار نافذة الأصناف
    window_ok = test_items_window_import()
    
    print("\n" + "=" * 50)
    print("📋 نتائج التشخيص:")
    print(f"{'✅' if excel_ok else '❌'} قراءة ملف Excel")
    print(f"{'✅' if db_ok else '❌'} عمليات قاعدة البيانات")
    print(f"{'✅' if window_ok else '❌'} نافذة الأصناف")
    
    if all([excel_ok, db_ok, window_ok]):
        print("\n🎉 جميع الاختبارات نجحت!")
        print("💡 إذا كان الخطأ ما زال موجوداً، يرجى تقديم تفاصيل أكثر عن:")
        print("  - نص الخطأ الكامل")
        print("  - الخطوات التي أدت للخطأ")
        print("  - محتوى ملف Excel المستخدم")
    else:
        print("\n⚠️ تم اكتشاف مشاكل. راجع التفاصيل أعلاه.")

if __name__ == "__main__":
    import tkinter as tk
    main()
