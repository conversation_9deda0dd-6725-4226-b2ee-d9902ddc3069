#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تكامل نظام ربط الأصناف بالمخازن
Integration Test for Items-Warehouse Linking System
"""

import sys
import os
from datetime import datetime

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from database.database_manager import DatabaseManager

def test_database_schema():
    """اختبار هيكل قاعدة البيانات"""
    print("🔍 اختبار هيكل قاعدة البيانات...")
    
    db = DatabaseManager()
    conn = db.get_connection()
    cursor = conn.cursor()
    
    try:
        # اختبار وجود الحقول الجديدة في shipment_items
        cursor.execute("PRAGMA table_info(shipment_items)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        required_columns = ['item_id', 'warehouse_id']
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"❌ حقول مفقودة في shipment_items: {missing_columns}")
            return False
        else:
            print("✅ جميع الحقول المطلوبة موجودة في shipment_items")
        
        # اختبار وجود جدول inventory
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='inventory'")
        if not cursor.fetchone():
            print("❌ جدول inventory غير موجود")
            return False
        else:
            print("✅ جدول inventory موجود")
        
        # اختبار وجود جدول inventory_movements
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='inventory_movements'")
        if not cursor.fetchone():
            print("❌ جدول inventory_movements غير موجود")
            return False
        else:
            print("✅ جدول inventory_movements موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False
    finally:
        conn.close()

def test_inventory_functions():
    """اختبار وظائف إدارة المخزون"""
    print("\n🔍 اختبار وظائف إدارة المخزون...")
    
    db = DatabaseManager()
    
    try:
        # إنشاء بيانات تجريبية
        db.create_sample_categories()
        db.create_sample_items()
        
        # جلب صنف ومخزن للاختبار
        conn = db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id FROM items LIMIT 1")
        item = cursor.fetchone()
        if not item:
            print("❌ لا توجد أصناف للاختبار")
            return False
        item_id = item['id']
        
        cursor.execute("SELECT id FROM warehouses LIMIT 1")
        warehouse = cursor.fetchone()
        if not warehouse:
            print("❌ لا توجد مخازن للاختبار")
            return False
        warehouse_id = warehouse['id']
        
        conn.close()
        
        # اختبار إضافة للمخزون
        print(f"📦 اختبار إضافة 100 وحدة للصنف {item_id} في المخزن {warehouse_id}")
        db.add_to_inventory(item_id, warehouse_id, 100, 'test', 'test_001')
        
        # التحقق من الإضافة
        inventory_info = db.get_item_inventory(item_id, warehouse_id)
        if inventory_info and inventory_info['quantity'] >= 100:
            print("✅ تم إضافة الكمية للمخزون بنجاح")
        else:
            print("❌ فشل في إضافة الكمية للمخزون")
            return False
        
        # اختبار خصم من المخزون
        print(f"📤 اختبار خصم 50 وحدة من الصنف {item_id} في المخزن {warehouse_id}")
        db.remove_from_inventory(item_id, warehouse_id, 50, 'test', 'test_002')
        
        # التحقق من الخصم
        inventory_info = db.get_item_inventory(item_id, warehouse_id)
        if inventory_info and inventory_info['quantity'] >= 50:
            print("✅ تم خصم الكمية من المخزون بنجاح")
        else:
            print("❌ فشل في خصم الكمية من المخزون")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وظائف المخزون: {e}")
        return False

def test_shipment_integration():
    """اختبار تكامل الشحنات مع المخزون"""
    print("\n🔍 اختبار تكامل الشحنات مع المخزون...")
    
    db = DatabaseManager()
    
    try:
        # إنشاء شحنة تجريبية
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # إنشاء شحنة
        shipment_id = "test_shipment_001"
        cursor.execute("""
            INSERT OR REPLACE INTO shipments (
                id, shipment_number, supplier_id, status, created_at
            ) VALUES (?, ?, ?, ?, ?)
        """, (shipment_id, "TEST001", 1, "جديدة", datetime.now().isoformat()))
        
        # جلب صنف ومخزن
        cursor.execute("SELECT id FROM items LIMIT 1")
        item = cursor.fetchone()
        item_id = item['id']
        
        cursor.execute("SELECT id FROM warehouses LIMIT 1")
        warehouse = cursor.fetchone()
        warehouse_id = warehouse['id']
        
        # إضافة صنف للشحنة
        cursor.execute("""
            INSERT OR REPLACE INTO shipment_items (
                id, shipment_id, item_code, item_name, item_id, warehouse_id,
                quantity, unit, unit_price, total_price
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, ("test_item_001", shipment_id, "TEST001", "صنف تجريبي", 
              item_id, warehouse_id, 25, "قطعة", 10.0, 250.0))
        
        conn.commit()
        conn.close()
        
        # اختبار تحديث المخزون من الشحنة
        print(f"📦 اختبار تحديث المخزون من الشحنة {shipment_id}")
        db.update_inventory_from_shipment(shipment_id, 'add')
        
        # التحقق من التحديث
        inventory_info = db.get_item_inventory(item_id, warehouse_id)
        if inventory_info:
            print(f"✅ تم تحديث المخزون - الكمية الحالية: {inventory_info['quantity']}")
        else:
            print("❌ فشل في تحديث المخزون من الشحنة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تكامل الشحنات: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار نظام ربط الأصناف بالمخازن")
    print("=" * 50)
    
    tests = [
        ("اختبار هيكل قاعدة البيانات", test_database_schema),
        ("اختبار وظائف إدارة المخزون", test_inventory_functions),
        ("اختبار تكامل الشحنات مع المخزون", test_shipment_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - نجح")
            else:
                print(f"❌ {test_name} - فشل")
        except Exception as e:
            print(f"❌ {test_name} - خطأ: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت - يرجى مراجعة الأخطاء")
        return False

if __name__ == "__main__":
    main()
