#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة حوار إضافة/تعديل الأصناف مع دعم RTL كامل
Item Dialog with Full RTL Support
"""

import tkinter as tk
from tkinter import messagebox
import os
import sys
import uuid

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS
from src.simple_rtl_components import *
from src.item_code_search_dialog import ItemCodeSearchDialog
from database.database_manager import DatabaseManager

class ItemDialog:
    """نافذة حوار الأصناف"""
    
    def __init__(self, parent, mode='add', item_data=None, initial_data=None):
        self.parent = parent
        self.mode = mode  # add, edit, duplicate
        self.item_data = item_data or {}
        self.initial_data = initial_data or {}  # بيانات أولية من البحث
        self.result = None
        self.db_manager = DatabaseManager()
        
        # إنشاء النافذة
        self.root = tk.Toplevel(parent)
        self.setup_window()
        
        # متغيرات النموذج
        self.form_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات
        self.load_data()
        
        # توسيط النافذة
        self.center_window()
        
    def setup_window(self):
        """إعداد النافذة"""
        titles = {
            'add': '➕ إضافة صنف جديد',
            'edit': '✏️ تعديل الصنف',
            'duplicate': '📋 نسخ الصنف'
        }
        
        self.root.title(titles.get(self.mode, 'نافذة الصنف'))
        self.root.geometry("600x500")
        self.root.configure(bg=COLORS['background'])
        self.root.resizable(False, False)
        
        # جعل النافذة modal
        self.root.transient(self.parent)
        self.root.grab_set()
        
        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.cancel)
        
        # ربط اختصارات لوحة المفاتيح
        self.root.bind('<Return>', lambda e: self.save())
        self.root.bind('<Escape>', lambda e: self.cancel())
        self.root.bind('<F9>', self.show_item_code_search)
        
    def create_interface(self):
        """إنشاء الواجهة"""
        # الإطار الرئيسي
        main_frame = create_simple_rtl_frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_text = {
            'add': '📦 إضافة صنف جديد',
            'edit': '✏️ تعديل بيانات الصنف',
            'duplicate': '📋 نسخ صنف موجود'
        }
        
        title_label = create_simple_rtl_label(
            main_frame,
            text=title_text.get(self.mode, 'نافذة الصنف'),
            font=('Segoe UI', 18, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e', pady=(0, 20))
        
        # محتوى النموذج
        content_frame = create_simple_rtl_frame(main_frame)
        content_frame.pack(fill='both', expand=True)
        
        # الصف الأول - كود الصنف واسم الصنف
        row1 = create_simple_rtl_frame(content_frame)
        row1.pack(fill='x', pady=(0, 15))
        
        # كود الصنف مع البحث F9
        self.item_code_entry = self.create_field(row1, "🔢 كود الصنف * (F9 للبحث)", 'item_code', width_ratio=1/2)

        # ربط F9 بحقل كود الصنف
        self.item_code_entry.bind('<F9>', self.show_item_code_search)
        self.item_code_entry.bind('<KeyPress-F9>', self.show_item_code_search)
        
        # اسم الصنف
        self.create_field(row1, "📦 اسم الصنف *", 'item_name', width_ratio=1/2)
        
        # الصف الثاني - الوصف
        row2 = create_simple_rtl_frame(content_frame)
        row2.pack(fill='x', pady=(15, 15))
        
        # الوصف
        desc_label = create_simple_rtl_label(
            row2,
            text="📝 وصف الصنف:",
            font=('Segoe UI', 12, 'bold')
        )
        desc_label.pack(anchor='e', pady=(0, 5))
        
        self.form_vars['description'] = tk.StringVar()
        desc_entry = create_simple_rtl_entry(
            row2,
            textvariable=self.form_vars['description']
        )
        desc_entry.pack(fill='x', ipady=8)
        
        # الصف الثالث - الكمية والوحدة
        row3 = create_simple_rtl_frame(content_frame)
        row3.pack(fill='x', pady=(15, 15))
        
        # الكمية
        self.quantity_entry = self.create_field(row3, "📊 الكمية *", 'quantity', width_ratio=1/2)
        
        # الوحدة
        self.create_combobox_field(row3, "📏 الوحدة *", 'unit', 
                                  values=['قطعة', 'كيلو', 'متر', 'لتر', 'طن', 'صندوق', 'كرتون'], 
                                  width_ratio=1/2)
        
        # الصف الرابع - سعر الوحدة والإجمالي
        row4 = create_simple_rtl_frame(content_frame)
        row4.pack(fill='x', pady=(15, 15))
        
        # سعر الوحدة
        self.create_field(row4, "💰 سعر الوحدة *", 'unit_price', width_ratio=1/2)
        
        # الإجمالي (محسوب تلقائياً)
        self.create_calculated_field(row4, "💯 الإجمالي", 'total_price', width_ratio=1/2)
        
        # الصف الخامس - الوزن والحجم
        row5 = create_simple_rtl_frame(content_frame)
        row5.pack(fill='x', pady=(15, 15))
        
        # الوزن
        self.create_field(row5, "⚖️ الوزن (كجم)", 'weight', width_ratio=1/2)
        
        # الحجم
        self.create_field(row5, "📐 الحجم (م³)", 'volume', width_ratio=1/2)
        
        # الصف السادس - المخزن المستهدف
        row6 = create_simple_rtl_frame(content_frame)
        row6.pack(fill='x', pady=(15, 15))

        # المخزن المستهدف
        self.create_warehouse_combobox(row6, "🏪 المخزن المستهدف *", 'warehouse_id', width_ratio=1)

        # الصف السابع - كود HS وبلد المنشأ
        row7 = create_simple_rtl_frame(content_frame)
        row7.pack(fill='x', pady=(15, 15))

        # كود HS
        self.create_field(row7, "🏷️ كود HS", 'hs_code', width_ratio=1/2)

        # بلد المنشأ
        self.create_combobox_field(row7, "🌍 بلد المنشأ", 'origin_country',
                                  values=['السعودية', 'الإمارات', 'الكويت', 'قطر', 'البحرين', 'عمان',
                                         'الصين', 'الهند', 'تركيا', 'ألمانيا', 'أمريكا', 'اليابان'],
                                  width_ratio=1/2)
        
        # أزرار التحكم
        buttons_frame = create_simple_rtl_frame(main_frame)
        buttons_frame.pack(fill='x', pady=(20, 0))
        
        # زر الحفظ
        save_btn = create_simple_rtl_button(
            buttons_frame,
            text="💾 حفظ",
            button_type="success",
            command=self.save
        )
        save_btn.pack(side='right', padx=5)
        
        # زر الإلغاء
        cancel_btn = create_simple_rtl_button(
            buttons_frame,
            text="❌ إلغاء",
            button_type="danger",
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=5)

        # معلومات المساعدة
        help_label = create_simple_rtl_label(
            buttons_frame,
            text="💡 نصيحة: F9 للبحث في أكواد الأصناف، Enter للحفظ، Esc للإلغاء",
            font=('Segoe UI', 10, 'normal'),
            fg=COLORS['text_secondary']
        )
        help_label.pack(side='left', padx=10)

        # ربط تغيير البيانات للحسابات التلقائية
        self.form_vars['quantity'].trace('w', self.calculate_total)
        self.form_vars['unit_price'].trace('w', self.calculate_total)
        
    def create_field(self, parent, label_text, var_name, width_ratio=1/2):
        """إنشاء حقل إدخال"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=10)
        
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 12, 'bold')
        )
        label.pack(anchor='e', pady=(0, 5))
        
        self.form_vars[var_name] = tk.StringVar()
        entry = create_simple_rtl_entry(
            field_frame,
            textvariable=self.form_vars[var_name]
        )
        entry.pack(fill='x', ipady=8)
        
        return entry
    
    def create_combobox_field(self, parent, label_text, var_name, values=[], width_ratio=1/2):
        """إنشاء حقل قائمة منسدلة"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=10)
        
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 12, 'bold')
        )
        label.pack(anchor='e', pady=(0, 5))
        
        self.form_vars[var_name] = tk.StringVar()
        combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.form_vars[var_name]
        )
        combo['values'] = values
        combo.pack(fill='x', ipady=8)
        
        return combo
    
    def create_calculated_field(self, parent, label_text, var_name, width_ratio=1/2):
        """إنشاء حقل محسوب"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=10)
        
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 12, 'bold')
        )
        label.pack(anchor='e', pady=(0, 5))
        
        self.form_vars[var_name] = tk.StringVar()
        display = create_simple_rtl_label(
            field_frame,
            textvariable=self.form_vars[var_name],
            font=('Segoe UI', 12, 'normal'),
            fg=COLORS['primary'],
            relief='sunken',
            bd=2,
            pady=8
        )
        display.pack(fill='x')
        
        return display
    
    def calculate_total(self, *args):
        """حساب الإجمالي تلقائياً"""
        try:
            quantity = float(self.form_vars['quantity'].get() or 0)
            unit_price = float(self.form_vars['unit_price'].get() or 0)
            total = quantity * unit_price
            self.form_vars['total_price'].set(f"{total:.2f}")
        except:
            self.form_vars['total_price'].set("0.00")

    def create_warehouse_combobox(self, parent, label_text, var_name, width_ratio=1):
        """إنشاء combobox للمخازن"""
        # إطار الحقل
        field_frame = create_simple_rtl_frame(parent)
        if width_ratio < 1:
            field_frame.pack(side='right', fill='x', expand=True, padx=(0, 10))
        else:
            field_frame.pack(fill='x', pady=(0, 5))

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 12, 'bold')
        )
        label.pack(anchor='e', pady=(0, 5))

        # المتغير
        self.form_vars[var_name] = tk.StringVar()

        # جلب المخازن من قاعدة البيانات
        warehouses = self.get_warehouses()
        warehouse_values = [f"{w['id']} - {w['warehouse_name']}" for w in warehouses]

        # Combobox
        combobox = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.form_vars[var_name],
            values=warehouse_values,
            state='readonly'
        )
        combobox.pack(fill='x', ipady=8)

        return combobox

    def get_warehouses(self):
        """جلب قائمة المخازن النشطة"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT id, warehouse_name FROM warehouses WHERE is_active = 1 ORDER BY warehouse_name")
            warehouses = cursor.fetchall()
            conn.close()
            return warehouses
        except Exception as e:
            print(f"خطأ في جلب المخازن: {e}")
            return []

    def load_data(self):
        """تحميل البيانات"""
        # تحميل البيانات الأولية من البحث أولاً
        if self.initial_data:
            for field, var in self.form_vars.items():
                value = self.initial_data.get(field, '')
                if value:
                    var.set(str(value))

        # ثم تحميل بيانات التحرير إذا كانت متوفرة
        if self.item_data and self.mode in ['edit', 'duplicate']:
            # ملء الحقول بالبيانات الموجودة
            for field, var in self.form_vars.items():
                value = self.item_data.get(field, '')
                if value:
                    var.set(str(value))

            # إذا كان الوضع نسخ، مسح كود الصنف
            if self.mode == 'duplicate':
                self.form_vars['item_code'].set('')
    
    def validate_form(self, show_errors=True):
        """التحقق من صحة النموذج"""
        errors = []
        
        # التحقق من الحقول المطلوبة
        required_fields = {
            'item_code': 'كود الصنف',
            'item_name': 'اسم الصنف',
            'quantity': 'الكمية',
            'unit': 'الوحدة',
            'unit_price': 'سعر الوحدة',
            'warehouse_id': 'المخزن المستهدف'
        }
        
        for field, label in required_fields.items():
            value = self.form_vars.get(field, tk.StringVar()).get().strip()

            # معالجة خاصة لحقل المخزن المستهدف
            if field == 'warehouse_id':
                # التحقق من أن القيمة تحتوي على رقم المخزن صحيح
                if not value:
                    errors.append(f"حقل {label} مطلوب")
                else:
                    try:
                        # التحقق من وجود الفاصل والرقم
                        if ' - ' not in value:
                            errors.append(f"حقل {label} غير صحيح")
                        else:
                            warehouse_id_part = value.split(' - ')[0].strip()
                            if not warehouse_id_part or not warehouse_id_part.isdigit():
                                errors.append(f"حقل {label} غير صحيح")
                    except (ValueError, IndexError):
                        errors.append(f"حقل {label} غير صحيح")
            else:
                if not value:
                    errors.append(f"حقل {label} مطلوب")
        
        # التحقق من صحة الأرقام
        numeric_fields = ['quantity', 'unit_price', 'weight', 'volume']
        for field in numeric_fields:
            value = self.form_vars.get(field, tk.StringVar()).get().strip()
            if value:
                try:
                    float(value)
                except ValueError:
                    errors.append(f"قيمة {field} يجب أن تكون رقماً")
        
        if errors:
            if show_errors:
                error_message = "يرجى تصحيح الأخطاء التالية:\n\n" + "\n".join(f"• {error}" for error in errors)
                messagebox.showerror("أخطاء في البيانات", error_message)
            return False

        return True
    
    def save(self):
        """حفظ البيانات"""
        if not self.validate_form():
            return
        
        try:
            # جمع البيانات
            self.result = {}
            for field, var in self.form_vars.items():
                value = var.get().strip()
                if field in ['quantity', 'unit_price', 'total_price', 'weight', 'volume'] and value:
                    try:
                        self.result[field] = float(value)
                    except ValueError:
                        self.result[field] = 0.0
                elif field == 'warehouse_id' and value:
                    # استخراج معرف المخزن من النص المحدد
                    try:
                        self.result[field] = int(value.split(' - ')[0])
                    except (ValueError, IndexError):
                        self.result[field] = None
                else:
                    self.result[field] = value if value else None

            # البحث عن معرف الصنف من قاعدة البيانات
            item_code = self.result.get('item_code', '')
            if item_code:
                self.result['item_id'] = self.get_item_id_by_code(item_code)

            # إضافة معرف إذا كان جديداً
            if self.mode == 'add' or self.mode == 'duplicate':
                self.result['id'] = str(uuid.uuid4())
            else:
                self.result['id'] = self.item_data.get('id')

            # إغلاق النافذة
            self.root.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ البيانات: {str(e)}")

    def get_item_id_by_code(self, item_code):
        """جلب معرف الصنف من كود الصنف"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT id FROM items WHERE item_code = ?", (item_code,))
            result = cursor.fetchone()
            conn.close()
            return result['id'] if result else None
        except Exception as e:
            print(f"خطأ في جلب معرف الصنف: {e}")
            return None
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.root.destroy()
    
    def show_item_code_search(self, event=None):
        """إظهار نافذة البحث في أكواد الأصناف عند الضغط على F9"""
        try:
            # إنشاء نافذة البحث في أكواد الأصناف
            search_dialog = ItemCodeSearchDialog(self.root, self)
            self.root.wait_window(search_dialog.root)

            # إذا تم اختيار كود صنف، ملء الحقل
            if hasattr(search_dialog, 'selected_code') and search_dialog.selected_code:
                self.form_vars['item_code'].set(search_dialog.selected_code)

                # إذا كان هناك بيانات إضافية، ملؤها أيضاً
                if hasattr(search_dialog, 'selected_item_data') and search_dialog.selected_item_data:
                    item_data = search_dialog.selected_item_data

                    # ملء الحقول المتاحة
                    if item_data.get('item_name'):
                        self.form_vars['item_name'].set(item_data['item_name'])
                    if item_data.get('description'):
                        self.form_vars['description'].set(item_data['description'])
                    if item_data.get('unit'):
                        self.form_vars['unit'].set(item_data['unit'])
                    if item_data.get('unit_price'):
                        self.form_vars['unit_price'].set(item_data['unit_price'])
                    if item_data.get('origin_country'):
                        self.form_vars['origin_country'].set(item_data['origin_country'])

                # تركيز على الحقل التالي (الكمية)
                if hasattr(self, 'quantity_entry'):
                    self.quantity_entry.focus_set()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")

    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

if __name__ == "__main__":
    # اختبار النافذة
    root = tk.Tk()
    root.withdraw()
    
    dialog = ItemDialog(root, mode='add')
    root.mainloop()
