#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير أنماط واجهة المستخدم
UI Styles Manager
"""

class StyleManager:
    """مدير الأنماط"""
    
    def __init__(self):
        self.fonts = {
            'arabic': {
                'family': 'Segoe UI',
                'size': 12
            },
            'arabic_header': {
                'family': 'Segoe UI',
                'size': 14,
                'weight': 'bold'
            }
        }
    
    def get_font(self, font_type='arabic', size=None, weight=None):
        """الحصول على خط محدد"""
        font_config = self.fonts.get(font_type, self.fonts['arabic']).copy()
        
        if size:
            font_config['size'] = size
        if weight:
            font_config['weight'] = weight
            
        return (font_config['family'], font_config['size'], font_config.get('weight', 'normal'))

# إنشاء مثيل مشترك
_style_manager = StyleManager()

def get_style_manager():
    """الحصول على مدير الأنماط"""
    return _style_manager
