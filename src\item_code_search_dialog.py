#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة حوار البحث في أكواد الأصناف مع دعم RTL كامل
Item Code Search Dialog with Full RTL Support
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from config.config import COLORS, FONTS
from src.simple_rtl_components import *
from database.database_manager import DatabaseManager

class ItemCodeSearchDialog:
    """نافذة حوار البحث في أكواد الأصناف"""
    
    def __init__(self, parent, item_dialog):
        self.parent = parent
        self.item_dialog = item_dialog
        self.db_manager = DatabaseManager()
        self.items_data = []
        self.filtered_items = []
        self.selected_code = None
        self.selected_item_data = None
        
        # إنشاء النافذة
        self.root = tk.Toplevel(parent)
        self.setup_window()
        
        # متغيرات البحث
        self.search_vars = {}
        
        # إنشاء الواجهة
        self.create_interface()

        # تحميل البيانات
        self.load_items_from_database()

        # ربط أحداث البحث بعد تحميل البيانات
        self.bind_search_events()

        # التأكد من تحديث الجدول بعد إنشاء الواجهة
        if hasattr(self, 'items_data') and self.items_data and hasattr(self, 'results_tree'):
            self.filtered_items = self.items_data.copy()
            self.update_results_tree()
            self.update_results_count()
        
        # توسيط النافذة
        self.center_window()
        
        # تركيز على حقل البحث
        self.focus_search_field()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.root.title("🔍 البحث المتقدم في أكواد الأصناف - F9")
        self.root.geometry("1000x700")
        self.root.configure(bg=COLORS['background'])
        self.root.resizable(True, True)
        self.root.minsize(800, 600)

        # جعل النافذة modal
        self.root.transient(self.parent)
        self.root.grab_set()

        # ربط إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.close_dialog)

        # ربط اختصارات لوحة المفاتيح
        self.root.bind('<Return>', lambda e: self.select_item())
        self.root.bind('<Escape>', lambda e: self.close_dialog())
        self.root.bind('<F9>', lambda e: self.close_dialog())
        self.root.bind('<F3>', lambda e: self.toggle_advanced_search())  # F3 للبحث المتقدم
        self.root.bind('<Double-Button-1>', lambda e: self.select_item())
        self.root.bind('<Control-f>', lambda e: self.focus_search_field())
        self.root.bind('<F5>', lambda e: self.refresh_data())
        
    def create_interface(self):
        """إنشاء الواجهة"""
        # الإطار الرئيسي
        main_frame = create_simple_rtl_frame(self.root)
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = create_simple_rtl_label(
            main_frame,
            text="🔍 البحث المتقدم في أكواد الأصناف",
            font=('Segoe UI', 20, 'bold'),
            fg=COLORS['primary']
        )
        title_label.pack(anchor='e', pady=(0, 15))

        # وصف
        desc_label = create_simple_rtl_label(
            main_frame,
            text="ابحث في الأصناف المحفوظة مسبقاً بطرق متعددة واختر كود الصنف المطلوب • البحث فوري ومتقدم",
            font=('Segoe UI', 12, 'normal'),
            fg=COLORS['text_secondary']
        )
        desc_label.pack(anchor='e', pady=(0, 15))
        
        # منطقة البحث المتقدم
        search_frame = create_simple_rtl_frame(main_frame)
        search_frame.pack(fill='x', pady=(0, 20))

        # عنوان البحث
        search_title = create_simple_rtl_label(
            search_frame,
            text="🔍 البحث المتقدم والفوري",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['primary']
        )
        search_title.pack(anchor='e', pady=(0, 15))

        # الصف الأول - البحث العام
        search_row1 = create_simple_rtl_frame(search_frame)
        search_row1.pack(fill='x', pady=(0, 15))

        # حقل البحث العام
        general_label = create_simple_rtl_label(
            search_row1,
            text="🔍 البحث السريع (كود أو اسم):",
            font=('Segoe UI', 14, 'bold')
        )
        general_label.pack(anchor='e', pady=(0, 5))

        self.search_vars['general'] = tk.StringVar()
        self.general_search_entry = create_simple_rtl_entry(
            search_row1,
            textvariable=self.search_vars['general'],
            placeholder="ابحث في كود الصنف أو اسم الصنف أو الوصف..."
        )
        self.general_search_entry.configure(
            font=('Segoe UI', 14, 'normal'),
            fg='#1f2937',
            bg='#f9fafb',
            relief='solid',
            bd=2
        )
        self.general_search_entry.pack(fill='x', ipady=12)

        # سيتم ربط البحث الفوري لاحقاً بعد تحميل البيانات

        # أزرار البحث الأساسية
        buttons_row = create_simple_rtl_frame(search_frame)
        buttons_row.pack(fill='x', pady=(15, 0))

        # زر البحث المتقدم
        self.advanced_search_btn = create_simple_rtl_button(
            buttons_row,
            text="⚙️ بحث متقدم - F3",
            button_type="info",
            command=self.toggle_advanced_search
        )
        self.advanced_search_btn.pack(side='right', padx=5)

        # إطار البحث المتقدم (مخفي في البداية)
        self.advanced_frame = create_simple_rtl_frame(search_frame)
        self.advanced_frame.pack(fill='x', pady=(10, 0))
        self.advanced_frame.pack_forget()  # إخفاء الإطار

        # عنوان البحث المتقدم
        advanced_label = create_simple_rtl_label(
            self.advanced_frame,
            text="🎯 البحث المتقدم:",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['secondary']
        )
        advanced_label.pack(anchor='e', pady=(0, 10))

        # إطار الحقول المتقدمة
        advanced_fields_frame = create_simple_rtl_frame(self.advanced_frame)
        advanced_fields_frame.pack(fill='x')

        # الصف الأول من الحقول المتقدمة
        advanced_row1 = create_simple_rtl_frame(advanced_fields_frame)
        advanced_row1.pack(fill='x', pady=(0, 10))

        # كود الصنف
        self.create_advanced_search_field(advanced_row1, "🔢 كود الصنف:", 'item_code', width_ratio=1/3)

        # اسم الصنف
        self.create_advanced_search_field(advanced_row1, "📦 اسم الصنف:", 'item_name', width_ratio=1/3)

        # الوحدة
        self.create_advanced_search_field(advanced_row1, "📏 الوحدة:", 'unit', width_ratio=1/3)

        # الصف الثاني من الحقول المتقدمة
        advanced_row2 = create_simple_rtl_frame(advanced_fields_frame)
        advanced_row2.pack(fill='x', pady=(10, 0))

        # بلد المنشأ
        self.create_advanced_search_combobox(advanced_row2, "🌍 بلد المنشأ:", 'origin_country',
                                           values=['', 'السعودية', 'الإمارات', 'الكويت', 'قطر', 'البحرين', 'عمان',
                                                  'الصين', 'الهند', 'تركيا', 'ألمانيا', 'أمريكا', 'اليابان', 'كوريا الجنوبية',
                                                  'إيطاليا', 'فرنسا', 'بريطانيا', 'كندا', 'أستراليا'],
                                           width_ratio=1/2)

        # نطاق السعر
        self.create_price_range_field(advanced_row2, width_ratio=1/2)

        # متغير لتتبع حالة البحث المتقدم
        self.advanced_search_visible = False

        # أزرار البحث والتحكم الإضافية
        additional_buttons_row = create_simple_rtl_frame(search_frame)
        additional_buttons_row.pack(fill='x', pady=(10, 0))
        
        # زر تحديث البيانات
        refresh_btn = create_simple_rtl_button(
            additional_buttons_row,
            text="🔄 تحديث البيانات",
            button_type="primary",
            command=self.refresh_data
        )
        refresh_btn.pack(side='right', padx=5)

        # زر مسح البحث
        clear_btn = create_simple_rtl_button(
            additional_buttons_row,
            text="🗑️ مسح البحث",
            button_type="secondary",
            command=self.clear_all_search
        )
        clear_btn.pack(side='right', padx=5)

        # عداد النتائج مع معلومات إضافية
        self.results_label = create_simple_rtl_label(
            additional_buttons_row,
            text="",
            font=('Segoe UI', 12, 'bold'),
            fg=COLORS['primary']
        )
        self.results_label.pack(side='left', padx=10)

        # مؤشر حالة البحث
        self.search_status_label = create_simple_rtl_label(
            additional_buttons_row,
            text="💡 ابدأ الكتابة للبحث الفوري",
            font=('Segoe UI', 10, 'italic'),
            fg=COLORS['text_secondary']
        )
        self.search_status_label.pack(side='left', padx=(20, 10))
        
        # جدول النتائج
        results_frame = create_simple_rtl_frame(main_frame)
        results_frame.pack(fill='both', expand=True, pady=(20, 0))
        
        # عنوان النتائج مع إحصائيات
        results_header = create_simple_rtl_frame(results_frame)
        results_header.pack(fill='x', pady=(0, 10))

        results_title = create_simple_rtl_label(
            results_header,
            text="📊 نتائج البحث المتقدم",
            font=('Segoe UI', 16, 'bold'),
            fg=COLORS['primary']
        )
        results_title.pack(side='right', anchor='e')

        # مؤشر حالة التحميل
        self.loading_label = create_simple_rtl_label(
            results_header,
            text="",
            font=('Segoe UI', 12, 'normal'),
            fg=COLORS['warning']
        )
        self.loading_label.pack(side='left', padx=10)

        # إنشاء جدول النتائج المحسن
        table_frame = create_simple_rtl_frame(results_frame)
        table_frame.pack(fill='both', expand=True)

        # تعريف أعمدة الجدول المحسنة
        columns = ['item_code', 'item_name', 'description', 'unit', 'unit_price', 'origin_country']
        column_names = ['كود الصنف', 'اسم الصنف', 'الوصف', 'الوحدة', 'سعر الوحدة', 'بلد المنشأ']

        # إنشاء Treeview محسن
        self.results_tree = create_simple_rtl_treeview(
            table_frame,
            columns=columns,
            show='tree headings',
            height=15
        )

        # تكوين الأعمدة المحسنة
        widths = [120, 180, 200, 80, 100, 120]
        for i, (col_id, col_name, width) in enumerate(zip(columns, column_names, widths)):
            self.results_tree.heading(col_id, text=col_name, anchor='e')
            self.results_tree.column(col_id, width=width, anchor='e', minwidth=60)

        # العمود الرئيسي
        self.results_tree.column('#0', width=40, minwidth=40, anchor='e')
        self.results_tree.heading('#0', text='#', anchor='e')
        
        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.results_tree.xview)
        self.results_tree.configure(xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول
        self.results_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط أحداث الجدول
        self.results_tree.bind('<<TreeviewSelect>>', self.on_result_select)
        self.results_tree.bind('<Double-1>', self.on_result_double_click)
        
        # إعداد ألوان الجدول
        self.setup_tree_colors()
        
        # أزرار التحكم
        control_frame = create_simple_rtl_frame(main_frame)
        control_frame.pack(fill='x', pady=(20, 0))
        
        # زر الاختيار
        select_btn = create_simple_rtl_button(
            control_frame,
            text="✅ اختيار الكود",
            button_type="success",
            command=self.select_item
        )
        select_btn.pack(side='right', padx=5)
        
        # زر الإغلاق
        close_btn = create_simple_rtl_button(
            control_frame,
            text="❌ إغلاق",
            button_type="danger",
            command=self.close_dialog
        )
        close_btn.pack(side='right', padx=5)
        
        # معلومات المساعدة
        help_label = create_simple_rtl_label(
            control_frame,
            text="💡 نصيحة: انقر مرتين على الصنف لاختياره مباشرة",
            font=('Segoe UI', 10, 'normal'),
            fg=COLORS['text_secondary']
        )
        help_label.pack(side='left', padx=10)
    
    def setup_tree_colors(self):
        """إعداد ألوان الجدول المحسنة"""
        try:
            # الألوان الأساسية
            self.results_tree.tag_configure('odd_row', background='#F9FAFB', foreground='#1F2937')
            self.results_tree.tag_configure('even_row', background='#FFFFFF', foreground='#1F2937')
            self.results_tree.tag_configure('selected', background='#DBEAFE', foreground='#1E40AF')

            # ألوان خاصة للأسعار
            self.results_tree.tag_configure('high_price', background='#FEF3C7', foreground='#92400E')
            self.results_tree.tag_configure('no_price', background='#FEE2E2', foreground='#991B1B')

            # تحسين خط الجدول
            style = ttk.Style()
            style.configure("Treeview", font=('Segoe UI', 10, 'normal'))
            style.configure("Treeview.Heading", font=('Segoe UI', 11, 'bold'))

        except Exception as e:
            print(f"خطأ في إعداد ألوان الجدول: {e}")
    
    def load_items_from_database(self):
        """تحميل الأصناف من قاعدة البيانات"""
        try:
            # استعلام للحصول على الأصناف من جدول items الرئيسي
            query = """
                SELECT i.id, i.item_code, i.item_name, i.description, i.unit_of_measure,
                       i.cost_price, i.selling_price, i.current_stock, c.category_name,
                       i.barcode, i.is_active
                FROM items i
                LEFT JOIN item_categories c ON i.category_id = c.id
                WHERE i.is_active = 1
                ORDER BY i.item_code
            """

            items = self.db_manager.fetch_all(query)
            self.items_data = []

            for item in items:
                item_data = {
                    'id': item['id'],
                    'item_code': item['item_code'] or '',
                    'item_name': item['item_name'] or '',
                    'description': item['description'] or '',
                    'unit': item['unit_of_measure'] or '',
                    'unit_price': str(item['selling_price'] or item['cost_price'] or 0),
                    'origin_country': 'السعودية',  # قيمة افتراضية
                    'category': item['category_name'] or 'غير محدد',
                    'current_stock': str(item['current_stock'] or 0),
                    'barcode': item['barcode'] or ''
                }
                self.items_data.append(item_data)
            
            # تحديث القائمة المفلترة
            self.filtered_items = self.items_data.copy()

            # تحديث الجدول إذا كان موجوداً
            if hasattr(self, 'results_tree') and self.results_tree:
                self.update_results_tree()
                self.update_results_count()
            
        except Exception as e:
            print(f"خطأ في تحميل الأصناف من قاعدة البيانات: {e}")
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def bind_search_events(self):
        """ربط أحداث البحث بعد تحميل البيانات"""
        try:
            # ربط البحث العام
            if 'general' in self.search_vars:
                self.search_vars['general'].trace('w', self.on_search_change)

            # ربط حقول البحث المتقدم
            for var_name in ['item_code', 'item_name', 'description', 'unit', 'category', 'price_min', 'price_max']:
                if var_name in self.search_vars:
                    self.search_vars[var_name].trace('w', self.on_search_change)



            # تنفيذ البحث الأولي لعرض جميع البيانات
            self.perform_search()

        except Exception as e:
            print(f"خطأ في ربط أحداث البحث: {e}")

    def toggle_advanced_search(self):
        """تبديل إظهار/إخفاء البحث المتقدم"""
        try:
            if self.advanced_search_visible:
                # إخفاء البحث المتقدم
                self.advanced_frame.pack_forget()
                self.advanced_search_btn.config(text="⚙️ بحث متقدم - F3")
                self.advanced_search_visible = False
                print("🔼 تم إخفاء البحث المتقدم")
            else:
                # إظهار البحث المتقدم
                # البحث عن الموضع المناسب لإدراج الإطار
                buttons_row = self.advanced_search_btn.master
                search_frame = buttons_row.master

                # إدراج الإطار بعد أزرار البحث
                self.advanced_frame.pack(fill='x', pady=(10, 0), in_=search_frame, after=buttons_row)
                self.advanced_search_btn.config(text="🔼 إخفاء البحث المتقدم - F3")
                self.advanced_search_visible = True
                print("🔽 تم إظهار البحث المتقدم")

        except Exception as e:
            print(f"❌ خطأ في تبديل البحث المتقدم: {e}")
            import traceback
            traceback.print_exc()

    def on_search_change(self, *args):
        """عند تغيير نص البحث"""
        # التحقق من وجود البيانات قبل البحث
        if not hasattr(self, 'items_data') or not self.items_data:
            return

        if hasattr(self, '_search_timer'):
            self.root.after_cancel(self._search_timer)
        self._search_timer = self.root.after(300, self.perform_search)
    
    def perform_search(self):
        """تنفيذ البحث المحسن"""
        try:
            # التحقق من وجود البيانات قبل البحث
            if not hasattr(self, 'items_data') or not self.items_data:
                return

            # تحديث مؤشر حالة البحث
            self.search_status_label.configure(text="🔍 جاري البحث...")
            self.root.update()

            # الحصول على معايير البحث (تجاهل النصوص الافتراضية)
            general_search = self.search_vars['general'].get().strip().lower()
            if general_search.startswith('ابحث في'):
                general_search = ''

            item_code_search = self.search_vars.get('item_code', tk.StringVar()).get().strip().lower()
            if item_code_search.startswith('ابحث في'):
                item_code_search = ''

            item_name_search = self.search_vars.get('item_name', tk.StringVar()).get().strip().lower()
            if item_name_search.startswith('ابحث في'):
                item_name_search = ''

            unit_search = self.search_vars.get('unit', tk.StringVar()).get().strip().lower()
            if unit_search.startswith('ابحث في'):
                unit_search = ''

            origin_country_search = self.search_vars.get('origin_country', tk.StringVar()).get().strip()

            price_min = self.search_vars.get('price_min', tk.StringVar()).get().strip()
            if price_min == 'من...':
                price_min = ''

            price_max = self.search_vars.get('price_max', tk.StringVar()).get().strip()
            if price_max == 'إلى...':
                price_max = ''

            # تحويل نطاق السعر
            try:
                price_min_val = float(price_min) if price_min else None
            except ValueError:
                price_min_val = None

            try:
                price_max_val = float(price_max) if price_max else None
            except ValueError:
                price_max_val = None

            # تطبيق الفلاتر
            if not any([general_search, item_code_search, item_name_search, unit_search,
                       origin_country_search, price_min_val, price_max_val]):
                self.filtered_items = self.items_data.copy()
            else:
                self.filtered_items = []
                for item in self.items_data:
                    match = True

                    # البحث العام
                    if general_search:
                        item_text = f"{item.get('item_code', '')} {item.get('item_name', '')} {item.get('description', '')}".lower()
                        if general_search not in item_text:
                            match = False

                    # البحث في كود الصنف
                    if match and item_code_search:
                        if item_code_search not in item.get('item_code', '').lower():
                            match = False

                    # البحث في اسم الصنف
                    if match and item_name_search:
                        if item_name_search not in item.get('item_name', '').lower():
                            match = False

                    # البحث في الوحدة
                    if match and unit_search:
                        if unit_search not in item.get('unit', '').lower():
                            match = False

                    # البحث في بلد المنشأ
                    if match and origin_country_search:
                        if origin_country_search != item.get('origin_country', ''):
                            match = False

                    # فلتر نطاق السعر
                    if match and (price_min_val is not None or price_max_val is not None):
                        try:
                            item_price = float(item.get('unit_price', 0))
                            if price_min_val is not None and item_price < price_min_val:
                                match = False
                            if price_max_val is not None and item_price > price_max_val:
                                match = False
                        except (ValueError, TypeError):
                            if price_min_val is not None or price_max_val is not None:
                                match = False

                    if match:
                        self.filtered_items.append(item)

            self.update_results_tree()
            self.update_results_count()

            # تحديث مؤشر حالة البحث
            if self.filtered_items:
                self.search_status_label.configure(text=f"✅ تم العثور على {len(self.filtered_items)} نتيجة")
            else:
                self.search_status_label.configure(text="❌ لا توجد نتائج مطابقة")

        except Exception as e:
            self.search_status_label.configure(text="❌ خطأ في البحث")
            messagebox.showerror("خطأ", f"خطأ في البحث: {str(e)}")

    def perform_advanced_search(self):
        """تنفيذ البحث المتقدم مع تأكيد"""
        self.perform_search()
    
    def clear_search(self):
        """مسح البحث العام فقط"""
        self.search_vars['general'].set('')
        self.perform_search()
        self.focus_search_field()

    def clear_all_search(self):
        """مسح جميع حقول البحث"""
        for var_name in self.search_vars:
            self.search_vars[var_name].set('')

        self.perform_search()
        self.focus_search_field()
        self.search_status_label.configure(text="💡 تم مسح جميع معايير البحث")
    
    def update_results_tree(self):
        """تحديث جدول النتائج"""
        try:
            # مسح البيانات الحالية
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

            # إضافة النتائج
            for i, item_data in enumerate(self.filtered_items):
                self.add_result_to_tree(item_data, i + 1)

        except Exception as e:
            print(f"خطأ في تحديث جدول النتائج: {e}")
            import traceback
            traceback.print_exc()
    
    def add_result_to_tree(self, item_data, index):
        """إضافة نتيجة للجدول المحسن"""
        try:
            # تحضير القيم مع التحقق من الوصف
            description = item_data.get('description', '')
            if len(description) > 50:
                description = description[:47] + "..."

            values = [
                item_data.get('item_code', ''),
                item_data.get('item_name', ''),
                description,
                item_data.get('unit', ''),
                f"{float(item_data.get('unit_price', 0)):,.2f}",
                item_data.get('origin_country', '')
            ]

            # تحديد لون الصف مع تحسينات
            tags = ['odd_row' if index % 2 == 1 else 'even_row']

            # إضافة تمييز للأسعار العالية
            try:
                price = float(item_data.get('unit_price', 0))
                if price > 1000:
                    tags.append('high_price')
                elif price == 0:
                    tags.append('no_price')
            except (ValueError, TypeError):
                tags.append('no_price')

            # إدراج العنصر
            item = self.results_tree.insert(
                '', 'end',
                text=str(index),
                values=values,
                tags=tags
            )

        except Exception as e:
            print(f"خطأ في إضافة نتيجة للجدول: {e}")
    
    def update_results_count(self):
        """تحديث عداد النتائج المحسن"""
        total_items = len(self.items_data)
        filtered_items = len(self.filtered_items)

        if filtered_items == total_items:
            self.results_label.configure(
                text=f"📊 إجمالي الأكواد: {total_items:,}",
                fg=COLORS['primary']
            )
        else:
            percentage = (filtered_items / total_items * 100) if total_items > 0 else 0
            self.results_label.configure(
                text=f"📊 النتائج: {filtered_items:,} من {total_items:,} ({percentage:.1f}%)",
                fg=COLORS['success'] if filtered_items > 0 else COLORS['warning']
            )
    
    def on_result_select(self, event):
        """عند اختيار نتيجة"""
        selection = self.results_tree.selection()
        if selection:
            item = selection[0]
            values = self.results_tree.item(item, 'values')
            
            if values:
                # البحث عن بيانات الصنف الكاملة
                item_code = values[0]
                for item_data in self.filtered_items:
                    if item_data.get('item_code') == item_code:
                        self.selected_code = item_code
                        self.selected_item_data = item_data
                        break
    
    def on_result_double_click(self, event):
        """عند النقر المزدوج على نتيجة"""
        self.select_item()
    
    def select_item(self):
        """اختيار الكود وملء بيانات الصنف"""
        if not self.selected_code or not self.selected_item_data:
            messagebox.showwarning("تحذير", "يرجى اختيار كود صنف من النتائج")
            return

        try:
            # ملء بيانات الصنف في النموذج
            if hasattr(self.item_dialog, 'form_vars'):
                # ملء الحقول الأساسية
                self.item_dialog.form_vars['item_code'].set(self.selected_item_data.get('item_code', ''))
                self.item_dialog.form_vars['item_name'].set(self.selected_item_data.get('item_name', ''))
                self.item_dialog.form_vars['description'].set(self.selected_item_data.get('description', ''))
                self.item_dialog.form_vars['unit'].set(self.selected_item_data.get('unit', ''))
                self.item_dialog.form_vars['unit_price'].set(self.selected_item_data.get('unit_price', '0'))

                # ملء الحقول الإضافية إذا كانت متوفرة
                if 'origin_country' in self.item_dialog.form_vars:
                    self.item_dialog.form_vars['origin_country'].set(self.selected_item_data.get('origin_country', ''))

                # حساب السعر الإجمالي
                try:
                    quantity = float(self.item_dialog.form_vars.get('quantity', tk.StringVar()).get() or 1)
                    unit_price = float(self.selected_item_data.get('unit_price', 0))
                    total_price = quantity * unit_price
                    self.item_dialog.form_vars['total_price'].set(str(total_price))
                except (ValueError, KeyError):
                    pass

                messagebox.showinfo("نجح", f"تم تحميل بيانات الصنف '{self.selected_item_data.get('item_name', '')}'")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل بيانات الصنف: {str(e)}")

        self.close_dialog()
    
    def close_dialog(self):
        """إغلاق النافذة"""
        self.root.destroy()
    
    def focus_search_field(self):
        """تركيز على حقل البحث"""
        self.general_search_entry.focus_set()
    
    def create_advanced_search_field(self, parent, label_text, var_name, width_ratio=1/3):
        """إنشاء حقل بحث متقدم"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=10)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 11, 'bold'),
            fg=COLORS['secondary']
        )
        label.pack(anchor='e', pady=(0, 3))

        # الحقل
        self.search_vars[var_name] = tk.StringVar()
        entry = create_simple_rtl_entry(
            field_frame,
            textvariable=self.search_vars[var_name],
            placeholder=f"ابحث في {label_text.replace(':', '').replace('🔢', '').replace('📦', '').replace('📏', '').strip()}..."
        )
        entry.configure(font=('Segoe UI', 10, 'normal'))
        entry.pack(fill='x', ipady=6)

        # سيتم ربط البحث الفوري لاحقاً بعد تحميل البيانات

        return entry

    def create_advanced_search_combobox(self, parent, label_text, var_name, values, width_ratio=1/2):
        """إنشاء قائمة منسدلة للبحث المتقدم"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=10)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text=label_text,
            font=('Segoe UI', 11, 'bold'),
            fg=COLORS['secondary']
        )
        label.pack(anchor='e', pady=(0, 3))

        # القائمة المنسدلة
        self.search_vars[var_name] = tk.StringVar()
        combo = create_simple_rtl_combobox(
            field_frame,
            textvariable=self.search_vars[var_name]
        )
        combo['values'] = values
        combo.set('')
        combo.configure(font=('Segoe UI', 10, 'normal'))
        combo.pack(fill='x', ipady=6)

        # سيتم ربط البحث الفوري لاحقاً بعد تحميل البيانات

        return combo

    def create_price_range_field(self, parent, width_ratio=1/2):
        """إنشاء حقل نطاق السعر"""
        field_frame = create_simple_rtl_frame(parent)
        field_frame.pack(side='right', fill='x', expand=True, padx=10)

        # التسمية
        label = create_simple_rtl_label(
            field_frame,
            text="💰 نطاق السعر:",
            font=('Segoe UI', 11, 'bold'),
            fg=COLORS['secondary']
        )
        label.pack(anchor='e', pady=(0, 3))

        # إطار النطاق
        range_frame = create_simple_rtl_frame(field_frame)
        range_frame.pack(fill='x')

        # السعر الأدنى
        self.search_vars['price_min'] = tk.StringVar()
        min_entry = create_simple_rtl_entry(
            range_frame,
            textvariable=self.search_vars['price_min'],
            placeholder="من..."
        )
        min_entry.configure(font=('Segoe UI', 10, 'normal'))
        min_entry.pack(side='right', fill='x', expand=True, padx=(0, 5), ipady=6)

        # فاصل
        separator = create_simple_rtl_label(
            range_frame,
            text="إلى",
            font=('Segoe UI', 10, 'normal')
        )
        separator.pack(side='right', padx=5)

        # السعر الأعلى
        self.search_vars['price_max'] = tk.StringVar()
        max_entry = create_simple_rtl_entry(
            range_frame,
            textvariable=self.search_vars['price_max'],
            placeholder="إلى..."
        )
        max_entry.configure(font=('Segoe UI', 10, 'normal'))
        max_entry.pack(side='right', fill='x', expand=True, padx=(5, 0), ipady=6)

        # سيتم ربط البحث الفوري لاحقاً بعد تحميل البيانات

    def refresh_data(self):
        """تحديث البيانات من قاعدة البيانات"""
        self.loading_label.configure(text="🔄 جاري تحديث البيانات...")
        self.root.update()

        try:
            self.load_items_from_database()
            self.loading_label.configure(text="✅ تم التحديث بنجاح")
            self.root.after(2000, lambda: self.loading_label.configure(text=""))
        except Exception as e:
            self.loading_label.configure(text="❌ خطأ في التحديث")
            messagebox.showerror("خطأ", f"خطأ في تحديث البيانات: {str(e)}")

    def center_window(self):
        """توسيط النافذة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

if __name__ == "__main__":
    # اختبار النافذة
    root = tk.Tk()
    root.withdraw()
    
    dialog = ItemCodeSearchDialog(root, None)
    root.mainloop()
