#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إعداد بناء الملف التنفيذي
Setup script for building executable
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    print("جاري التحقق من المتطلبات...")
    print("Checking requirements...")
    
    # التحقق من Python
    try:
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            print("خطأ: يتطلب Python 3.8 أو أحدث")
            print("Error: Python 3.8 or newer required")
            return False
        print(f"✓ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    except Exception as e:
        print(f"خطأ في التحقق من Python: {e}")
        return False
    
    # التحقق من pip
    try:
        import pip
        print("✓ pip متوفر")
        print("✓ pip available")
    except ImportError:
        print("خطأ: pip غير متوفر")
        print("Error: pip not available")
        return False
    
    return True

def install_dependencies():
    """تثبيت المكتبات المطلوبة"""
    print("\nجاري تثبيت المكتبات المطلوبة...")
    print("Installing required packages...")
    
    # قائمة المكتبات المطلوبة
    required_packages = [
        'pyinstaller>=6.0.0',
        'pandas>=1.5.0',
        'openpyxl>=3.0.0',
        'xlsxwriter>=3.0.0',
        'reportlab>=3.6.0',
        'Pillow>=9.0.0',
        'python-bidi>=0.4.2',
        'arabic-reshaper>=2.1.0',
        'tkcalendar>=1.6.0',
        'matplotlib>=3.5.0'
    ]
    
    for package in required_packages:
        try:
            print(f"تثبيت {package}...")
            print(f"Installing {package}...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                         check=True, capture_output=True, text=True)
            print(f"✓ تم تثبيت {package}")
            print(f"✓ {package} installed")
        except subprocess.CalledProcessError as e:
            print(f"تحذير: فشل في تثبيت {package}")
            print(f"Warning: Failed to install {package}")
            print(f"Error: {e.stderr}")

def prepare_build_environment():
    """إعداد بيئة البناء"""
    print("\nجاري إعداد بيئة البناء...")
    print("Preparing build environment...")
    
    # إنشاء مجلدات البناء
    build_dirs = ['build', 'dist']
    for dir_name in build_dirs:
        if os.path.exists(dir_name):
            print(f"تنظيف مجلد {dir_name}...")
            print(f"Cleaning {dir_name} directory...")
            shutil.rmtree(dir_name)
        os.makedirs(dir_name, exist_ok=True)
        print(f"✓ تم إنشاء مجلد {dir_name}")
        print(f"✓ Created {dir_name} directory")

def create_version_file():
    """إنشاء ملف معلومات الإصدار"""
    print("\nجاري إنشاء ملف معلومات الإصدار...")
    print("Creating version info file...")
    
    version_info = """# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Ship Management System'),
        StringStruct(u'FileDescription', u'نظام متابعة شحنات الموردين'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'ShipManagementSystem'),
        StringStruct(u'LegalCopyright', u'© 2024 Ship Management System'),
        StringStruct(u'OriginalFilename', u'ShipManagementSystem.exe'),
        StringStruct(u'ProductName', u'Ship Management System'),
        StringStruct(u'ProductVersion', u'*******')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)"""
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print("✓ تم إنشاء ملف معلومات الإصدار")
    print("✓ Version info file created")

def build_executable():
    """بناء الملف التنفيذي"""
    print("\nجاري بناء الملف التنفيذي...")
    print("Building executable...")
    print("هذا قد يستغرق عدة دقائق...")
    print("This may take several minutes...")
    
    # أوامر PyInstaller
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--clean',
        '--onefile',
        '--windowed',
        '--name=ShipManagementSystem',
        '--add-data=config;config',
        '--add-data=database;database',
        '--add-data=templates;templates',
        '--add-data=docs;docs',
        '--add-data=requirements.txt;.',
        '--add-data=README.md;.',
        '--add-data=دليل_المستخدم.md;.',
        '--add-data=تعليمات_التشغيل.txt;.',
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=tkinter.filedialog',
        '--hidden-import=sqlite3',
        '--hidden-import=pandas',
        '--hidden-import=openpyxl',
        '--hidden-import=xlsxwriter',
        '--hidden-import=reportlab',
        '--hidden-import=PIL',
        '--hidden-import=PIL.Image',
        '--hidden-import=PIL.ImageTk',
        '--hidden-import=arabic_reshaper',
        '--hidden-import=bidi',
        '--hidden-import=bidi.algorithm',
        '--hidden-import=tkcalendar',
        '--hidden-import=matplotlib',
        '--hidden-import=matplotlib.backends.backend_tkagg',
        '--version-file=version_info.txt',
        'main.py'
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ تم بناء الملف التنفيذي بنجاح!")
        print("✓ Executable built successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print("خطأ في بناء الملف التنفيذي:")
        print("Error building executable:")
        print(e.stderr)
        return False

def verify_build():
    """التحقق من نجاح البناء"""
    print("\nجاري التحقق من البناء...")
    print("Verifying build...")
    
    exe_path = Path('dist/ShipManagementSystem.exe')
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / (1024 * 1024)
        print(f"✓ تم العثور على الملف التنفيذي: {exe_path}")
        print(f"✓ Executable found: {exe_path}")
        print(f"✓ حجم الملف: {size_mb:.1f} MB")
        print(f"✓ File size: {size_mb:.1f} MB")
        return True
    else:
        print("✗ لم يتم العثور على الملف التنفيذي")
        print("✗ Executable not found")
        return False

def create_distribution_package():
    """إنشاء حزمة التوزيع"""
    print("\nجاري إنشاء حزمة التوزيع...")
    print("Creating distribution package...")
    
    dist_dir = Path('ShipManagementSystem_Distribution')
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    dist_dir.mkdir()
    
    # نسخ الملف التنفيذي
    exe_source = Path('dist/ShipManagementSystem.exe')
    if exe_source.exists():
        shutil.copy2(exe_source, dist_dir / 'ShipManagementSystem.exe')
    
    # نسخ الملفات المهمة
    important_files = [
        'README.md',
        'دليل_المستخدم.md',
        'تعليمات_التشغيل.txt',
        'requirements.txt'
    ]
    
    for file_name in important_files:
        if os.path.exists(file_name):
            shutil.copy2(file_name, dist_dir / file_name)
    
    # نسخ مجلدات البيانات
    data_dirs = ['database', 'docs']
    for dir_name in data_dirs:
        if os.path.exists(dir_name):
            shutil.copytree(dir_name, dist_dir / dir_name)
    
    print(f"✓ تم إنشاء حزمة التوزيع في: {dist_dir}")
    print(f"✓ Distribution package created in: {dist_dir}")

def main():
    """الدالة الرئيسية"""
    print("========================================")
    print("    بناء ملف تنفيذي لنظام متابعة الشحنات")
    print("    Building Ship Management System EXE")
    print("========================================")
    
    # التحقق من المتطلبات
    if not check_requirements():
        print("\nفشل في التحقق من المتطلبات")
        print("Requirements check failed")
        return False
    
    # تثبيت المكتبات
    install_dependencies()
    
    # إعداد بيئة البناء
    prepare_build_environment()
    
    # إنشاء ملف معلومات الإصدار
    create_version_file()
    
    # بناء الملف التنفيذي
    if not build_executable():
        print("\nفشل في بناء الملف التنفيذي")
        print("Failed to build executable")
        return False
    
    # التحقق من البناء
    if not verify_build():
        print("\nفشل في التحقق من البناء")
        print("Build verification failed")
        return False
    
    # إنشاء حزمة التوزيع
    create_distribution_package()
    
    print("\n========================================")
    print("تم الانتهاء من بناء الملف التنفيذي بنجاح!")
    print("Executable build completed successfully!")
    print("========================================")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
