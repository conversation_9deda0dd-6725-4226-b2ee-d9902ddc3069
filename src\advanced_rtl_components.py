#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مكونات واجهة المستخدم المتقدمة مع دعم RTL
Advanced RTL UI Components
"""

import tkinter as tk
from tkinter import ttk, messagebox
from src.advanced_arabic_styles import *
from datetime import datetime

class AdvancedRTLFrame(tk.Frame):
    """إطار RTL متقدم"""
    
    def __init__(self, parent, style='default', **kwargs):
        # تطبيق الأنماط الافتراضية
        default_options = {
            'bg': ADVANCED_COLORS['surface'],
            'relief': 'flat',
            'bd': 0,
        }
        default_options.update(kwargs)
        
        super().__init__(parent, **default_options)
        self.style = style
        
    def add_shadow(self, shadow_type='md'):
        """إضافة ظل للإطار"""
        # محاكاة الظل باستخدام إطارات متعددة
        shadow_frame = tk.Frame(
            self.master,
            bg=ADVANCED_COLORS['border_light'],
            height=2,
            relief='flat'
        )
        shadow_frame.place(
            in_=self,
            x=2, y=2,
            relwidth=1.0, relheight=1.0
        )
        self.lift()

class AdvancedRTLLabel(tk.Label):
    """تسمية RTL متقدمة"""
    
    def __init__(self, parent, text="", style='default', **kwargs):
        # تطبيق الأنماط الافتراضية
        default_options = {
            'bg': ADVANCED_COLORS['surface'],
            'fg': ADVANCED_COLORS['text_primary'],
            'font': ADVANCED_FONTS['body_medium'],
            'anchor': 'e',
            'justify': 'right',
        }
        
        # تطبيق أنماط مختلفة حسب النوع
        if style == 'heading':
            default_options.update({
                'font': ADVANCED_FONTS['heading_medium'],
                'fg': ADVANCED_COLORS['primary'],
            })
        elif style == 'caption':
            default_options.update({
                'font': ADVANCED_FONTS['caption'],
                'fg': ADVANCED_COLORS['text_secondary'],
            })
        elif style == 'success':
            default_options.update({
                'fg': ADVANCED_COLORS['text_success'],
            })
        elif style == 'warning':
            default_options.update({
                'fg': ADVANCED_COLORS['text_warning'],
            })
        elif style == 'error':
            default_options.update({
                'fg': ADVANCED_COLORS['text_error'],
            })
        
        default_options.update(kwargs)
        super().__init__(parent, text=text, **default_options)

class AdvancedRTLButton(tk.Button):
    """زر RTL متقدم"""
    
    def __init__(self, parent, text="", style='primary', command=None, **kwargs):
        # الحصول على نمط الزر
        button_style = ADVANCED_BUTTON_STYLES.get(style, ADVANCED_BUTTON_STYLES['primary'])
        
        # تطبيق الأنماط
        default_options = button_style.copy()
        default_options.update({
            'text': text,
            'command': command,
            'padx': ADVANCED_DIMENSIONS['padding_lg'],
            'pady': ADVANCED_DIMENSIONS['padding_sm'],
        })
        default_options.update(kwargs)
        
        super().__init__(parent, **default_options)
        
        # إضافة تأثيرات التفاعل
        self.bind('<Enter>', self._on_enter)
        self.bind('<Leave>', self._on_leave)
        self.bind('<Button-1>', self._on_click)
        self.bind('<ButtonRelease-1>', self._on_release)
        
        self.original_bg = self['bg']
        self.original_fg = self['fg']
        
    def _on_enter(self, event):
        """عند دخول المؤشر"""
        self.configure(bg=self['activebackground'])
        
    def _on_leave(self, event):
        """عند خروج المؤشر"""
        self.configure(bg=self.original_bg)
        
    def _on_click(self, event):
        """عند الضغط"""
        self.configure(relief='sunken')
        
    def _on_release(self, event):
        """عند الإفلات"""
        self.configure(relief='flat')

class AdvancedRTLEntry(tk.Entry):
    """حقل إدخال RTL متقدم"""
    
    def __init__(self, parent, placeholder="", style='default', **kwargs):
        # تطبيق الأنماط الافتراضية
        input_style = ADVANCED_INPUT_STYLES['default'].copy()
        input_style.update(kwargs)
        
        super().__init__(parent, **input_style)
        
        self.placeholder = placeholder
        self.placeholder_active = False
        
        # إعداد النص التوضيحي
        if placeholder:
            self._show_placeholder()
            
        # ربط الأحداث
        self.bind('<FocusIn>', self._on_focus_in)
        self.bind('<FocusOut>', self._on_focus_out)
        self.bind('<KeyPress>', self._on_key_press)
        
        # حفظ الألوان الأصلية
        self.original_border = self['highlightcolor']
        
    def _show_placeholder(self):
        """عرض النص التوضيحي"""
        if not self.get():
            self.placeholder_active = True
            self.insert(0, self.placeholder)
            self.configure(fg=ADVANCED_COLORS['text_muted'])
            
    def _hide_placeholder(self):
        """إخفاء النص التوضيحي"""
        if self.placeholder_active:
            self.placeholder_active = False
            self.delete(0, tk.END)
            self.configure(fg=ADVANCED_COLORS['text_primary'])
            
    def _on_focus_in(self, event):
        """عند التركيز على الحقل"""
        self._hide_placeholder()
        self.configure(highlightcolor=ADVANCED_COLORS['border_focus'])
        
    def _on_focus_out(self, event):
        """عند فقدان التركيز"""
        if not self.get():
            self._show_placeholder()
        self.configure(highlightcolor=self.original_border)
        
    def _on_key_press(self, event):
        """عند الضغط على مفتاح"""
        if self.placeholder_active:
            self._hide_placeholder()
            
    def get_value(self):
        """الحصول على القيمة الفعلية"""
        if self.placeholder_active:
            return ""
        return self.get()
        
    def set_error(self):
        """تعيين حالة خطأ"""
        self.configure(highlightcolor=ADVANCED_COLORS['error'])
        
    def set_success(self):
        """تعيين حالة نجاح"""
        self.configure(highlightcolor=ADVANCED_COLORS['success'])
        
    def clear_state(self):
        """مسح الحالة"""
        self.configure(highlightcolor=self.original_border)

class AdvancedRTLCombobox(ttk.Combobox):
    """قائمة منسدلة RTL متقدمة"""
    
    def __init__(self, parent, values=None, placeholder="", **kwargs):
        # إعداد النمط
        style = ttk.Style()
        style.configure('RTL.TCombobox',
                       fieldbackground=ADVANCED_COLORS['surface'],
                       background=ADVANCED_COLORS['surface'],
                       foreground=ADVANCED_COLORS['text_primary'],
                       bordercolor=ADVANCED_COLORS['border'],
                       lightcolor=ADVANCED_COLORS['border_light'],
                       darkcolor=ADVANCED_COLORS['border_dark'])
        
        default_options = {
            'style': 'RTL.TCombobox',
            'font': ADVANCED_FONTS['input'],
            'justify': 'right',
            'state': 'readonly',
        }
        default_options.update(kwargs)
        
        super().__init__(parent, **default_options)
        
        if values:
            self['values'] = values
            
        self.placeholder = placeholder
        if placeholder and not self.get():
            self.set(placeholder)
            
    def set_values(self, values):
        """تعيين القيم"""
        self['values'] = values
        
    def get_selected_value(self):
        """الحصول على القيمة المحددة"""
        value = self.get()
        return value if value != self.placeholder else None

class AdvancedRTLText(tk.Text):
    """منطقة نص RTL متقدمة"""
    
    def __init__(self, parent, placeholder="", **kwargs):
        # تطبيق الأنماط الافتراضية
        default_options = {
            'bg': ADVANCED_COLORS['surface'],
            'fg': ADVANCED_COLORS['text_primary'],
            'font': ADVANCED_FONTS['body_medium'],
            'relief': 'solid',
            'bd': 1,
            'wrap': tk.WORD,
            'undo': True,
            'maxundo': 20,
        }
        default_options.update(kwargs)
        
        super().__init__(parent, **default_options)
        
        self.placeholder = placeholder
        self.placeholder_active = False
        
        # إعداد النص التوضيحي
        if placeholder:
            self._show_placeholder()
            
        # ربط الأحداث
        self.bind('<FocusIn>', self._on_focus_in)
        self.bind('<FocusOut>', self._on_focus_out)
        self.bind('<KeyPress>', self._on_key_press)
        
    def _show_placeholder(self):
        """عرض النص التوضيحي"""
        if not self.get('1.0', tk.END).strip():
            self.placeholder_active = True
            self.insert('1.0', self.placeholder)
            self.configure(fg=ADVANCED_COLORS['text_muted'])
            
    def _hide_placeholder(self):
        """إخفاء النص التوضيحي"""
        if self.placeholder_active:
            self.placeholder_active = False
            self.delete('1.0', tk.END)
            self.configure(fg=ADVANCED_COLORS['text_primary'])
            
    def _on_focus_in(self, event):
        """عند التركيز"""
        self._hide_placeholder()
        
    def _on_focus_out(self, event):
        """عند فقدان التركيز"""
        if not self.get('1.0', tk.END).strip():
            self._show_placeholder()
            
    def _on_key_press(self, event):
        """عند الضغط على مفتاح"""
        if self.placeholder_active:
            self._hide_placeholder()
            
    def get_value(self):
        """الحصول على القيمة الفعلية"""
        if self.placeholder_active:
            return ""
        return self.get('1.0', tk.END).strip()

class AdvancedRTLTreeview(ttk.Treeview):
    """جدول RTL متقدم"""

    def __init__(self, parent, columns=None, **kwargs):
        # إعداد النمط
        style = ttk.Style()

        # نمط الرأس المحسن للوضوح - إصلاح مشكلة الرؤية
        style.configure('RTL.Treeview.Heading',
                       background='#0F172A',  # أسود مزرق قوي جداً
                       foreground='#FFFFFF',  # أبيض نقي 100%
                       font=('Arial', 14, 'bold'),  # خط أكبر وأوضح
                       relief='solid',  # إطار صلب واضح
                       borderwidth=3,  # حدود سميكة جداً
                       anchor='center',  # محاذاة وسط للعناوين
                       padding=(12, 10),  # مساحة داخلية كبيرة
                       focuscolor='none')  # إزالة لون التركيز المتضارب

        # نمط الحالة العادية والنشطة للعناوين
        style.map('RTL.Treeview.Heading',
                 background=[
                     ('active', '#1E293B'),  # لون أغمق عند التمرير
                     ('pressed', '#334155'),  # لون عند الضغط
                     ('focus', '#0F172A'),    # لون عند التركيز
                     ('!active', '#0F172A')   # اللون الافتراضي
                 ],
                 foreground=[
                     ('active', '#FFFFFF'),   # أبيض دائماً
                     ('pressed', '#FFFFFF'),  # أبيض دائماً
                     ('focus', '#FFFFFF'),    # أبيض دائماً
                     ('!active', '#FFFFFF')   # أبيض دائماً
                 ],
                 relief=[
                     ('active', 'solid'),
                     ('pressed', 'solid'),
                     ('!active', 'solid')
                 ])

        # نمط الصفوف
        style.configure('RTL.Treeview',
                       background=ADVANCED_COLORS['surface'],
                       foreground=ADVANCED_COLORS['text_primary'],
                       font=ADVANCED_FONTS['table_cell'],
                       fieldbackground=ADVANCED_COLORS['surface'],
                       bordercolor=ADVANCED_COLORS['border'],
                       lightcolor=ADVANCED_COLORS['border_light'],
                       darkcolor=ADVANCED_COLORS['border_dark'])

        # نمط التحديد
        style.map('RTL.Treeview',
                 background=[('selected', ADVANCED_COLORS['surface_selected'])],
                 foreground=[('selected', ADVANCED_COLORS['text_primary'])])

        default_options = {
            'style': 'RTL.Treeview',
            'show': 'tree headings',
            'selectmode': 'extended',
        }

        if columns:
            default_options['columns'] = columns

        default_options.update(kwargs)

        super().__init__(parent, **default_options)

        # إعداد الأعمدة إذا تم تمريرها
        if columns:
            self.setup_columns(columns)

        # ربط الأحداث
        self.bind('<Button-1>', self._on_click)
        self.bind('<Double-1>', self._on_double_click)

    def setup_columns(self, columns):
        """إعداد الأعمدة"""
        # إخفاء العمود الأول
        self.column('#0', width=0, stretch=False)
        self.heading('#0', text='')

        # إعداد الأعمدة
        for i, col in enumerate(columns):
            col_id = f'#{i+1}'
            self.column(col_id, anchor='e', width=150)
            self.heading(col_id, text=col, anchor='e')

    def insert_rtl_row(self, values, **kwargs):
        """إدراج صف مع دعم RTL"""
        return self.insert('', 'end', values=values, **kwargs)

    def _on_click(self, event):
        """عند النقر"""
        pass

    def _on_double_click(self, event):
        """عند النقر المزدوج"""
        pass

# وظائف مساعدة لإنشاء المكونات المتقدمة
def create_advanced_rtl_frame(parent, style='default', **kwargs):
    """إنشاء إطار RTL متقدم"""
    return AdvancedRTLFrame(parent, style=style, **kwargs)

def create_advanced_rtl_label(parent, text="", style='default', **kwargs):
    """إنشاء تسمية RTL متقدمة"""
    return AdvancedRTLLabel(parent, text=text, style=style, **kwargs)

def create_advanced_rtl_button(parent, text="", style='primary', command=None, **kwargs):
    """إنشاء زر RTL متقدم"""
    return AdvancedRTLButton(parent, text=text, style=style, command=command, **kwargs)

def create_advanced_rtl_entry(parent, placeholder="", style='default', **kwargs):
    """إنشاء حقل إدخال RTL متقدم"""
    return AdvancedRTLEntry(parent, placeholder=placeholder, style=style, **kwargs)

def create_advanced_rtl_combobox(parent, values=None, placeholder="", **kwargs):
    """إنشاء قائمة منسدلة RTL متقدمة"""
    return AdvancedRTLCombobox(parent, values=values, placeholder=placeholder, **kwargs)

def create_advanced_rtl_text(parent, placeholder="", **kwargs):
    """إنشاء منطقة نص RTL متقدمة"""
    return AdvancedRTLText(parent, placeholder=placeholder, **kwargs)

def create_advanced_rtl_treeview(parent, columns=None, **kwargs):
    """إنشاء جدول RTL متقدم"""
    return AdvancedRTLTreeview(parent, columns=columns, **kwargs)
