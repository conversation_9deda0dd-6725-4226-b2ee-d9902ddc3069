#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة قاعدة البيانات
Database Management System
"""

import sqlite3
import os
import hashlib
from datetime import datetime
import logging

class DatabaseManager:
    def __init__(self, db_path="database/shipment_system.db"):
        """تهيئة مدير قاعدة البيانات"""
        self.db_path = db_path
        self.ensure_database_directory()
        self.init_database()
        
    def ensure_database_directory(self):
        """التأكد من وجود مجلد قاعدة البيانات"""
        db_dir = os.path.dirname(self.db_path)
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        return conn
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # جدول المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    email TEXT,
                    phone TEXT,
                    role TEXT NOT NULL DEFAULT 'employee',
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            ''')
            
            # جدول الشركة والإعدادات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS company_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    company_name TEXT NOT NULL,
                    company_logo BLOB,
                    address TEXT,
                    phone TEXT,
                    email TEXT,
                    tax_number TEXT,
                    fiscal_year_start DATE,
                    fiscal_year_end DATE,
                    currency TEXT DEFAULT 'SAR',
                    language TEXT DEFAULT 'ar',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول الموردين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS suppliers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    supplier_code TEXT UNIQUE NOT NULL,
                    supplier_name TEXT NOT NULL,
                    contact_person TEXT,
                    email TEXT,
                    phone TEXT,
                    mobile TEXT,
                    address TEXT,
                    city TEXT,
                    country TEXT,
                    tax_number TEXT,
                    payment_terms TEXT,
                    credit_limit DECIMAL(15,2) DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول فئات الأصناف
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS item_categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category_name TEXT NOT NULL,
                    parent_category_id INTEGER,
                    description TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (parent_category_id) REFERENCES item_categories (id)
                )
            ''')
            
            # جدول الأصناف
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_code TEXT UNIQUE NOT NULL,
                    item_name TEXT NOT NULL,
                    item_name_en TEXT,
                    category_id INTEGER,
                    unit_of_measure TEXT,
                    cost_price DECIMAL(15,2) DEFAULT 0,
                    selling_price DECIMAL(15,2) DEFAULT 0,
                    min_stock_level INTEGER DEFAULT 0,
                    max_stock_level INTEGER DEFAULT 0,
                    current_stock INTEGER DEFAULT 0,
                    reorder_point INTEGER DEFAULT 0,
                    barcode TEXT,
                    description TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES item_categories (id)
                )
            ''')
            
            # جدول المخازن
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS warehouses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    warehouse_code TEXT UNIQUE NOT NULL,
                    warehouse_name TEXT NOT NULL,
                    location TEXT,
                    manager_name TEXT,
                    phone TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول الشحنات المحدث والشامل
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS shipments (
                    id TEXT PRIMARY KEY,
                    shipment_number TEXT UNIQUE NOT NULL,
                    supplier_id INTEGER NOT NULL,
                    status TEXT DEFAULT 'في الانتظار',
                    shipment_date DATE,
                    expected_arrival_date DATE,
                    actual_departure_date DATE,
                    actual_arrival_date DATE,
                    departure_port TEXT,
                    arrival_port TEXT,
                    shipping_company TEXT,
                    container_number TEXT,
                    bill_of_lading TEXT,
                    container_type TEXT,
                    seal_number TEXT,
                    weight REAL,
                    volume REAL,
                    pieces_count INTEGER,
                    net_weight REAL,
                    currency TEXT DEFAULT 'USD',
                    total_value REAL DEFAULT 0,
                    shipping_cost REAL DEFAULT 0,
                    additional_fees REAL DEFAULT 0,
                    insurance_cost REAL DEFAULT 0,
                    customs_fees REAL DEFAULT 0,
                    handling_fees REAL DEFAULT 0,
                    paid_amount REAL DEFAULT 0,
                    payment_method TEXT,
                    payment_status TEXT DEFAULT 'لم يتم الدفع',
                    tracking_number TEXT,
                    current_location TEXT,
                    goods_description TEXT,

                    -- المستندات
                    commercial_invoice TEXT,
                    commercial_invoice_status TEXT DEFAULT 'غير متوفر',
                    packing_list TEXT,
                    packing_list_status TEXT DEFAULT 'غير متوفر',
                    certificate_of_origin TEXT,
                    certificate_of_origin_status TEXT DEFAULT 'غير متوفر',
                    insurance_policy TEXT,
                    insurance_policy_status TEXT DEFAULT 'غير متوفر',
                    quality_certificate TEXT,
                    quality_certificate_status TEXT DEFAULT 'غير متوفر',
                    health_certificate TEXT,
                    health_certificate_status TEXT DEFAULT 'غير متوفر',
                    customs_declaration TEXT,
                    customs_declaration_status TEXT DEFAULT 'غير متوفر',
                    import_license TEXT,
                    import_license_status TEXT DEFAULT 'غير متوفر',
                    inspection_certificate TEXT,
                    inspection_certificate_status TEXT DEFAULT 'غير متوفر',
                    other_documents TEXT,
                    documents_status TEXT DEFAULT 'غير مكتملة',
                    documents_notes TEXT,

                    notes TEXT,
                    attachments TEXT,
                    created_by TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
                )
            ''')

            # تحديث جدول الشحنات إذا كان موجوداً بالهيكل القديم
            try:
                # إضافة الأعمدة الجديدة إذا لم تكن موجودة
                new_columns = [
                    ('container_type', 'TEXT'),
                    ('weight', 'REAL'),
                    ('volume', 'REAL'),
                    ('total_value', 'REAL DEFAULT 0'),
                    ('shipping_cost', 'REAL DEFAULT 0'),
                    ('additional_fees', 'REAL DEFAULT 0'),
                    ('insurance_cost', 'REAL DEFAULT 0'),
                    ('customs_fees', 'REAL DEFAULT 0'),
                    ('payment_method', 'TEXT'),
                    ('payment_status', 'TEXT DEFAULT "لم يتم الدفع"')
                ]

                for column_name, column_type in new_columns:
                    try:
                        cursor.execute(f'ALTER TABLE shipments ADD COLUMN {column_name} {column_type}')
                    except sqlite3.OperationalError:
                        # العمود موجود بالفعل
                        pass

            except Exception as e:
                logging.warning(f"تحذير في تحديث جدول الشحنات: {e}")

            # جدول الشحنات المتقدم (advanced_shipments)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS advanced_shipments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    shipment_number TEXT UNIQUE NOT NULL,
                    customer_name TEXT NOT NULL,
                    supplier_name TEXT NOT NULL,
                    supplier_invoice_number TEXT,
                    shipment_date DATE,
                    expected_arrival_date DATE,
                    actual_departure_date DATE,
                    actual_arrival_date DATE,
                    departure_port TEXT,
                    arrival_port TEXT,
                    shipping_company TEXT,
                    container_number TEXT,
                    container_count INTEGER DEFAULT 1,
                    container_numbers_json TEXT,
                    bill_of_lading TEXT,
                    container_type TEXT,
                    seal_number TEXT,
                    weight REAL,
                    volume REAL,
                    pieces_count INTEGER,
                    net_weight REAL,
                    currency TEXT DEFAULT 'USD',
                    total_value REAL DEFAULT 0,
                    shipping_cost REAL DEFAULT 0,
                    additional_fees REAL DEFAULT 0,
                    insurance_cost REAL DEFAULT 0,
                    customs_fees REAL DEFAULT 0,
                    handling_fees REAL DEFAULT 0,
                    paid_amount REAL DEFAULT 0,
                    payment_method TEXT,
                    payment_status TEXT DEFAULT 'لم يتم الدفع',
                    tracking_number TEXT,
                    current_location TEXT,
                    goods_description TEXT,
                    release_status TEXT DEFAULT 'بدون افراج',
                    status TEXT DEFAULT 'قيد التحضير',

                    -- المستندات
                    commercial_invoice TEXT,
                    commercial_invoice_status TEXT DEFAULT 'غير متوفر',
                    packing_list TEXT,
                    packing_list_status TEXT DEFAULT 'غير متوفر',
                    certificate_of_origin TEXT,
                    certificate_of_origin_status TEXT DEFAULT 'غير متوفر',
                    insurance_policy TEXT,
                    insurance_policy_status TEXT DEFAULT 'غير متوفر',
                    quality_certificate TEXT,
                    quality_certificate_status TEXT DEFAULT 'غير متوفر',
                    health_certificate TEXT,
                    health_certificate_status TEXT DEFAULT 'غير متوفر',
                    customs_declaration TEXT,
                    customs_declaration_status TEXT DEFAULT 'غير متوفر',
                    import_license TEXT,
                    import_license_status TEXT DEFAULT 'غير متوفر',
                    inspection_certificate TEXT,
                    inspection_certificate_status TEXT DEFAULT 'غير متوفر',
                    other_documents TEXT,
                    documents_status TEXT DEFAULT 'غير مكتملة',
                    documents_notes TEXT,

                    notes TEXT,
                    attachments TEXT,
                    created_by TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول تفاصيل الشحنات المحدث
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS shipment_items (
                    id TEXT PRIMARY KEY,
                    shipment_id TEXT NOT NULL,
                    item_code TEXT,
                    item_name TEXT NOT NULL,
                    item_id INTEGER,
                    warehouse_id INTEGER,
                    quantity REAL NOT NULL,
                    unit TEXT,
                    unit_price REAL,
                    total_price REAL,
                    received_quantity REAL DEFAULT 0,
                    notes TEXT,
                    FOREIGN KEY (shipment_id) REFERENCES shipments (id) ON DELETE CASCADE,
                    FOREIGN KEY (item_id) REFERENCES items (id),
                    FOREIGN KEY (warehouse_id) REFERENCES warehouses (id)
                )
            ''')

            # تحديث جدول أصناف الشحنة إذا كان موجوداً بالهيكل القديم
            try:
                # إضافة الأعمدة الجديدة إذا لم تكن موجودة
                new_item_columns = [
                    ('item_code', 'TEXT'),
                    ('item_name', 'TEXT'),
                    ('item_id', 'INTEGER'),
                    ('warehouse_id', 'INTEGER'),
                    ('unit', 'TEXT'),
                    ('total_price', 'REAL'),
                    ('description', 'TEXT'),
                    ('weight', 'REAL DEFAULT 0'),
                    ('volume', 'REAL DEFAULT 0'),
                    ('hs_code', 'TEXT'),
                    ('origin_country', 'TEXT'),
                    ('created_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'),
                    ('updated_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
                ]

                for column_name, column_type in new_item_columns:
                    try:
                        cursor.execute(f'ALTER TABLE shipment_items ADD COLUMN {column_name} {column_type}')
                    except sqlite3.OperationalError:
                        # العمود موجود بالفعل
                        pass

            except Exception as e:
                logging.warning(f"تحذير في تحديث جدول أصناف الشحنة: {e}")
            
            # جدول حالات الشحنة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS shipment_status_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    shipment_id INTEGER NOT NULL,
                    status TEXT NOT NULL,
                    status_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    notes TEXT,
                    updated_by INTEGER,
                    FOREIGN KEY (shipment_id) REFERENCES shipments (id) ON DELETE CASCADE,
                    FOREIGN KEY (updated_by) REFERENCES users (id)
                )
            ''')
            
            # جدول المخزون
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS inventory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_id INTEGER NOT NULL,
                    warehouse_id INTEGER NOT NULL,
                    quantity INTEGER DEFAULT 0,
                    reserved_quantity INTEGER DEFAULT 0,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (item_id) REFERENCES items (id),
                    FOREIGN KEY (warehouse_id) REFERENCES warehouses (id),
                    UNIQUE(item_id, warehouse_id)
                )
            ''')
            
            # جدول حركات المخزون
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS inventory_movements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_id INTEGER NOT NULL,
                    warehouse_id INTEGER NOT NULL,
                    movement_type TEXT NOT NULL, -- 'in', 'out', 'transfer'
                    quantity INTEGER NOT NULL,
                    reference_type TEXT, -- 'shipment', 'adjustment', 'transfer'
                    reference_id INTEGER,
                    notes TEXT,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (item_id) REFERENCES items (id),
                    FOREIGN KEY (warehouse_id) REFERENCES warehouses (id),
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            ''')
            
            conn.commit()
            
            # إنشاء المستخدم الافتراضي (admin)
            self.create_default_admin()

            # إنشاء إعدادات الشركة الافتراضية
            self.create_default_company_settings()

            # إنشاء بيانات تجريبية
            self.create_sample_data()
            
        except Exception as e:
            conn.rollback()
            logging.error(f"خطأ في إنشاء قاعدة البيانات: {e}")
            raise
        finally:
            conn.close()
    
    def create_default_admin(self):
        """إنشاء مستخدم المدير الافتراضي"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # التحقق من وجود مستخدم admin
            cursor.execute("SELECT id FROM users WHERE username = 'admin'")
            if cursor.fetchone() is None:
                # إنشاء كلمة مرور مشفرة
                password_hash = hashlib.sha256("admin123".encode()).hexdigest()
                
                cursor.execute('''
                    INSERT INTO users (username, password_hash, full_name, role, is_active)
                    VALUES (?, ?, ?, ?, ?)
                ''', ("admin", password_hash, "مدير النظام", "admin", 1))
                
                conn.commit()
                logging.info("تم إنشاء مستخدم المدير الافتراضي")
                
        except Exception as e:
            conn.rollback()
            logging.error(f"خطأ في إنشاء المستخدم الافتراضي: {e}")
        finally:
            conn.close()
    
    def create_default_company_settings(self):
        """إنشاء إعدادات الشركة الافتراضية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # التحقق من وجود إعدادات الشركة
            cursor.execute("SELECT id FROM company_settings LIMIT 1")
            if cursor.fetchone() is None:
                cursor.execute('''
                    INSERT INTO company_settings (
                        company_name, address, phone, email, currency, language,
                        fiscal_year_start, fiscal_year_end
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    "شركة متابعة الشحنات",
                    "المملكة العربية السعودية",
                    "+966-11-1234567",
                    "<EMAIL>",
                    "SAR",
                    "ar",
                    "2024-01-01",
                    "2024-12-31"
                ))
                
                conn.commit()
                logging.info("تم إنشاء إعدادات الشركة الافتراضية")
                
        except Exception as e:
            conn.rollback()
            logging.error(f"خطأ في إنشاء إعدادات الشركة: {e}")
        finally:
            conn.close()
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()

    def verify_password(self, password, password_hash):
        """التحقق من كلمة المرور"""
        return self.hash_password(password) == password_hash

    def execute_query(self, query, params=None):
        """تنفيذ استعلام (INSERT, UPDATE, DELETE)"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            conn.commit()
            return cursor.rowcount

        except Exception as e:
            conn.rollback()
            logging.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise
        finally:
            conn.close()

    def fetch_one(self, query, params=None):
        """جلب سجل واحد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            result = cursor.fetchone()
            return dict(result) if result else None

        except Exception as e:
            logging.error(f"خطأ في جلب البيانات: {e}")
            raise
        finally:
            conn.close()

    def fetch_all(self, query, params=None):
        """جلب جميع السجلات"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)

            results = cursor.fetchall()
            return [dict(row) for row in results]

        except Exception as e:
            logging.error(f"خطأ في جلب البيانات: {e}")
            raise
        finally:
            conn.close()

    def get_table_info(self, table_name):
        """الحصول على معلومات الجدول"""
        query = f"PRAGMA table_info({table_name})"
        return self.fetch_all(query)

    def table_exists(self, table_name):
        """التحقق من وجود الجدول"""
        query = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
        result = self.fetch_one(query, (table_name,))
        return result is not None

    def get_last_insert_id(self):
        """الحصول على آخر معرف مدرج"""
        conn = self.get_connection()
        try:
            return conn.lastrowid
        finally:
            conn.close()

    def create_sample_data(self):
        """إنشاء بيانات تجريبية للاختبار"""
        try:
            # إنشاء موردين تجريبيين
            self.create_sample_suppliers()

            # إنشاء مخازن تجريبية
            self.create_sample_warehouses()

            # إنشاء فئات الأصناف التجريبية
            self.create_sample_categories()

            # إنشاء أصناف تجريبية
            self.create_sample_items()

            # إنشاء شحنات تجريبية
            self.create_sample_shipments()

        except Exception as e:
            logging.warning(f"تحذير في إنشاء البيانات التجريبية: {e}")

    def create_sample_suppliers(self):
        """إنشاء موردين تجريبيين"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود موردين
            cursor.execute("SELECT COUNT(*) as count FROM suppliers")
            result = cursor.fetchone()

            if result['count'] == 0:
                suppliers = [
                    (1, 'SUP001', 'شركة التجارة الدولية', 'الرياض', 'أحمد محمد', '+966501234567', '<EMAIL>', 1),
                    (2, 'SUP002', 'مؤسسة الشحن السريع', 'جدة', 'سارة أحمد', '+966502345678', '<EMAIL>', 1),
                    (3, 'SUP003', 'شركة الخليج للاستيراد', 'الدمام', 'محمد علي', '+966503456789', '<EMAIL>', 1)
                ]

                for supplier in suppliers:
                    cursor.execute('''
                        INSERT OR IGNORE INTO suppliers
                        (id, supplier_code, supplier_name, address, contact_person, phone, email, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', supplier)

                conn.commit()
                logging.info("تم إنشاء الموردين التجريبيين")

        except Exception as e:
            conn.rollback()
            logging.error(f"خطأ في إنشاء الموردين التجريبيين: {e}")
        finally:
            conn.close()

    def create_sample_warehouses(self):
        """إنشاء مخازن تجريبية"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود مخازن
            cursor.execute("SELECT COUNT(*) as count FROM warehouses")
            result = cursor.fetchone()

            if result['count'] == 0:
                warehouses = [
                    ('WH001', 'المخزن الرئيسي', 'الرياض - حي الصناعية', 'أحمد محمد', '+966501234567', 1),
                    ('WH002', 'مخزن جدة', 'جدة - المنطقة الصناعية الثانية', 'سارة أحمد', '+966502345678', 1),
                    ('WH003', 'مخزن الدمام', 'الدمام - المنطقة الصناعية الأولى', 'محمد علي', '+966503456789', 1),
                    ('WH004', 'مخزن الطائف', 'الطائف - المنطقة الصناعية', 'فاطمة حسن', '+966504567890', 1),
                    ('WH005', 'مخزن المدينة', 'المدينة المنورة - المنطقة الصناعية', 'عبدالله أحمد', '+966505678901', 0)
                ]

                for warehouse in warehouses:
                    cursor.execute('''
                        INSERT OR IGNORE INTO warehouses
                        (warehouse_code, warehouse_name, location, manager_name, phone, is_active)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', warehouse)

                conn.commit()
                logging.info("تم إنشاء المخازن التجريبية")

        except Exception as e:
            conn.rollback()
            logging.error(f"خطأ في إنشاء المخازن التجريبية: {e}")
        finally:
            conn.close()

    def create_sample_categories(self):
        """إنشاء فئات الأصناف التجريبية"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود فئات
            cursor.execute("SELECT COUNT(*) as count FROM item_categories")
            result = cursor.fetchone()

            if result['count'] == 0:
                categories = [
                    ('إلكترونيات', 'أجهزة إلكترونية ومعدات تقنية', 1),
                    ('ملابس', 'ملابس رجالية ونسائية وأطفال', 1),
                    ('أغذية', 'مواد غذائية ومشروبات', 1),
                    ('أدوات منزلية', 'أدوات وأجهزة منزلية', 1),
                    ('كتب ومكتبة', 'كتب وقرطاسية ومواد تعليمية', 1),
                    ('رياضة', 'معدات وأدوات رياضية', 1),
                    ('تجميل', 'مستحضرات تجميل وعناية شخصية', 1),
                    ('ألعاب', 'ألعاب أطفال وترفيه', 1)
                ]

                for category in categories:
                    cursor.execute('''
                        INSERT OR IGNORE INTO item_categories
                        (category_name, description, is_active)
                        VALUES (?, ?, ?)
                    ''', category)

                conn.commit()
                logging.info("تم إنشاء فئات الأصناف التجريبية")

        except Exception as e:
            conn.rollback()
            logging.error(f"خطأ في إنشاء فئات الأصناف التجريبية: {e}")
        finally:
            conn.close()

    def create_sample_items(self):
        """إنشاء أصناف تجريبية"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود أصناف
            cursor.execute("SELECT COUNT(*) as count FROM items")
            result = cursor.fetchone()

            if result['count'] == 0:
                # الحصول على معرفات الفئات
                cursor.execute("SELECT id, category_name FROM item_categories")
                categories = {cat['category_name']: cat['id'] for cat in cursor.fetchall()}

                items = [
                    ('ELEC001', 'لابتوب ديل XPS 13', categories.get('إلكترونيات'), 'قطعة', 3500.00, 4200.00, 15, 5, 'لابتوب عالي الأداء للأعمال', 1),
                    ('ELEC002', 'هاتف آيفون 14', categories.get('إلكترونيات'), 'قطعة', 3800.00, 4500.00, 25, 10, 'هاتف ذكي متطور', 1),
                    ('ELEC003', 'سماعات بلوتوث', categories.get('إلكترونيات'), 'قطعة', 150.00, 220.00, 50, 15, 'سماعات لاسلكية عالية الجودة', 1),

                    ('CLTH001', 'قميص رجالي قطني', categories.get('ملابس'), 'قطعة', 45.00, 75.00, 100, 20, 'قميص قطني مريح', 1),
                    ('CLTH002', 'فستان نسائي صيفي', categories.get('ملابس'), 'قطعة', 80.00, 150.00, 60, 15, 'فستان أنيق للصيف', 1),
                    ('CLTH003', 'حذاء رياضي', categories.get('ملابس'), 'زوج', 120.00, 200.00, 40, 10, 'حذاء رياضي مريح', 1),

                    ('FOOD001', 'أرز بسمتي', categories.get('أغذية'), 'كيس 5 كيلو', 25.00, 35.00, 200, 50, 'أرز بسمتي فاخر', 1),
                    ('FOOD002', 'زيت زيتون', categories.get('أغذية'), 'زجاجة 500 مل', 35.00, 55.00, 80, 20, 'زيت زيتون بكر ممتاز', 1),
                    ('FOOD003', 'عسل طبيعي', categories.get('أغذية'), 'برطمان 1 كيلو', 60.00, 95.00, 30, 10, 'عسل طبيعي صافي', 1),

                    ('HOME001', 'مكنسة كهربائية', categories.get('أدوات منزلية'), 'قطعة', 250.00, 380.00, 20, 5, 'مكنسة كهربائية قوية', 1),
                    ('HOME002', 'طقم أواني طبخ', categories.get('أدوات منزلية'), 'طقم', 180.00, 280.00, 15, 5, 'طقم أواني من الستانلس ستيل', 1),

                    ('BOOK001', 'كتاب البرمجة بـ Python', categories.get('كتب ومكتبة'), 'قطعة', 45.00, 75.00, 50, 10, 'كتاب تعليمي للبرمجة', 1),
                    ('BOOK002', 'دفتر ملاحظات A4', categories.get('كتب ومكتبة'), 'قطعة', 8.00, 15.00, 200, 50, 'دفتر ملاحظات عالي الجودة', 1),

                    ('SPRT001', 'كرة قدم', categories.get('رياضة'), 'قطعة', 35.00, 60.00, 30, 10, 'كرة قدم احترافية', 1),
                    ('SPRT002', 'دراجة هوائية', categories.get('رياضة'), 'قطعة', 800.00, 1200.00, 8, 2, 'دراجة هوائية للكبار', 1),

                    ('BEAU001', 'كريم مرطب للوجه', categories.get('تجميل'), 'أنبوب 50 مل', 25.00, 45.00, 60, 15, 'كريم مرطب طبيعي', 1),
                    ('BEAU002', 'شامبو للشعر', categories.get('تجميل'), 'زجاجة 400 مل', 18.00, 32.00, 80, 20, 'شامبو مغذي للشعر', 1),

                    ('TOYS001', 'لعبة تركيب للأطفال', categories.get('ألعاب'), 'علبة', 40.00, 70.00, 25, 8, 'لعبة تعليمية للأطفال', 1),
                    ('TOYS002', 'دمية للأطفال', categories.get('ألعاب'), 'قطعة', 30.00, 55.00, 35, 10, 'دمية ناعمة وآمنة', 1)
                ]

                for item in items:
                    cursor.execute('''
                        INSERT OR IGNORE INTO items
                        (item_code, item_name, category_id, unit_of_measure, cost_price,
                         selling_price, current_stock, min_stock_level, description, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', item)

                conn.commit()
                logging.info("تم إنشاء الأصناف التجريبية")

        except Exception as e:
            conn.rollback()
            logging.error(f"خطأ في إنشاء الأصناف التجريبية: {e}")
        finally:
            conn.close()

    def create_sample_shipments(self):
        """إنشاء شحنات تجريبية"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود شحنات
            cursor.execute("SELECT COUNT(*) as count FROM shipments")
            result = cursor.fetchone()

            if result['count'] == 0:
                from datetime import datetime, timedelta
                import uuid

                shipments = [
                    {
                        'id': str(uuid.uuid4()),
                        'shipment_number': 'SH-000001',
                        'supplier_id': 1,
                        'status': 'في الطريق',
                        'shipment_date': (datetime.now() - timedelta(days=10)).strftime('%Y-%m-%d'),
                        'expected_arrival_date': (datetime.now() + timedelta(days=5)).strftime('%Y-%m-%d'),
                        'departure_port': 'شنغهاي',
                        'arrival_port': 'جدة',
                        'shipping_company': 'أرامكس',
                        'container_number': 'CONT001234',
                        'bill_of_lading': 'BL001234',
                        'container_type': '40 قدم عادية',
                        'weight': 15000.0,
                        'volume': 67.5,
                        'currency': 'USD',
                        'total_value': 50000.0,
                        'shipping_cost': 3000.0,
                        'payment_status': 'تم الدفع بالكامل',
                        'notes': 'شحنة مواد إلكترونية',
                        'created_by': 'admin'
                    },
                    {
                        'id': str(uuid.uuid4()),
                        'shipment_number': 'SH-000002',
                        'supplier_id': 2,
                        'status': 'وصلت',
                        'shipment_date': (datetime.now() - timedelta(days=20)).strftime('%Y-%m-%d'),
                        'expected_arrival_date': (datetime.now() - timedelta(days=5)).strftime('%Y-%m-%d'),
                        'departure_port': 'دبي',
                        'arrival_port': 'الدمام',
                        'shipping_company': 'DHL',
                        'container_number': 'CONT005678',
                        'bill_of_lading': 'BL005678',
                        'container_type': '20 قدم عادية',
                        'weight': 8000.0,
                        'volume': 33.2,
                        'currency': 'SAR',
                        'total_value': 75000.0,
                        'shipping_cost': 2000.0,
                        'payment_status': 'دفع جزئي',
                        'notes': 'شحنة قطع غيار',
                        'created_by': 'admin'
                    },
                    {
                        'id': str(uuid.uuid4()),
                        'shipment_number': 'SH-000003',
                        'supplier_id': 3,
                        'status': 'في الانتظار',
                        'shipment_date': datetime.now().strftime('%Y-%m-%d'),
                        'expected_arrival_date': (datetime.now() + timedelta(days=15)).strftime('%Y-%m-%d'),
                        'departure_port': 'الرياض',
                        'arrival_port': 'جدة',
                        'shipping_company': 'البحري السعودي',
                        'container_number': 'CONT009876',
                        'bill_of_lading': 'BL009876',
                        'container_type': 'مبردة 40 قدم',
                        'weight': 12000.0,
                        'volume': 55.8,
                        'currency': 'USD',
                        'total_value': 30000.0,
                        'shipping_cost': 2500.0,
                        'payment_status': 'لم يتم الدفع',
                        'notes': 'شحنة مواد غذائية مبردة',
                        'created_by': 'admin'
                    }
                ]

                for shipment in shipments:
                    cursor.execute('''
                        INSERT OR IGNORE INTO shipments
                        (shipment_number, supplier_id, status, shipment_date, expected_arrival_date,
                         departure_port, arrival_port, shipping_company, container_number, bill_of_lading,
                         container_type, weight, volume, currency, total_value, shipping_cost,
                         payment_status, notes, created_by, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        shipment['shipment_number'], shipment['supplier_id'],
                        shipment['status'], shipment['shipment_date'], shipment['expected_arrival_date'],
                        shipment['departure_port'], shipment['arrival_port'], shipment['shipping_company'],
                        shipment['container_number'], shipment['bill_of_lading'], shipment['container_type'],
                        shipment['weight'], shipment['volume'], shipment['currency'], shipment['total_value'],
                        shipment['shipping_cost'], shipment['payment_status'], shipment['notes'],
                        shipment['created_by'], datetime.now().isoformat(), datetime.now().isoformat()
                    ))

                conn.commit()
                logging.info("تم إنشاء الشحنات التجريبية")

        except Exception as e:
            conn.rollback()
            logging.error(f"خطأ في إنشاء الشحنات التجريبية: {e}")
        finally:
            conn.close()

    def update_inventory_from_shipment(self, shipment_id, action='add'):
        """تحديث المخزون من الشحنة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # جلب أصناف الشحنة
            cursor.execute('''
                SELECT item_id, warehouse_id, quantity, received_quantity
                FROM shipment_items
                WHERE shipment_id = ? AND item_id IS NOT NULL AND warehouse_id IS NOT NULL
            ''', (shipment_id,))

            shipment_items = cursor.fetchall()

            for item in shipment_items:
                item_id = item['item_id']
                warehouse_id = item['warehouse_id']
                quantity = item['received_quantity'] or item['quantity']

                if action == 'add':
                    # إضافة للمخزون
                    self.add_to_inventory(item_id, warehouse_id, quantity, 'shipment', shipment_id)
                elif action == 'remove':
                    # خصم من المخزون
                    self.remove_from_inventory(item_id, warehouse_id, quantity, 'shipment', shipment_id)

            conn.commit()

        except Exception as e:
            conn.rollback()
            logging.error(f"خطأ في تحديث المخزون من الشحنة: {e}")
            raise
        finally:
            conn.close()

    def add_to_inventory(self, item_id, warehouse_id, quantity, reference_type=None, reference_id=None):
        """إضافة كمية للمخزون"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود سجل المخزون
            cursor.execute('''
                SELECT quantity FROM inventory
                WHERE item_id = ? AND warehouse_id = ?
            ''', (item_id, warehouse_id))

            existing = cursor.fetchone()

            if existing:
                # تحديث الكمية الموجودة
                new_quantity = existing['quantity'] + quantity
                cursor.execute('''
                    UPDATE inventory
                    SET quantity = ?, last_updated = CURRENT_TIMESTAMP
                    WHERE item_id = ? AND warehouse_id = ?
                ''', (new_quantity, item_id, warehouse_id))
            else:
                # إنشاء سجل جديد
                cursor.execute('''
                    INSERT INTO inventory (item_id, warehouse_id, quantity)
                    VALUES (?, ?, ?)
                ''', (item_id, warehouse_id, quantity))

            # تسجيل حركة المخزون
            cursor.execute('''
                INSERT INTO inventory_movements
                (item_id, warehouse_id, movement_type, quantity, reference_type, reference_id)
                VALUES (?, ?, 'in', ?, ?, ?)
            ''', (item_id, warehouse_id, quantity, reference_type, reference_id))

            conn.commit()

        except Exception as e:
            conn.rollback()
            logging.error(f"خطأ في إضافة للمخزون: {e}")
            raise
        finally:
            conn.close()

    def remove_from_inventory(self, item_id, warehouse_id, quantity, reference_type=None, reference_id=None):
        """خصم كمية من المخزون"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود سجل المخزون والكمية المتاحة
            cursor.execute('''
                SELECT quantity FROM inventory
                WHERE item_id = ? AND warehouse_id = ?
            ''', (item_id, warehouse_id))

            existing = cursor.fetchone()

            if existing:
                new_quantity = max(0, existing['quantity'] - quantity)
                cursor.execute('''
                    UPDATE inventory
                    SET quantity = ?, last_updated = CURRENT_TIMESTAMP
                    WHERE item_id = ? AND warehouse_id = ?
                ''', (new_quantity, item_id, warehouse_id))

                # تسجيل حركة المخزون
                cursor.execute('''
                    INSERT INTO inventory_movements
                    (item_id, warehouse_id, movement_type, quantity, reference_type, reference_id)
                    VALUES (?, ?, 'out', ?, ?, ?)
                ''', (item_id, warehouse_id, quantity, reference_type, reference_id))

            conn.commit()

        except Exception as e:
            conn.rollback()
            logging.error(f"خطأ في خصم من المخزون: {e}")
            raise
        finally:
            conn.close()

    def get_item_inventory(self, item_id, warehouse_id=None):
        """جلب معلومات المخزون للصنف"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            if warehouse_id:
                cursor.execute('''
                    SELECT i.quantity, i.reserved_quantity, w.warehouse_name
                    FROM inventory i
                    JOIN warehouses w ON i.warehouse_id = w.id
                    WHERE i.item_id = ? AND i.warehouse_id = ?
                ''', (item_id, warehouse_id))
                return cursor.fetchone()
            else:
                cursor.execute('''
                    SELECT i.quantity, i.reserved_quantity, w.warehouse_name, w.id as warehouse_id
                    FROM inventory i
                    JOIN warehouses w ON i.warehouse_id = w.id
                    WHERE i.item_id = ?
                ''', (item_id,))
                return cursor.fetchall()

        except Exception as e:
            logging.error(f"خطأ في جلب معلومات المخزون: {e}")
            return None
        finally:
            conn.close()

# إنشاء مثيل من مدير قاعدة البيانات
db_manager = DatabaseManager()
