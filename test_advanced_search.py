#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار البحث المتقدم المخفي
"""

import sys
import os
import tkinter as tk

# إضافة المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_advanced_search():
    """اختبار البحث المتقدم"""
    print("🔍 اختبار البحث المتقدم المخفي")
    
    # إنشاء نافذة رئيسية
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    try:
        # استيراد نافذة البحث
        from src.item_search_dialog import ItemSearchDialog
        
        # إنشاء بيانات وهمية للاختبار
        test_items = [
            {
                'id': 1,
                'item_code': 'LAPTOP001',
                'item_name': 'لابتوب ديل XPS 13',
                'description': 'لابتوب عالي الأداء للأعمال',
                'unit': 'قطعة',
                'unit_price': '3500.00',
                'category': 'إلكترونيات',
                'current_stock': '5',
                'origin_country': 'الصين'
            },
            {
                'id': 2,
                'item_code': 'PHONE001',
                'item_name': 'هاتف آيفون 14',
                'description': 'هاتف ذكي من آبل',
                'unit': 'قطعة',
                'unit_price': '4200.00',
                'category': 'إلكترونيات',
                'current_stock': '8',
                'origin_country': 'الصين'
            },
            {
                'id': 3,
                'item_code': 'BOOK001',
                'item_name': 'كتاب البرمجة',
                'description': 'كتاب تعليمي في البرمجة',
                'unit': 'قطعة',
                'unit_price': '85.00',
                'category': 'كتب',
                'current_stock': '20',
                'origin_country': 'السعودية'
            }
        ]
        
        print(f"📦 إنشاء نافذة البحث مع {len(test_items)} صنف تجريبي")
        
        # إنشاء نافذة البحث
        search_dialog = ItemSearchDialog(root, test_items, None)
        
        # تشغيل النافذة
        root.deiconify()  # إظهار النافذة الرئيسية
        
        # إضافة تعليمات للمستخدم
        instructions = tk.Label(
            root,
            text="تعليمات الاختبار:\n" +
                 "• اضغط F9 لفتح نافذة البحث\n" +
                 "• اضغط F3 لإظهار/إخفاء البحث المتقدم\n" +
                 "• جرب البحث في الحقل العام\n" +
                 "• جرب البحث المتقدم بالحقول المخصصة",
            font=('Arial', 12),
            justify='right',
            padx=20,
            pady=20
        )
        instructions.pack(expand=True)
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البحث المتقدم: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_advanced_search()
