#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة استيراد الأصناف من Excel
Test Excel Import Feature
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار الاستيرادات المطلوبة"""
    print("🧪 اختبار الاستيرادات المطلوبة...")
    
    # اختبار pandas
    try:
        import pandas as pd
        print("✅ pandas متوفر - الإصدار:", pd.__version__)
        pandas_available = True
    except ImportError:
        print("❌ pandas غير متوفر")
        pandas_available = False
    
    # اختبار openpyxl
    try:
        import openpyxl
        print("✅ openpyxl متوفر - الإصدار:", openpyxl.__version__)
        openpyxl_available = True
    except ImportError:
        print("❌ openpyxl غير متوفر")
        openpyxl_available = False
    
    # اختبار tkinter
    try:
        import tkinter as tk
        from tkinter import ttk, messagebox, filedialog
        print("✅ tkinter متوفر")
        tkinter_available = True
    except ImportError:
        print("❌ tkinter غير متوفر")
        tkinter_available = False
    
    return pandas_available and openpyxl_available and tkinter_available

def test_items_window_import():
    """اختبار استيراد نافذة الأصناف"""
    print("\n🧪 اختبار استيراد نافذة الأصناف...")
    
    try:
        from src.items_window import ItemsWindow
        print("✅ تم استيراد ItemsWindow بنجاح")
        return True
    except ImportError as e:
        print(f"❌ فشل في استيراد ItemsWindow: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في استيراد ItemsWindow: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🧪 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        from database.database_manager import DatabaseManager
        db_manager = DatabaseManager()
        conn = db_manager.get_connection()
        cursor = conn.cursor()
        
        # اختبار جدول الأصناف
        cursor.execute("SELECT COUNT(*) FROM items")
        items_count = cursor.fetchone()[0]
        print(f"✅ جدول الأصناف متوفر - عدد الأصناف: {items_count}")
        
        # اختبار جدول الفئات
        cursor.execute("SELECT COUNT(*) FROM item_categories")
        categories_count = cursor.fetchone()[0]
        print(f"✅ جدول الفئات متوفر - عدد الفئات: {categories_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return False

def create_sample_excel():
    """إنشاء ملف Excel نموذجي للاختبار"""
    print("\n🧪 إنشاء ملف Excel نموذجي...")
    
    try:
        import pandas as pd
        
        # بيانات نموذجية
        sample_data = {
            'item_code': ['TEST001', 'TEST002', 'TEST003'],
            'item_name': ['صنف تجريبي 1', 'صنف تجريبي 2', 'صنف تجريبي 3'],
            'unit_of_measure': ['قطعة', 'كيلو', 'متر'],
            'category_name': ['فئة تجريبية', 'فئة تجريبية', 'فئة أخرى'],
            'cost_price': [100.0, 50.0, 75.0],
            'selling_price': [120.0, 65.0, 90.0],
            'current_stock': [10, 20, 15],
            'min_stock_level': [5, 10, 5],
            'description': ['وصف الصنف الأول', 'وصف الصنف الثاني', 'وصف الصنف الثالث']
        }
        
        df = pd.DataFrame(sample_data)
        test_file = 'test_items_import.xlsx'
        df.to_excel(test_file, index=False)
        
        print(f"✅ تم إنشاء ملف Excel نموذجي: {test_file}")
        print(f"📊 يحتوي على {len(df)} صف و {len(df.columns)} عمود")
        
        return test_file
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف Excel: {e}")
        return None

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار ميزة استيراد الأصناف من Excel")
    print("=" * 50)
    
    # اختبار الاستيرادات
    imports_ok = test_imports()
    
    # اختبار نافذة الأصناف
    items_window_ok = test_items_window_import()
    
    # اختبار قاعدة البيانات
    database_ok = test_database_connection()
    
    # إنشاء ملف Excel نموذجي
    if imports_ok:
        sample_file = create_sample_excel()
    else:
        sample_file = None
    
    # النتائج النهائية
    print("\n" + "=" * 50)
    print("📋 نتائج الاختبار:")
    print(f"{'✅' if imports_ok else '❌'} المكتبات المطلوبة")
    print(f"{'✅' if items_window_ok else '❌'} نافذة الأصناف")
    print(f"{'✅' if database_ok else '❌'} قاعدة البيانات")
    print(f"{'✅' if sample_file else '❌'} ملف Excel النموذجي")
    
    if all([imports_ok, items_window_ok, database_ok]):
        print("\n🎉 جميع الاختبارات نجحت! ميزة الاستيراد جاهزة للاستخدام")
        print("\n📝 خطوات الاستخدام:")
        print("1. افتح شاشة إدارة الأصناف")
        print("2. اضغط على زر '📋 قالب Excel' لإنشاء قالب")
        print("3. املأ البيانات في القالب")
        print("4. اضغط على زر '📥 استيراد من Excel' لاستيراد البيانات")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى إصلاح المشاكل قبل الاستخدام")
        
        if not imports_ok:
            print("\n💡 لتثبيت المكتبات المطلوبة:")
            print("pip install pandas openpyxl")

if __name__ == "__main__":
    main()
