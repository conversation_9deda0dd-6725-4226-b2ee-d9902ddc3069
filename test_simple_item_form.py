#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النموذج المبسط لإدخال الأصناف
Test Simple Item Input Form

هذا الملف يختبر النموذج المبسط الجديد لإدخال الأصناف في شاشة الشحنة الجديدة
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# إضافة مسار المشروع
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from src.fullscreen_shipment_form import FullscreenShipmentForm
    from database.database_manager import DatabaseManager
except ImportError as e:
    print(f"خطأ في الاستيراد: {e}")
    print("تأكد من وجود الملفات في المسار الصحيح")
    sys.exit(1)

def test_simple_form():
    """اختبار النموذج المبسط"""
    print("🧪 بدء اختبار النموذج المبسط لإدخال الأصناف...")
    
    try:
        # إنشاء النافذة الرئيسية
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # إنشاء نموذج الشحنة
        form = FullscreenShipmentForm(root, mode='add')
        
        # التنقل إلى قسم الأصناف
        form.switch_section(1)  # قسم الأصناف هو القسم رقم 1
        
        print("✅ تم إنشاء النموذج المبسط بنجاح")
        print("📋 الميزات المتوفرة:")
        print("   • إدخال مباشر للأصناف بدون أزرار منفصلة")
        print("   • حقول مبسطة: كود الصنف، اسم الصنف، الكمية، الوحدة، سعر الوحدة، المخزن")
        print("   • حساب الإجمالي تلقائياً")
        print("   • ملء البيانات تلقائياً عند إدخال كود الصنف")
        print("   • التنقل بين الحقول باستخدام Tab/Shift+Tab")
        print("   • إضافة الصنف بالضغط على Enter")
        print("   • البحث في الأصناف بالضغط على F9")
        print("   • مسح النموذج تلقائياً بعد الإضافة")
        
        # تشغيل التطبيق
        print("\n🚀 تشغيل التطبيق...")
        print("💡 للاختبار:")
        print("   1. انتقل إلى قسم 'الأصناف'")
        print("   2. ستجد النموذج المبسط في الأعلى")
        print("   3. جرب إدخال البيانات والضغط على Enter")
        print("   4. جرب استخدام Tab للتنقل بين الحقول")
        print("   5. جرب F9 للبحث في الأصناف")
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False
    
    return True

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔗 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        db_manager = DatabaseManager()
        conn = db_manager.get_connection()
        
        # اختبار جلب الأصناف
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) as count FROM items WHERE is_active = 1")
        result = cursor.fetchone()
        items_count = result['count'] if result else 0
        
        # اختبار جلب المخازن
        cursor.execute("SELECT COUNT(*) as count FROM warehouses WHERE is_active = 1")
        result = cursor.fetchone()
        warehouses_count = result['count'] if result else 0
        
        conn.close()
        
        print(f"✅ الاتصال بقاعدة البيانات ناجح")
        print(f"📦 عدد الأصناف النشطة: {items_count}")
        print(f"🏪 عدد المخازن النشطة: {warehouses_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار النموذج المبسط لإدخال الأصناف")
    print("=" * 60)
    
    # اختبار قاعدة البيانات أولاً
    if not test_database_connection():
        print("❌ فشل اختبار قاعدة البيانات")
        return
    
    print("\n" + "-" * 40)
    
    # اختبار النموذج المبسط
    if test_simple_form():
        print("✅ تم اختبار النموذج المبسط بنجاح")
    else:
        print("❌ فشل اختبار النموذج المبسط")

if __name__ == "__main__":
    main()
