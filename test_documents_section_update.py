#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحديثات قسم المستندات في شاشة الشحنة الجديدة
Test Documents Section Updates in New Shipment Screen
"""

import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

def test_fullscreen_shipment_form_import():
    """اختبار استيراد نموذج الشحنة الجديدة"""
    try:
        from src.fullscreen_shipment_form import FullscreenShipmentForm
        print("✅ تم استيراد FullscreenShipmentForm بنجاح")
        return True
    except ImportError as e:
        print(f"❌ خطأ في استيراد FullscreenShipmentForm: {e}")
        return False

def test_document_fields_structure():
    """اختبار بنية حقول المستندات الجديدة"""
    try:
        # قراءة محتوى الملف للتحقق من التحديثات
        with open('src/fullscreen_shipment_form.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من التحديثات المطلوبة
        checks = [
            ("الوثائق الأولية", "📋 الوثائق الأولية" in content),
            ("المستندات (DN)", "📦 المستندات (DN)" in content),
            ("المستندات الخاصة بالجمارك", "🏭 المستندات الخاصة بالجمارك" in content),
            ("زر إضافة مرفق", "📎 إضافة مرفق" in content),
            ("زر إضافة رابط", "📝 إضافة رابط" in content),
            ("حقل للقراءة فقط", "state='readonly'" in content),
        ]
        
        # التحقق من حذف الحقول غير المطلوبة
        removed_checks = [
            ("شهادة الجودة محذوفة", "✅ شهادة الجودة" not in content),
            ("شهادة الصحة محذوفة", "🏥 شهادة الصحة" not in content),
            ("رخصة الاستيراد محذوفة", "📜 رخصة الاستيراد" not in content),
            ("شهادة التفتيش محذوفة", "🔍 شهادة التفتيش" not in content),
        ]
        
        all_checks = checks + removed_checks
        passed = 0
        
        print("🔍 فحص بنية حقول المستندات:")
        for check_name, result in all_checks:
            if result:
                print(f"   ✅ {check_name}")
                passed += 1
            else:
                print(f"   ❌ {check_name}")
        
        print(f"\n📊 النتيجة: {passed}/{len(all_checks)} فحوصات نجحت")
        return passed == len(all_checks)
        
    except Exception as e:
        print(f"❌ خطأ في فحص بنية المستندات: {e}")
        return False

def test_documents_progress_update():
    """اختبار تحديث قائمة المستندات في دالة التقدم"""
    try:
        with open('src/fullscreen_shipment_form.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من أن قائمة المستندات تحتوي على الحقول الصحيحة فقط
        expected_fields = [
            "'commercial_invoice_status'",
            "'packing_list_status'", 
            "'certificate_of_origin_status'",
            "'insurance_policy_status'",
            "'customs_declaration_status'"
        ]
        
        # التحقق من أن الحقول المحذوفة غير موجودة
        removed_fields = [
            "'quality_certificate_status'",
            "'health_certificate_status'",
            "'import_license_status'",
            "'inspection_certificate_status'"
        ]
        
        print("🔍 فحص قائمة المستندات في دالة التقدم:")
        
        passed = 0
        total = len(expected_fields) + len(removed_fields)
        
        for field in expected_fields:
            if field in content:
                print(f"   ✅ {field} موجود")
                passed += 1
            else:
                print(f"   ❌ {field} مفقود")
        
        for field in removed_fields:
            if field not in content:
                print(f"   ✅ {field} محذوف بنجاح")
                passed += 1
            else:
                print(f"   ❌ {field} لا يزال موجود")
        
        print(f"\n📊 النتيجة: {passed}/{total} فحوصات نجحت")
        return passed == total
        
    except Exception as e:
        print(f"❌ خطأ في فحص دالة التقدم: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار تحديثات قسم المستندات...")
    print("=" * 60)
    
    tests = [
        ("اختبار استيراد نموذج الشحنة", test_fullscreen_shipment_form_import),
        ("اختبار بنية حقول المستندات", test_document_fields_structure),
        ("اختبار تحديث دالة التقدم", test_documents_progress_update),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        if test_func():
            passed += 1
            print(f"   ✅ نجح الاختبار")
        else:
            print(f"   ❌ فشل الاختبار")
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! تحديثات قسم المستندات مكتملة.")
        print("\n📋 ملخص التحديثات:")
        print("   • تم تغيير 'الفاتورة التجارية' إلى 'الوثائق الأولية'")
        print("   • تم تغيير 'قائمة التعبئة' إلى 'المستندات (DN)'")
        print("   • تم تغيير 'شهادة المنشأ' إلى 'المستندات الخاصة بالجمارك'")
        print("   • تم استبدال 'فتح الرابط' بـ 'إضافة مرفق'")
        print("   • تم حذف الحقول غير المطلوبة (شهادة الجودة، الصحة، رخصة الاستيراد، شهادة التفتيش)")
        print("   • تم جعل حقول الروابط للقراءة فقط")
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
