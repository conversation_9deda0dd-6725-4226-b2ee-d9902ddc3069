#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة المخازن
Warehouses Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.auth_manager import auth_manager
from database.database_manager import DatabaseManager
from config.config import COLORS, FONTS

class WarehousesWindow:
    def __init__(self, parent=None):
        self.parent = parent
        self.db_manager = DatabaseManager()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_warehouses()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة المخازن - نظام متابعة الشحنات")
        self.window.geometry("1000x700")
        self.window.configure(bg=COLORS['background'])
        
        # توسيط النافذة
        self.center_window()
        
        # ربط حدث إغلاق النافذة
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
        
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(
            main_frame,
            text="🏪 إدارة المخازن",
            font=(FONTS['arabic']['family'], 18, 'bold'),
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 20))
        
        # إطار الأزرار العلوية
        buttons_frame = tk.Frame(main_frame, bg=COLORS['background'])
        buttons_frame.pack(fill='x', pady=(0, 20))
        
        # زر إضافة مخزن جديد
        add_btn = tk.Button(
            buttons_frame,
            text="➕ إضافة مخزن جديد",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['success'],
            fg='white',
            command=self.add_warehouse,
            relief='flat',
            padx=20,
            pady=8
        )
        add_btn.pack(side='right', padx=(0, 10))
        
        # زر تحديث
        refresh_btn = tk.Button(
            buttons_frame,
            text="🔄 تحديث",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['info'],
            fg='white',
            command=self.load_warehouses,
            relief='flat',
            padx=20,
            pady=8
        )
        refresh_btn.pack(side='right', padx=(0, 10))
        
        # إطار البحث
        search_frame = tk.Frame(main_frame, bg=COLORS['background'])
        search_frame.pack(fill='x', pady=(0, 20))
        
        tk.Label(
            search_frame,
            text="🔍 البحث:",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['background'],
            fg=COLORS['text_primary']
        ).pack(side='right', padx=(0, 10))
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search)
        search_entry = tk.Entry(
            search_frame,
            textvariable=self.search_var,
            font=(FONTS['arabic']['family'], 12),
            width=30
        )
        search_entry.pack(side='right', padx=(0, 10))
        
        # إطار الجدول
        table_frame = tk.Frame(main_frame, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True)
        
        # إنشاء Treeview للمخازن
        columns = ('warehouse_code', 'warehouse_name', 'location', 'manager_name', 'phone', 'status')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعريف العناوين
        self.tree.heading('warehouse_code', text='كود المخزن')
        self.tree.heading('warehouse_name', text='اسم المخزن')
        self.tree.heading('location', text='الموقع')
        self.tree.heading('manager_name', text='مدير المخزن')
        self.tree.heading('phone', text='الهاتف')
        self.tree.heading('status', text='الحالة')
        
        # تعريف عرض الأعمدة
        self.tree.column('warehouse_code', width=100, anchor='center')
        self.tree.column('warehouse_name', width=200, anchor='center')
        self.tree.column('location', width=150, anchor='center')
        self.tree.column('manager_name', width=150, anchor='center')
        self.tree.column('phone', width=120, anchor='center')
        self.tree.column('status', width=80, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول وشريط التمرير
        self.tree.pack(side='right', fill='both', expand=True)
        scrollbar.pack(side='left', fill='y')
        
        # ربط النقر المزدوج
        self.tree.bind('<Double-1>', self.on_double_click)
        
        # إطار الأزرار السفلية
        bottom_buttons_frame = tk.Frame(main_frame, bg=COLORS['background'])
        bottom_buttons_frame.pack(fill='x', pady=(20, 0))
        
        # زر تعديل
        edit_btn = tk.Button(
            bottom_buttons_frame,
            text="✏️ تعديل",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['warning'],
            fg='white',
            command=self.edit_warehouse,
            relief='flat',
            padx=20,
            pady=8
        )
        edit_btn.pack(side='right', padx=(0, 10))
        
        # زر حذف
        delete_btn = tk.Button(
            bottom_buttons_frame,
            text="🗑️ حذف",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['danger'],
            fg='white',
            command=self.delete_warehouse,
            relief='flat',
            padx=20,
            pady=8
        )
        delete_btn.pack(side='right', padx=(0, 10))
        
        # زر عرض المخزون
        inventory_btn = tk.Button(
            bottom_buttons_frame,
            text="📦 عرض المخزون",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['info'],
            fg='white',
            command=self.view_inventory,
            relief='flat',
            padx=20,
            pady=8
        )
        inventory_btn.pack(side='right', padx=(0, 10))
        
    def load_warehouses(self):
        """تحميل المخازن من قاعدة البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # جلب المخازن من قاعدة البيانات
            query = """
                SELECT warehouse_code, warehouse_name, location, 
                       manager_name, phone, is_active
                FROM warehouses
                ORDER BY warehouse_name
            """
            warehouses = self.db_manager.fetch_all(query)
            
            # إضافة المخازن إلى الجدول
            for warehouse in warehouses:
                status = "نشط" if warehouse['is_active'] else "غير نشط"
                self.tree.insert('', 'end', values=(
                    warehouse['warehouse_code'],
                    warehouse['warehouse_name'],
                    warehouse['location'] or '',
                    warehouse['manager_name'] or '',
                    warehouse['phone'] or '',
                    status
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل المخازن: {str(e)}")
    
    def on_search(self, *args):
        """البحث في المخازن"""
        search_term = self.search_var.get().strip()
        
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        try:
            if search_term:
                query = """
                    SELECT warehouse_code, warehouse_name, location, 
                           manager_name, phone, is_active
                    FROM warehouses
                    WHERE warehouse_code LIKE ? OR warehouse_name LIKE ? 
                       OR location LIKE ? OR manager_name LIKE ?
                    ORDER BY warehouse_name
                """
                search_pattern = f"%{search_term}%"
                warehouses = self.db_manager.fetch_all(query, 
                    (search_pattern, search_pattern, search_pattern, search_pattern))
            else:
                query = """
                    SELECT warehouse_code, warehouse_name, location, 
                           manager_name, phone, is_active
                    FROM warehouses
                    ORDER BY warehouse_name
                """
                warehouses = self.db_manager.fetch_all(query)
            
            # إضافة النتائج إلى الجدول
            for warehouse in warehouses:
                status = "نشط" if warehouse['is_active'] else "غير نشط"
                self.tree.insert('', 'end', values=(
                    warehouse['warehouse_code'],
                    warehouse['warehouse_name'],
                    warehouse['location'] or '',
                    warehouse['manager_name'] or '',
                    warehouse['phone'] or '',
                    status
                ))
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في البحث: {str(e)}")
    
    def on_double_click(self, event):
        """عند النقر المزدوج على مخزن"""
        self.edit_warehouse()
    
    def add_warehouse(self):
        """إضافة مخزن جديد"""
        self.open_warehouse_form()
    
    def edit_warehouse(self):
        """تعديل المخزن المحدد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مخزن للتعديل")
            return
        
        warehouse_code = self.tree.item(selected_item[0])['values'][0]
        self.open_warehouse_form(warehouse_code)
    
    def delete_warehouse(self):
        """حذف المخزن المحدد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مخزن للحذف")
            return
        
        warehouse_code = self.tree.item(selected_item[0])['values'][0]
        warehouse_name = self.tree.item(selected_item[0])['values'][1]
        
        # تأكيد الحذف
        if messagebox.askyesno("تأكيد الحذف", 
                              f"هل أنت متأكد من حذف المخزن '{warehouse_name}'؟"):
            try:
                # حذف المخزن من قاعدة البيانات
                query = "DELETE FROM warehouses WHERE warehouse_code = ?"
                self.db_manager.execute_query(query, (warehouse_code,))
                
                messagebox.showinfo("نجح", "تم حذف المخزن بنجاح")
                self.load_warehouses()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف المخزن: {str(e)}")
    
    def view_inventory(self):
        """عرض مخزون المخزن المحدد"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("تحذير", "يرجى اختيار مخزن لعرض مخزونه")
            return

        warehouse_code = self.tree.item(selected_item[0])['values'][0]
        warehouse_name = self.tree.item(selected_item[0])['values'][1]

        # فتح نافذة مخزون المخزن
        WarehouseInventoryWindow(self.window, self.db_manager, warehouse_code, warehouse_name)
    
    def open_warehouse_form(self, warehouse_code=None):
        """فتح نموذج المخزن"""
        WarehouseForm(self.window, self.db_manager, warehouse_code, self.load_warehouses)
    
    def on_closing(self):
        """عند إغلاق النافذة"""
        self.window.destroy()


class WarehouseForm:
    def __init__(self, parent, db_manager, warehouse_code=None, callback=None):
        self.parent = parent
        self.db_manager = db_manager
        self.warehouse_code = warehouse_code
        self.callback = callback
        self.is_edit_mode = warehouse_code is not None
        
        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()
        
        if self.is_edit_mode:
            self.load_warehouse_data()
    
    def setup_window(self):
        """إعداد النافذة"""
        title = "تعديل مخزن" if self.is_edit_mode else "إضافة مخزن جديد"
        self.window.title(f"{title} - نظام متابعة الشحنات")
        self.window.geometry("500x400")
        self.window.configure(bg=COLORS['background'])
        self.window.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # جعل النافذة في المقدمة
        self.window.transient(self.parent)
        self.window.grab_set()
        
    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر النموذج"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=30, pady=30)
        
        # العنوان
        title = "تعديل مخزن" if self.is_edit_mode else "إضافة مخزن جديد"
        title_label = tk.Label(
            main_frame,
            text=f"🏪 {title}",
            font=(FONTS['arabic']['family'], 16, 'bold'),
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 30))
        
        # إطار الحقول
        fields_frame = tk.Frame(main_frame, bg=COLORS['background'])
        fields_frame.pack(fill='both', expand=True)
        
        # كود المخزن
        self.create_field(fields_frame, "كود المخزن:", 'warehouse_code', 0)
        
        # اسم المخزن
        self.create_field(fields_frame, "اسم المخزن:", 'warehouse_name', 1)
        
        # الموقع
        self.create_field(fields_frame, "الموقع:", 'location', 2)
        
        # مدير المخزن
        self.create_field(fields_frame, "مدير المخزن:", 'manager_name', 3)
        
        # الهاتف
        self.create_field(fields_frame, "الهاتف:", 'phone', 4)
        
        # الحالة
        tk.Label(
            fields_frame,
            text="الحالة:",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['background'],
            fg=COLORS['text_primary']
        ).grid(row=5, column=1, sticky='e', padx=(0, 10), pady=10)
        
        self.is_active_var = tk.BooleanVar(value=True)
        status_frame = tk.Frame(fields_frame, bg=COLORS['background'])
        status_frame.grid(row=5, column=0, sticky='w', pady=10)
        
        tk.Checkbutton(
            status_frame,
            text="نشط",
            variable=self.is_active_var,
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['background'],
            fg=COLORS['text_primary']
        ).pack(side='right')
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg=COLORS['background'])
        buttons_frame.pack(fill='x', pady=(30, 0))
        
        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['success'],
            fg='white',
            command=self.save_warehouse,
            relief='flat',
            padx=30,
            pady=10
        )
        save_btn.pack(side='right', padx=(0, 10))
        
        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['secondary'],
            fg='white',
            command=self.window.destroy,
            relief='flat',
            padx=30,
            pady=10
        )
        cancel_btn.pack(side='right')
    
    def create_field(self, parent, label_text, field_name, row):
        """إنشاء حقل إدخال"""
        tk.Label(
            parent,
            text=label_text,
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['background'],
            fg=COLORS['text_primary']
        ).grid(row=row, column=1, sticky='e', padx=(0, 10), pady=10)
        
        entry = tk.Entry(
            parent,
            font=(FONTS['arabic']['family'], 12),
            width=25
        )
        entry.grid(row=row, column=0, sticky='w', pady=10)
        
        setattr(self, f"{field_name}_entry", entry)
    
    def load_warehouse_data(self):
        """تحميل بيانات المخزن للتعديل"""
        try:
            query = """
                SELECT warehouse_code, warehouse_name, location, 
                       manager_name, phone, is_active
                FROM warehouses
                WHERE warehouse_code = ?
            """
            warehouse = self.db_manager.fetch_one(query, (self.warehouse_code,))
            
            if warehouse:
                self.warehouse_code_entry.insert(0, warehouse['warehouse_code'])
                self.warehouse_code_entry.config(state='readonly')  # منع تعديل الكود
                
                self.warehouse_name_entry.insert(0, warehouse['warehouse_name'])
                self.location_entry.insert(0, warehouse['location'] or '')
                self.manager_name_entry.insert(0, warehouse['manager_name'] or '')
                self.phone_entry.insert(0, warehouse['phone'] or '')
                self.is_active_var.set(warehouse['is_active'])
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل بيانات المخزن: {str(e)}")
    
    def save_warehouse(self):
        """حفظ بيانات المخزن"""
        # التحقق من صحة البيانات
        warehouse_code = self.warehouse_code_entry.get().strip()
        warehouse_name = self.warehouse_name_entry.get().strip()
        
        if not warehouse_code:
            messagebox.showerror("خطأ", "يرجى إدخال كود المخزن")
            return
        
        if not warehouse_name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المخزن")
            return
        
        try:
            location = self.location_entry.get().strip()
            manager_name = self.manager_name_entry.get().strip()
            phone = self.phone_entry.get().strip()
            is_active = self.is_active_var.get()
            
            if self.is_edit_mode:
                # تحديث المخزن
                query = """
                    UPDATE warehouses 
                    SET warehouse_name = ?, location = ?, manager_name = ?, 
                        phone = ?, is_active = ?
                    WHERE warehouse_code = ?
                """
                params = (warehouse_name, location, manager_name, phone, is_active, warehouse_code)
            else:
                # إضافة مخزن جديد
                query = """
                    INSERT INTO warehouses (warehouse_code, warehouse_name, location, 
                                          manager_name, phone, is_active)
                    VALUES (?, ?, ?, ?, ?, ?)
                """
                params = (warehouse_code, warehouse_name, location, manager_name, phone, is_active)
            
            self.db_manager.execute_query(query, params)
            
            action = "تحديث" if self.is_edit_mode else "إضافة"
            messagebox.showinfo("نجح", f"تم {action} المخزن بنجاح")
            
            if self.callback:
                self.callback()
            
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في حفظ المخزن: {str(e)}")


class WarehouseInventoryWindow:
    def __init__(self, parent, db_manager, warehouse_code, warehouse_name):
        self.parent = parent
        self.db_manager = db_manager
        self.warehouse_code = warehouse_code
        self.warehouse_name = warehouse_name

        self.window = tk.Toplevel(parent)
        self.setup_window()
        self.create_widgets()
        self.load_inventory()

    def setup_window(self):
        """إعداد النافذة"""
        self.window.title(f"مخزون المخزن: {self.warehouse_name} - نظام متابعة الشحنات")
        self.window.geometry("800x600")
        self.window.configure(bg=COLORS['background'])

        # توسيط النافذة
        self.center_window()

        # جعل النافذة في المقدمة
        self.window.transient(self.parent)

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # العنوان
        title_label = tk.Label(
            main_frame,
            text=f"📦 مخزون المخزن: {self.warehouse_name}",
            font=(FONTS['arabic']['family'], 16, 'bold'),
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 20))

        # إطار الجدول
        table_frame = tk.Frame(main_frame, bg=COLORS['background'])
        table_frame.pack(fill='both', expand=True)

        # إنشاء Treeview للمخزون
        columns = ('item_code', 'item_name', 'quantity', 'reserved_quantity', 'available_quantity')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # تعريف العناوين
        self.tree.heading('item_code', text='كود الصنف')
        self.tree.heading('item_name', text='اسم الصنف')
        self.tree.heading('quantity', text='الكمية الإجمالية')
        self.tree.heading('reserved_quantity', text='الكمية المحجوزة')
        self.tree.heading('available_quantity', text='الكمية المتاحة')

        # تعريف عرض الأعمدة
        self.tree.column('item_code', width=120, anchor='center')
        self.tree.column('item_name', width=200, anchor='center')
        self.tree.column('quantity', width=120, anchor='center')
        self.tree.column('reserved_quantity', width=120, anchor='center')
        self.tree.column('available_quantity', width=120, anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول وشريط التمرير
        self.tree.pack(side='right', fill='both', expand=True)
        scrollbar.pack(side='left', fill='y')

        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg=COLORS['background'])
        buttons_frame.pack(fill='x', pady=(20, 0))

        # زر تحديث
        refresh_btn = tk.Button(
            buttons_frame,
            text="🔄 تحديث",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['info'],
            fg='white',
            command=self.load_inventory,
            relief='flat',
            padx=20,
            pady=8
        )
        refresh_btn.pack(side='right', padx=(0, 10))

        # زر إغلاق
        close_btn = tk.Button(
            buttons_frame,
            text="❌ إغلاق",
            font=(FONTS['arabic']['family'], 12),
            bg=COLORS['secondary'],
            fg='white',
            command=self.window.destroy,
            relief='flat',
            padx=20,
            pady=8
        )
        close_btn.pack(side='right')

    def load_inventory(self):
        """تحميل مخزون المخزن"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)

            # جلب مخزون المخزن من قاعدة البيانات
            query = """
                SELECT i.item_code, i.item_name,
                       COALESCE(inv.quantity, 0) as quantity,
                       COALESCE(inv.reserved_quantity, 0) as reserved_quantity,
                       (COALESCE(inv.quantity, 0) - COALESCE(inv.reserved_quantity, 0)) as available_quantity
                FROM items i
                LEFT JOIN inventory inv ON i.id = inv.item_id
                LEFT JOIN warehouses w ON inv.warehouse_id = w.id
                WHERE w.warehouse_code = ? AND (inv.quantity > 0 OR inv.reserved_quantity > 0)
                ORDER BY i.item_name
            """
            inventory_items = self.db_manager.fetch_all(query, (self.warehouse_code,))

            # إضافة العناصر إلى الجدول
            for item in inventory_items:
                self.tree.insert('', 'end', values=(
                    item['item_code'],
                    item['item_name'],
                    item['quantity'],
                    item['reserved_quantity'],
                    item['available_quantity']
                ))

            # إذا لم توجد عناصر، أظهر رسالة
            if not inventory_items:
                self.tree.insert('', 'end', values=(
                    '', 'لا توجد أصناف في هذا المخزن', '', '', ''
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل المخزون: {str(e)}")


if __name__ == "__main__":
    # تشغيل نافذة المخازن للاختبار
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية

    warehouses_window = WarehousesWindow()
    warehouses_window.window.mainloop()
