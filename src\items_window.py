#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إدارة الأصناف
Items Management Window
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from datetime import datetime

# استيراد مكتبات Excel
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.auth_manager import auth_manager
from database.database_manager import DatabaseManager
from config.config import COLORS, FONTS, UNITS_OF_MEASURE

class ItemsWindow:
    def __init__(self, parent=None):
        self.parent = parent
        self.db_manager = DatabaseManager()
        self.window = tk.Toplevel(parent) if parent else tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_items()
        
    def setup_window(self):
        """إعداد النافذة"""
        self.window.title("إدارة الأصناف - نظام متابعة الشحنات")
        self.window.geometry("1200x700")
        self.window.configure(bg=COLORS['background'])
        
        # توسيط النافذة
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = tk.Label(
            main_frame,
            text="إدارة الأصناف",
            font=(FONTS['title']['family'], FONTS['title']['size'], 'bold'),
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 20))
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg=COLORS['background'])
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        # أزرار العمليات
        self.add_button = tk.Button(
            buttons_frame,
            text="إضافة صنف جديد",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['success'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.add_item
        )
        self.add_button.pack(side='right', padx=5)
        
        self.edit_button = tk.Button(
            buttons_frame,
            text="تعديل",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['warning'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.edit_item,
            state='disabled'
        )
        self.edit_button.pack(side='right', padx=5)
        
        self.delete_button = tk.Button(
            buttons_frame,
            text="حذف",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['danger'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.delete_item,
            state='disabled'
        )
        self.delete_button.pack(side='right', padx=5)
        
        self.categories_button = tk.Button(
            buttons_frame,
            text="إدارة الفئات",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['info'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.manage_categories
        )
        self.categories_button.pack(side='right', padx=5)
        
        self.refresh_button = tk.Button(
            buttons_frame,
            text="تحديث",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['secondary'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.load_items
        )
        self.refresh_button.pack(side='right', padx=5)

        self.import_button = tk.Button(
            buttons_frame,
            text="📥 استيراد من Excel",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['warning'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.import_from_excel
        )
        self.import_button.pack(side='right', padx=5)

        self.template_button = tk.Button(
            buttons_frame,
            text="📋 قالب Excel",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['info'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=self.create_excel_template
        )
        self.template_button.pack(side='right', padx=5)
        
        # إطار البحث والتصفية
        filter_frame = tk.Frame(main_frame, bg=COLORS['background'])
        filter_frame.pack(fill='x', pady=(0, 10))
        
        # البحث
        search_label = tk.Label(
            filter_frame,
            text="البحث:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['background'],
            fg=COLORS['dark']
        )
        search_label.pack(side='right', padx=(0, 5))
        
        self.search_entry = tk.Entry(
            filter_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            justify='right',
            width=20
        )
        self.search_entry.pack(side='right', padx=(0, 10))
        self.search_entry.bind('<KeyRelease>', self.search_items)
        
        # تصفية حسب الفئة
        category_label = tk.Label(
            filter_frame,
            text="الفئة:",
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['background'],
            fg=COLORS['dark']
        )
        category_label.pack(side='right', padx=(0, 5))
        
        self.category_filter = ttk.Combobox(
            filter_frame,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            state='readonly',
            width=15
        )
        self.category_filter.pack(side='right', padx=(0, 10))
        self.category_filter.bind('<<ComboboxSelected>>', self.filter_by_category)
        
        # تحميل الفئات
        self.load_categories()
        
        # جدول الأصناف
        self.create_items_table(main_frame)
        
    def create_items_table(self, parent):
        """إنشاء جدول الأصناف"""
        # إطار الجدول
        table_frame = tk.Frame(parent, bg=COLORS['white'])
        table_frame.pack(fill='both', expand=True)
        
        # إنشاء Treeview
        columns = ('item_code', 'item_name', 'category', 'unit', 'cost_price', 
                  'selling_price', 'current_stock', 'min_stock', 'is_active')
        self.items_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # تعريف العناوين
        headers = {
            'item_code': 'كود الصنف',
            'item_name': 'اسم الصنف',
            'category': 'الفئة',
            'unit': 'الوحدة',
            'cost_price': 'سعر التكلفة',
            'selling_price': 'سعر البيع',
            'current_stock': 'المخزون الحالي',
            'min_stock': 'الحد الأدنى',
            'is_active': 'الحالة'
        }
        
        for col in columns:
            self.items_tree.heading(col, text=headers[col])
            if col in ['cost_price', 'selling_price', 'current_stock', 'min_stock']:
                self.items_tree.column(col, width=100, anchor='center')
            else:
                self.items_tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط الجدول
        self.items_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # ربط الأحداث
        self.items_tree.bind('<<TreeviewSelect>>', self.on_item_select)
        self.items_tree.bind('<Double-1>', self.edit_item)
    
    def load_categories(self):
        """تحميل الفئات"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('SELECT category_name FROM item_categories WHERE is_active = 1 ORDER BY category_name')
            categories = cursor.fetchall()
            
            category_list = ['جميع الفئات'] + [cat['category_name'] for cat in categories]
            self.category_filter['values'] = category_list
            self.category_filter.set('جميع الفئات')
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الفئات: {str(e)}")
    
    def load_items(self):
        """تحميل الأصناف من قاعدة البيانات"""
        try:
            # مسح البيانات الحالية
            for item in self.items_tree.get_children():
                self.items_tree.delete(item)
            
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT i.item_code, i.item_name, c.category_name, i.unit_of_measure,
                       i.cost_price, i.selling_price, i.current_stock, i.min_stock_level, i.is_active
                FROM items i
                LEFT JOIN item_categories c ON i.category_id = c.id
                ORDER BY i.item_name
            ''')
            
            items = cursor.fetchall()
            
            for item in items:
                status = "نشط" if item['is_active'] else "غير نشط"
                values = (
                    item['item_code'],
                    item['item_name'],
                    item['category_name'] or 'بدون فئة',
                    item['unit_of_measure'] or '',
                    f"{item['cost_price']:.2f}" if item['cost_price'] else '0.00',
                    f"{item['selling_price']:.2f}" if item['selling_price'] else '0.00',
                    str(item['current_stock'] or 0),
                    str(item['min_stock_level'] or 0),
                    status
                )
                self.items_tree.insert('', 'end', values=values)
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل البيانات: {str(e)}")
    
    def search_items(self, event=None):
        """البحث في الأصناف"""
        search_term = self.search_entry.get().strip()
        category_filter = self.category_filter.get()
        
        self.filter_items(search_term, category_filter)
    
    def filter_by_category(self, event=None):
        """تصفية حسب الفئة"""
        search_term = self.search_entry.get().strip()
        category_filter = self.category_filter.get()
        
        self.filter_items(search_term, category_filter)
    
    def filter_items(self, search_term="", category_filter="جميع الفئات"):
        """تصفية الأصناف"""
        # مسح البيانات الحالية
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)
        
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()
            
            query = '''
                SELECT i.item_code, i.item_name, c.category_name, i.unit_of_measure,
                       i.cost_price, i.selling_price, i.current_stock, i.min_stock_level, i.is_active
                FROM items i
                LEFT JOIN item_categories c ON i.category_id = c.id
                WHERE 1=1
            '''
            params = []
            
            if search_term:
                query += ' AND (i.item_name LIKE ? OR i.item_code LIKE ?)'
                params.extend([f'%{search_term}%', f'%{search_term}%'])
            
            if category_filter != 'جميع الفئات':
                query += ' AND c.category_name = ?'
                params.append(category_filter)
            
            query += ' ORDER BY i.item_name'
            
            cursor.execute(query, params)
            items = cursor.fetchall()
            
            for item in items:
                status = "نشط" if item['is_active'] else "غير نشط"
                values = (
                    item['item_code'],
                    item['item_name'],
                    item['category_name'] or 'بدون فئة',
                    item['unit_of_measure'] or '',
                    f"{item['cost_price']:.2f}" if item['cost_price'] else '0.00',
                    f"{item['selling_price']:.2f}" if item['selling_price'] else '0.00',
                    str(item['current_stock'] or 0),
                    str(item['min_stock_level'] or 0),
                    status
                )
                self.items_tree.insert('', 'end', values=values)
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في التصفية: {str(e)}")
    
    def on_item_select(self, event=None):
        """عند اختيار صنف من الجدول"""
        selection = self.items_tree.selection()
        if selection:
            self.edit_button.config(state='normal')
            self.delete_button.config(state='normal')
        else:
            self.edit_button.config(state='disabled')
            self.delete_button.config(state='disabled')
    
    def add_item(self):
        """إضافة صنف جديد"""
        if not auth_manager.has_permission('manage_items'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لإضافة الأصناف")
            return
        
        self.open_item_form()
    
    def edit_item(self, event=None):
        """تعديل صنف"""
        if not auth_manager.has_permission('manage_items'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لتعديل الأصناف")
            return
        
        selection = self.items_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للتعديل")
            return
        
        item = self.items_tree.item(selection[0])
        item_code = item['values'][0]
        self.open_item_form(item_code)
    
    def delete_item(self):
        """حذف صنف"""
        if not auth_manager.has_permission('manage_items'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لحذف الأصناف")
            return
        
        selection = self.items_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار صنف للحذف")
            return
        
        item = self.items_tree.item(selection[0])
        item_name = item['values'][1]
        item_code = item['values'][0]
        
        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف الصنف '{item_name}'؟"):
            try:
                conn = self.db_manager.get_connection()
                cursor = conn.cursor()
                
                # التحقق من وجود حركات مخزون مرتبطة بالصنف
                cursor.execute('SELECT COUNT(*) as count FROM inventory_movements WHERE item_id = (SELECT id FROM items WHERE item_code = ?)', (item_code,))
                result = cursor.fetchone()
                
                if result['count'] > 0:
                    messagebox.showerror("خطأ", "لا يمكن حذف هذا الصنف لأنه مرتبط بحركات مخزون")
                    conn.close()
                    return
                
                # حذف الصنف
                cursor.execute('DELETE FROM items WHERE item_code = ?', (item_code,))
                conn.commit()
                conn.close()
                
                messagebox.showinfo("نجح", "تم حذف الصنف بنجاح")
                self.load_items()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في حذف الصنف: {str(e)}")
    
    def manage_categories(self):
        """إدارة فئات الأصناف"""
        try:
            from src.categories_window import CategoriesWindow
            categories_window = CategoriesWindow(self.window, self.load_categories)
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم فتح نافذة إدارة الفئات قريباً")
    
    def open_item_form(self, item_code=None):
        """فتح نموذج الصنف"""
        try:
            from src.item_form import ItemForm
            ItemForm(self.window, self.db_manager, item_code, self.load_items)
        except ImportError:
            messagebox.showinfo("معلومات", "سيتم فتح نموذج الصنف قريباً")

    def import_from_excel(self):
        """استيراد البيانات من ملف Excel"""
        if not auth_manager.has_permission('manage_items'):
            messagebox.showerror("خطأ", "ليس لديك صلاحية لاستيراد الأصناف")
            return

        # التحقق من توفر المكتبات المطلوبة
        if not PANDAS_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة pandas غير متوفرة\nيرجى تثبيتها باستخدام: pip install pandas")
            return

        if not OPENPYXL_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة openpyxl غير متوفرة\nيرجى تثبيتها باستخدام: pip install openpyxl")
            return

        try:
            # اختيار ملف Excel
            file_path = filedialog.askopenfilename(
                title="اختر ملف Excel للاستيراد",
                filetypes=[
                    ("Excel files", "*.xlsx *.xls"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:
                return

            # قراءة ملف Excel
            try:
                df = pd.read_excel(file_path)
            except Exception as e:
                messagebox.showerror("خطأ", f"خطأ في قراءة ملف Excel:\n{str(e)}")
                return

            # التحقق من وجود الأعمدة المطلوبة
            required_columns = ['item_code', 'item_name', 'unit_of_measure']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                messagebox.showerror(
                    "خطأ في تنسيق الملف",
                    f"الأعمدة التالية مطلوبة ولكنها غير موجودة:\n{', '.join(missing_columns)}\n\n"
                    f"الأعمدة المطلوبة:\n"
                    f"- item_code (كود الصنف)\n"
                    f"- item_name (اسم الصنف)\n"
                    f"- unit_of_measure (وحدة القياس)\n\n"
                    f"الأعمدة الاختيارية:\n"
                    f"- category_name (اسم الفئة)\n"
                    f"- cost_price (سعر التكلفة)\n"
                    f"- selling_price (سعر البيع)\n"
                    f"- current_stock (المخزون الحالي)\n"
                    f"- min_stock_level (الحد الأدنى للمخزون)\n"
                    f"- description (الوصف)"
                )
                return

            # عرض معاينة البيانات
            preview_result = self.show_import_preview(df)
            if not preview_result:
                return

            # بدء عملية الاستيراد
            self.process_excel_import(df)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في استيراد البيانات:\n{str(e)}")

    def show_import_preview(self, df):
        """عرض معاينة البيانات قبل الاستيراد"""
        preview_window = tk.Toplevel(self.window)
        preview_window.title("معاينة البيانات - استيراد الأصناف")
        preview_window.geometry("800x600")
        preview_window.configure(bg=COLORS['background'])
        preview_window.transient(self.window)
        preview_window.grab_set()

        # توسيط النافذة
        preview_window.update_idletasks()
        x = (preview_window.winfo_screenwidth() // 2) - (800 // 2)
        y = (preview_window.winfo_screenheight() // 2) - (600 // 2)
        preview_window.geometry(f"800x600+{x}+{y}")

        # الإطار الرئيسي
        main_frame = tk.Frame(preview_window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # العنوان
        title_label = tk.Label(
            main_frame,
            text="معاينة البيانات المستوردة",
            font=(FONTS['title']['family'], FONTS['title']['size'], 'bold'),
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 10))

        # معلومات الملف
        info_frame = tk.Frame(main_frame, bg=COLORS['white'], relief='solid', bd=1)
        info_frame.pack(fill='x', pady=(0, 10))

        info_text = f"عدد الصفوف: {len(df)}\nعدد الأعمدة: {len(df.columns)}"
        info_label = tk.Label(
            info_frame,
            text=info_text,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['text_primary'],
            justify='right'
        )
        info_label.pack(pady=10)

        # جدول المعاينة
        table_frame = tk.Frame(main_frame, bg=COLORS['white'])
        table_frame.pack(fill='both', expand=True, pady=(0, 10))

        # إنشاء Treeview للمعاينة
        columns = list(df.columns)
        preview_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # تعريف العناوين
        for col in columns:
            preview_tree.heading(col, text=col)
            preview_tree.column(col, width=120, anchor='center')

        # إضافة البيانات (أول 50 صف فقط للمعاينة)
        preview_data = df.head(50)
        for index, row in preview_data.iterrows():
            values = [str(row[col]) if pd.notna(row[col]) else '' for col in columns]
            preview_tree.insert('', 'end', values=values)

        # شريط التمرير
        scrollbar_v = ttk.Scrollbar(table_frame, orient='vertical', command=preview_tree.yview)
        scrollbar_h = ttk.Scrollbar(table_frame, orient='horizontal', command=preview_tree.xview)
        preview_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)

        # تخطيط الجدول
        preview_tree.pack(side='left', fill='both', expand=True)
        scrollbar_v.pack(side='right', fill='y')
        scrollbar_h.pack(side='bottom', fill='x')

        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg=COLORS['background'])
        buttons_frame.pack(fill='x', pady=(10, 0))

        # متغير للنتيجة
        result = {'confirmed': False}

        def confirm_import():
            result['confirmed'] = True
            preview_window.destroy()

        def cancel_import():
            result['confirmed'] = False
            preview_window.destroy()

        # أزرار التأكيد والإلغاء
        confirm_button = tk.Button(
            buttons_frame,
            text="✅ تأكيد الاستيراد",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['success'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=confirm_import
        )
        confirm_button.pack(side='left', padx=5)

        cancel_button = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['danger'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=cancel_import
        )
        cancel_button.pack(side='left', padx=5)

        # انتظار إغلاق النافذة
        preview_window.wait_window()

        return result['confirmed']

    def process_excel_import(self, df):
        """معالجة استيراد البيانات من Excel"""
        try:
            conn = self.db_manager.get_connection()
            cursor = conn.cursor()

            # إحصائيات الاستيراد
            total_rows = len(df)
            imported_count = 0
            updated_count = 0
            error_count = 0
            errors = []

            # إنشاء نافذة التقدم
            progress_window = tk.Toplevel(self.window)
            progress_window.title("جاري الاستيراد...")
            progress_window.geometry("400x200")
            progress_window.configure(bg=COLORS['background'])
            progress_window.transient(self.window)
            progress_window.grab_set()

            # توسيط النافذة
            progress_window.update_idletasks()
            x = (progress_window.winfo_screenwidth() // 2) - (400 // 2)
            y = (progress_window.winfo_screenheight() // 2) - (200 // 2)
            progress_window.geometry(f"400x200+{x}+{y}")

            # عناصر نافذة التقدم
            progress_frame = tk.Frame(progress_window, bg=COLORS['background'])
            progress_frame.pack(fill='both', expand=True, padx=20, pady=20)

            progress_label = tk.Label(
                progress_frame,
                text="جاري استيراد البيانات...",
                font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
                bg=COLORS['background'],
                fg=COLORS['text_primary']
            )
            progress_label.pack(pady=(0, 10))

            progress_bar = ttk.Progressbar(
                progress_frame,
                length=300,
                mode='determinate',
                maximum=total_rows
            )
            progress_bar.pack(pady=(0, 10))

            status_label = tk.Label(
                progress_frame,
                text="",
                font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
                bg=COLORS['background'],
                fg=COLORS['text_primary']
            )
            status_label.pack()

            progress_window.update()

            # معالجة كل صف
            for index, row in df.iterrows():
                try:
                    # تحديث شريط التقدم
                    progress_bar['value'] = index + 1
                    status_label.config(text=f"معالجة الصف {index + 1} من {total_rows}")
                    progress_window.update()

                    # استخراج البيانات
                    item_code = str(row['item_code']).strip() if pd.notna(row['item_code']) else ''
                    item_name = str(row['item_name']).strip() if pd.notna(row['item_name']) else ''
                    unit_of_measure = str(row['unit_of_measure']).strip() if pd.notna(row['unit_of_measure']) else ''

                    # التحقق من البيانات المطلوبة
                    if not item_code or not item_name or not unit_of_measure:
                        error_count += 1
                        errors.append(f"الصف {index + 1}: بيانات مطلوبة مفقودة")
                        continue

                    # البيانات الاختيارية
                    category_name = str(row['category_name']).strip() if 'category_name' in row and pd.notna(row['category_name']) else None
                    cost_price = float(row['cost_price']) if 'cost_price' in row and pd.notna(row['cost_price']) else 0.0
                    selling_price = float(row['selling_price']) if 'selling_price' in row and pd.notna(row['selling_price']) else 0.0
                    current_stock = float(row['current_stock']) if 'current_stock' in row and pd.notna(row['current_stock']) else 0.0
                    min_stock_level = float(row['min_stock_level']) if 'min_stock_level' in row and pd.notna(row['min_stock_level']) else 0.0
                    description = str(row['description']).strip() if 'description' in row and pd.notna(row['description']) else ''

                    # البحث عن الفئة إذا كانت محددة
                    category_id = None
                    if category_name:
                        cursor.execute("SELECT id FROM item_categories WHERE category_name = ?", (category_name,))
                        category_result = cursor.fetchone()
                        if category_result:
                            category_id = category_result[0]
                        else:
                            # إنشاء فئة جديدة
                            cursor.execute(
                                "INSERT INTO item_categories (category_name, is_active) VALUES (?, 1)",
                                (category_name,)
                            )
                            category_id = cursor.lastrowid

                    # التحقق من وجود الصنف
                    cursor.execute("SELECT id FROM items WHERE item_code = ?", (item_code,))
                    existing_item = cursor.fetchone()

                    if existing_item:
                        # تحديث الصنف الموجود
                        cursor.execute("""
                            UPDATE items SET
                                item_name = ?,
                                category_id = ?,
                                unit_of_measure = ?,
                                cost_price = ?,
                                selling_price = ?,
                                current_stock = ?,
                                min_stock_level = ?,
                                description = ?,
                                updated_at = ?
                            WHERE item_code = ?
                        """, (
                            item_name, category_id, unit_of_measure,
                            cost_price, selling_price, current_stock, min_stock_level,
                            description, datetime.now(), item_code
                        ))
                        updated_count += 1
                    else:
                        # إضافة صنف جديد
                        cursor.execute("""
                            INSERT INTO items (
                                item_code, item_name, category_id, unit_of_measure,
                                cost_price, selling_price, current_stock, min_stock_level,
                                description, is_active, created_at, updated_at
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?)
                        """, (
                            item_code, item_name, category_id, unit_of_measure,
                            cost_price, selling_price, current_stock, min_stock_level,
                            description, datetime.now(), datetime.now()
                        ))
                        imported_count += 1

                except Exception as e:
                    error_count += 1
                    errors.append(f"الصف {index + 1}: {str(e)}")
                    continue

            # حفظ التغييرات
            conn.commit()
            conn.close()

            # إغلاق نافذة التقدم
            progress_window.destroy()

            # عرض نتائج الاستيراد
            self.show_import_results(total_rows, imported_count, updated_count, error_count, errors)

            # تحديث الجدول
            self.load_items()

        except Exception as e:
            if 'progress_window' in locals():
                progress_window.destroy()
            messagebox.showerror("خطأ", f"حدث خطأ في معالجة الاستيراد:\n{str(e)}")

    def show_import_results(self, total_rows, imported_count, updated_count, error_count, errors):
        """عرض نتائج الاستيراد"""
        results_window = tk.Toplevel(self.window)
        results_window.title("نتائج الاستيراد")
        results_window.geometry("600x500")
        results_window.configure(bg=COLORS['background'])
        results_window.transient(self.window)
        results_window.grab_set()

        # توسيط النافذة
        results_window.update_idletasks()
        x = (results_window.winfo_screenwidth() // 2) - (600 // 2)
        y = (results_window.winfo_screenheight() // 2) - (500 // 2)
        results_window.geometry(f"600x500+{x}+{y}")

        # الإطار الرئيسي
        main_frame = tk.Frame(results_window, bg=COLORS['background'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # العنوان
        title_label = tk.Label(
            main_frame,
            text="نتائج استيراد الأصناف",
            font=(FONTS['title']['family'], FONTS['title']['size'], 'bold'),
            bg=COLORS['background'],
            fg=COLORS['primary']
        )
        title_label.pack(pady=(0, 20))

        # إطار الإحصائيات
        stats_frame = tk.Frame(main_frame, bg=COLORS['white'], relief='solid', bd=1)
        stats_frame.pack(fill='x', pady=(0, 20))

        # الإحصائيات
        stats_text = f"""📊 إحصائيات الاستيراد:

📝 إجمالي الصفوف: {total_rows}
✅ أصناف جديدة: {imported_count}
🔄 أصناف محدثة: {updated_count}
❌ أخطاء: {error_count}
✔️ نجح: {imported_count + updated_count}"""

        stats_label = tk.Label(
            stats_frame,
            text=stats_text,
            font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
            bg=COLORS['white'],
            fg=COLORS['text_primary'],
            justify='right'
        )
        stats_label.pack(pady=15)

        # إطار الأخطاء (إذا وجدت)
        if errors:
            errors_label = tk.Label(
                main_frame,
                text="🚨 الأخطاء التي حدثت:",
                font=(FONTS['arabic']['family'], FONTS['arabic']['size'], 'bold'),
                bg=COLORS['background'],
                fg=COLORS['danger']
            )
            errors_label.pack(anchor='e', pady=(0, 10))

            # إطار قائمة الأخطاء
            errors_frame = tk.Frame(main_frame, bg=COLORS['white'])
            errors_frame.pack(fill='both', expand=True, pady=(0, 20))

            # قائمة الأخطاء مع شريط تمرير
            errors_text = tk.Text(
                errors_frame,
                font=(FONTS['arabic']['family'], FONTS['arabic']['size']),
                bg=COLORS['white'],
                fg=COLORS['text_primary'],
                wrap='word',
                height=10
            )

            scrollbar = ttk.Scrollbar(errors_frame, orient='vertical', command=errors_text.yview)
            errors_text.configure(yscrollcommand=scrollbar.set)

            # إضافة الأخطاء
            for i, error in enumerate(errors, 1):
                errors_text.insert('end', f"{i}. {error}\n")

            errors_text.config(state='disabled')

            # تخطيط قائمة الأخطاء
            errors_text.pack(side='left', fill='both', expand=True)
            scrollbar.pack(side='right', fill='y')

        # زر الإغلاق
        close_button = tk.Button(
            main_frame,
            text="✅ إغلاق",
            font=(FONTS['button']['family'], FONTS['button']['size']),
            bg=COLORS['primary'],
            fg=COLORS['white'],
            relief='flat',
            cursor='hand2',
            command=results_window.destroy
        )
        close_button.pack(pady=(10, 0))

    def create_excel_template(self):
        """إنشاء قالب Excel لاستيراد الأصناف"""
        if not PANDAS_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة pandas غير متوفرة\nيرجى تثبيتها باستخدام: pip install pandas")
            return

        if not OPENPYXL_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة openpyxl غير متوفرة\nيرجى تثبيتها باستخدام: pip install openpyxl")
            return

        try:
            # اختيار مكان حفظ القالب
            file_path = filedialog.asksaveasfilename(
                title="حفظ قالب Excel",
                defaultextension=".xlsx",
                filetypes=[
                    ("Excel files", "*.xlsx"),
                    ("All files", "*.*")
                ],
                initialname="قالب_استيراد_الأصناف.xlsx"
            )

            if not file_path:
                return

            # البيانات النموذجية
            sample_data = {
                'item_code': [
                    'ITM001',
                    'ITM002',
                    'ITM003'
                ],
                'item_name': [
                    'لابتوب ديل',
                    'ماوس لاسلكي',
                    'لوحة مفاتيح عربية'
                ],
                'unit_of_measure': [
                    'قطعة',
                    'قطعة',
                    'قطعة'
                ],
                'category_name': [
                    'أجهزة كمبيوتر',
                    'ملحقات كمبيوتر',
                    'ملحقات كمبيوتر'
                ],
                'cost_price': [
                    2500.00,
                    45.00,
                    120.00
                ],
                'selling_price': [
                    3000.00,
                    65.00,
                    150.00
                ],
                'current_stock': [
                    10,
                    25,
                    15
                ],
                'min_stock_level': [
                    5,
                    10,
                    5
                ],
                'description': [
                    'لابتوب ديل انسبايرون 15',
                    'ماوس لاسلكي بتقنية البلوتوث',
                    'لوحة مفاتيح عربية/إنجليزية'
                ]
            }

            # إنشاء DataFrame
            df = pd.DataFrame(sample_data)

            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # ورقة البيانات النموذجية
                df.to_excel(writer, sheet_name='البيانات النموذجية', index=False)

                # ورقة فارغة للاستخدام
                empty_df = pd.DataFrame(columns=df.columns)
                empty_df.to_excel(writer, sheet_name='قالب فارغ', index=False)

                # ورقة التعليمات
                instructions = pd.DataFrame({
                    'العمود': [
                        'item_code',
                        'item_name',
                        'unit_of_measure',
                        'category_name',
                        'cost_price',
                        'selling_price',
                        'current_stock',
                        'min_stock_level',
                        'description'
                    ],
                    'الوصف': [
                        'كود الصنف (مطلوب - يجب أن يكون فريد)',
                        'اسم الصنف (مطلوب)',
                        'وحدة القياس (مطلوب)',
                        'اسم الفئة (اختياري)',
                        'سعر التكلفة (اختياري)',
                        'سعر البيع (اختياري)',
                        'المخزون الحالي (اختياري)',
                        'الحد الأدنى للمخزون (اختياري)',
                        'وصف الصنف (اختياري)'
                    ],
                    'مطلوب': [
                        'نعم',
                        'نعم',
                        'نعم',
                        'لا',
                        'لا',
                        'لا',
                        'لا',
                        'لا',
                        'لا'
                    ]
                })
                instructions.to_excel(writer, sheet_name='التعليمات', index=False)

                # تنسيق الأوراق
                for sheet_name in writer.sheets:
                    worksheet = writer.sheets[sheet_name]

                    # تعديل عرض الأعمدة
                    for column in worksheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter
                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass
                        adjusted_width = min(max_length + 2, 50)
                        worksheet.column_dimensions[column_letter].width = adjusted_width

            messagebox.showinfo(
                "نجح",
                f"تم إنشاء قالب Excel بنجاح!\n\n"
                f"📁 المسار: {file_path}\n\n"
                f"📋 يحتوي القالب على:\n"
                f"• ورقة بيانات نموذجية\n"
                f"• ورقة فارغة للاستخدام\n"
                f"• ورقة تعليمات مفصلة\n\n"
                f"💡 استخدم ورقة 'قالب فارغ' لإدخال بياناتك"
            )

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء القالب:\n{str(e)}")


if __name__ == "__main__":
    # تشغيل نافذة الأصناف للاختبار
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    items_window = ItemsWindow()
    items_window.window.mainloop()
