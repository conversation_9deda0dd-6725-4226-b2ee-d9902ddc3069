# نظام متابعة شحنات الموردين - النسخة التنفيذية
# Ship Management System - Executable Distribution

## 🎯 نظرة عامة | Overview

هذه النسخة التنفيذية من نظام متابعة شحنات الموردين تحتوي على جميع المكونات المطلوبة للتشغيل على أي جهاز يعمل بنظام Windows بدون الحاجة لتثبيت Python أو أي مكتبات إضافية.

This executable version of the Ship Management System contains all required components to run on any Windows machine without needing to install Python or additional libraries.

## 📦 محتويات الحزمة | Package Contents

```
📦 ShipManagementSystem_Distribution/
├── 🚀 ShipManagementSystem.exe          # الملف التنفيذي الرئيسي
├── 🔧 تشغيل_البرنامج.bat               # ملف تشغيل مساعد
├── 📁 database/                         # مجلد قاعدة البيانات
│   ├── shipment_system.db              # قاعدة البيانات الرئيسية
│   └── database_manager.py             # مدير قاعدة البيانات
├── 📁 docs/                            # مجلد الوثائق
├── 📄 README.md                        # معلومات المشروع الأصلي
├── 📄 دليل_المستخدم.md                 # دليل المستخدم
├── 📄 تعليمات_التشغيل.txt              # تعليمات التشغيل الأصلية
├── 📄 تعليمات_تشغيل_الملف_التنفيذي.txt  # تعليمات الملف التنفيذي
├── 📄 requirements.txt                 # قائمة المتطلبات (للمرجع)
└── 📄 README_DISTRIBUTION.md           # هذا الملف
```

## 🚀 التشغيل السريع | Quick Start

### الطريقة الأولى | Method 1:
1. انقر نقراً مزدوجاً على `ShipManagementSystem.exe`
2. انتظر تحميل البرنامج (10-30 ثانية)
3. استخدم بيانات الدخول الافتراضية:
   - **اسم المستخدم:** admin
   - **كلمة المرور:** admin

### الطريقة الثانية | Method 2:
1. انقر نقراً مزدوجاً على `تشغيل_البرنامج.bat`
2. سيتم تشغيل البرنامج تلقائياً

## 💻 متطلبات النظام | System Requirements

- **نظام التشغيل:** Windows 10 أو أحدث
- **الذاكرة:** 4 GB RAM (8 GB مُوصى به)
- **مساحة القرص:** 200 MB مساحة فارغة
- **دقة الشاشة:** 1024x768 (1920x1080 مُوصى به)

## 🔧 المميزات | Features

✅ **تشغيل مستقل:** لا يحتاج Python أو مكتبات إضافية  
✅ **دعم كامل للعربية:** واجهة RTL مع دعم الخطوط العربية  
✅ **قاعدة بيانات محلية:** جميع البيانات محفوظة محلياً  
✅ **لا يحتاج إنترنت:** يعمل بدون اتصال بالإنترنت  
✅ **سهولة النقل:** يمكن نسخه إلى أي جهاز Windows  

## 🛠️ حل المشاكل | Troubleshooting

### البرنامج لا يبدأ | Program Won't Start
- تشغيل كمدير (Run as Administrator)
- إضافة استثناء في مكافح الفيروسات
- التأكد من وجود جميع الملفات

### خطأ قاعدة البيانات | Database Error
- التأكد من وجود مجلد `database`
- التأكد من صلاحيات الكتابة في المجلد
- تشغيل البرنامج كمدير

### البرنامج بطيء | Program is Slow
- طبيعي في المرة الأولى
- إضافة استثناء في مكافح الفيروسات
- إغلاق البرامج غير الضرورية

## 🔄 النسخ الاحتياطي | Backup

⚠️ **مهم جداً:** قم بعمل نسخة احتياطية من مجلد `database` بانتظام

```bash
# انسخ مجلد database إلى مكان آمن
copy database backup_folder/database_YYYY-MM-DD
```

## 📋 معلومات تقنية | Technical Information

- **حجم الملف:** ~89 MB
- **إصدار Python:** 3.13.5
- **أداة البناء:** PyInstaller 6.14.1
- **تاريخ البناء:** 2025-07-01
- **نوع التطبيق:** Windows GUI Application

## 🔐 الأمان | Security

- جميع البيانات محفوظة محلياً
- لا يتم إرسال أي بيانات عبر الإنترنت
- يُنصح بتغيير كلمة المرور الافتراضية
- عمل نسخ احتياطية منتظمة

## 📞 الدعم | Support

للحصول على المساعدة:
1. راجع `تعليمات_تشغيل_الملف_التنفيذي.txt`
2. راجع `دليل_المستخدم.md`
3. راجع ملفات الوثائق في مجلد `docs/`

## 📝 ملاحظات | Notes

- هذا الملف التنفيذي تم إنشاؤه من الكود المصدري الأصلي
- يحتوي على جميع المكتبات والتبعيات المطلوبة
- يمكن توزيعه بحرية على أجهزة Windows
- لا يحتاج تثبيت أو إعداد إضافي

## 🏷️ الإصدار | Version

**الإصدار:** 1.0.0  
**تاريخ البناء:** 2025-07-01  
**نوع البناء:** Standalone Executable  

---

**تم إنشاء هذا التوزيع بواسطة PyInstaller**  
**This distribution was created using PyInstaller**
